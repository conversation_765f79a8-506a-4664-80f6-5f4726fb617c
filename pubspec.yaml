name: tadars
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 3.0.8+128 #android
# version: 6.2.0+1 #ios

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  get: ^4.6.6
  dio: ^5.2.0
  archive: ^4.0.7
  audio_session: ^0.1.18
  cached_network_image: ^3.2.3
  connectivity_plus: ^6.0.5
  jiffy: ^6.2.1
  dio_smart_retry: ^7.0.1
  hive: ^2.2.3
  wakelock_plus: ^1.1.4
  hive_flutter: ^1.1.0
  numberpicker: ^2.1.2
  screenshot: ^3.0.0
  share_plus: ^11.0.0
  flutter_local_notifications: ^19.0.0
  # flutter_native_timezone: ^2.0.0
  percent_indicator: ^4.2.3
  retrofit: ^4.0.1
  just_audio: ^0.9.40
  sqlbrite: ^2.6.0
  loading_animation_widget: ^1.2.0+4
  url_launcher: ^6.2.3
  flutter_svg: ^2.0.9
  flutter_slidable: ^4.0.0
  shimmer: ^3.0.0
  flutter_switch: ^0.3.2
  flex_color_picker: ^3.3.1
  youtube_player_flutter: ^9.1.1
  html: ^0.15.3
  numerus: ^2.0.0
  flutter_layout_grid: ^2.0.3
  chewie: ^1.5.0
  chewie_audio: ^1.5.0
  webview_flutter: ^4.4.4
  material_color_generator: ^1.1.0
  carousel_slider: ^5.0.0
  flutter_sticky_header: ^0.7.0
  package_info_plus: ^8.0.2
  device_info_plus: ^11.2.1
  path_provider: ^2.0.15
  flutter_timezone: ^4.0.0
  flutter_math_fork: '>=0.4.2+1 <1.0.0'
  popover: ^0.3.0+1
  sticky_headers: ^0.3.0+2
  sticky_and_expandable_list: ^1.1.2
  scroll_to_index: ^3.0.1
  image_picker: ^1.0.4
  firebase_core: ^3.11.0
  firebase_crashlytics: ^4.3.2
  firebase_analytics: ^11.4.2
  advanced_in_app_review: ^1.3.0
  firebase_messaging: ^15.2.2
  quran_core:
     path: ../packages/quran_core/
    # git:
    #    url: https://<EMAIL>/aymanalareqi/quran_core.git
    #    ref: main
  intl: ^0.20.1
  collection: ^1.18.0
  animated_custom_dropdown: ^3.1.1
  flutter_overlay_window: ^0.5.0
  screen_state: ^4.1.1
  shared_preferences: ^2.5.2
  pretty_dio_logger: ^1.4.0
  audio_service: ^0.18.15
  path: ^1.9.0
  timezone: ^0.10.1
  csslib: ^1.0.2
  # device_preview: ^1.2.0

dev_dependencies:
  build_runner: null
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  hive_generator:
  retrofit_generator:
  flutter_launcher_icons: ^0.14.0
flutter_icons:
  android: true
  ios: true
  image_path: "assets/launcher/launcher.png"
  # adaptive_icon_foreground: "assets/launcher/foreground.png"
  # adaptive_icon_background: "#998C60"
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/boxes/
    - assets/db/
    # - assets/images/quran_pages/
    - assets/images/
    - assets/svgs/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  fonts:
    - family: Samim
      fonts:
        - asset: assets/fonts/samim/Samim-WOL.ttf
        - asset: assets/fonts/samim/Samim-Medium-WOL.ttf
        - asset: assets/fonts/samim/Samim-Bold-WOL.ttf
    - family: Cocon
      fonts:
        - asset: "assets/fonts/cocon/cocon.ttf"
    - family: Hafs
      fonts:
        - asset: "assets/fonts/hafs/hafs2.ttf"
    - family: Amiri
      fonts:
        - asset: "assets/fonts/amiri/amiri.ttf"
    - family: Kitab
      fonts:
        - asset: "assets/fonts/kitab/kitab.ttf"
    - family: Cairo
      fonts:
        - asset: "assets/fonts/cairo/Cairo-Black.ttf"
          weight: 900
        - asset: "assets/fonts/cairo/Cairo-Bold.ttf"
          weight: 700
        - asset: "assets/fonts/cairo/Cairo-SemiBold.ttf"
          weight: 400
        - asset: "assets/fonts/cairo/Cairo-Light.ttf"
    - family: NumberFont
      fonts:
        - asset: assets/fonts/numbers_font.ttf
    # - family: QuranUIIcons
    #   fonts:
    #     - asset: assets/fonts/icons_font/quran_icons.ttf
    - family: QuranUIIcons
      fonts:
        - asset: assets/fonts/icons_font/quran_ui_icons.ttf
    - family: QuranUIIcons2
      fonts:
        - asset: assets/fonts/icons_font/quran_ui_icons2.ttf
    - family: QuranSuarIcons
      fonts:
        - asset: assets/fonts/icons_font/quran_suar_icons.ttf
    - family: adwaa-alsalaf
      fonts:
        - asset: "assets/fonts/adwaa-alsalaf/adwaa-alsalaf-regular.ttf"
        - asset: "assets/fonts/adwaa-alsalaf/adwaa-alsalaf-bold.ttf"
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
