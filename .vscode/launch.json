{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "tadars",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tadars (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "tadars (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "quran_core",
            "cwd": "packages\\quran_core",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "quran_core (profile mode)",
            "cwd": "packages\\quran_core",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "quran_core (release mode)",
            "cwd": "packages\\quran_core",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}