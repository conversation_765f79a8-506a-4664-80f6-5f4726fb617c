import 'dart:io';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/custom_http_overrides.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';
import 'package:tadars/helpers/boxes_helper.dart';

import 'package:tadars/quran_app.dart';
import 'package:tadars/services/notification_service.dart';
import 'package:tadars/utils/constants/boxes.dart';

import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:audio_service/audio_service.dart';

// overlay entry point

@pragma("vm:entry-point")
void overlayMain() async {

  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await Hive.openBox(Boxes.settings);

  await QuranDatabaseProvider.init(2);
  // await QuranProvider.loadPagesFonts();
  await QuranReciterDatabaseProvider.init(
    path: "assets/db/reciters.db",
    version: 4,
  );

  var reciterId = BoxesHelper.getSettingReciterId();

  await QuranAudioService.init(
    defaultReciterId: reciterId,
    defualtLoopCount: 1,
    defaultSurahNumber: 1,
    audioServiceConfig: AudioServiceConfig(
      androidNotificationChannelId: 'com.tadarose_quran.channel.audio',
      androidNotificationChannelName: 'تدارس القرآن',
      androidNotificationOngoing: true,
    ),
  );

  runApp(
    const MaterialApp(
      debugShowCheckedModeBanner: false,
      locale: Locale('ar'),
      home: IntroVersewidget(),
    ),
  );
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  HttpOverrides.global = CustomHttpOverrides();

  await Hive.initFlutter();
  await Hive.openBox(Boxes.settings);
  // SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [
  //   // SystemUiOverlay.top,
  // ]);
  NotificationService().configureLocalTimeZone();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeRight,
    DeviceOrientation.landscapeLeft,
  ]);

  runApp(const QuranApp());
}
