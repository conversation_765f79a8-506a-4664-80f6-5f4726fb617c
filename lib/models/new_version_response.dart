class NewVersionResponse {
  bool hasNewVersion;
  String newVersion;
  String oldVersion;
  String updateMsg;

  NewVersionResponse({
    required this.hasNewVersion,
    required this.newVersion,
    required this.oldVersion,
    required this.updateMsg,
  });

  factory NewVersionResponse.fromJson(Map<String, dynamic> json) =>
      NewVersionResponse(
        hasNewVersion: json["has_new_version"],
        newVersion: json["newVersion"],
        oldVersion: json["oldVersion"],
        updateMsg: json["updateMsg"],
      );

  Map<String, dynamic> toJson() => {
        "has_new_version": hasNewVersion,
        "newVersion": newVersion,
        "oldVersion": oldVersion,
        "updateMsg": updateMsg,
      };
}
