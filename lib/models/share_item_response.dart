class ShareItemResponse {
  ShareItemResponse({
    this.itemId,
    this.type,
    this.shares,
  });

  String? itemId;
  String? type;
  String? shares;

  factory ShareItemResponse.fromJson(Map<String, dynamic> json) =>
      ShareItemResponse(
        itemId: json["itemId"].toString(),
        type: json["type"].toString(),
        shares: json["shares"].toString(),
      );

  Map<String, dynamic> toJson() => {
        "itemId": itemId,
        "type": type,
        "shares": shares,
      };
}
