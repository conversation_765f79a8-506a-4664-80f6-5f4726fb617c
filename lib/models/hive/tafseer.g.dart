// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tafseer.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TafseerAdapter extends TypeAdapter<Tafseer> {
  @override
  final int typeId = 10;

  @override
  Tafseer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Tafseer(
      id: fields[0] as int,
      suraNumber: fields[1] as int,
      verseNumber: fields[2] as int,
      text: fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Tafseer obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.suraNumber)
      ..writeByte(2)
      ..write(obj.verseNumber)
      ..writeByte(3)
      ..write(obj.text);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TafseerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
