import 'package:hive/hive.dart';

part 'note.g.dart';

@HiveType(typeId: 8)
class Note extends HiveObject {
  @HiveField(0)
  int suraNumber;
  @HiveField(1)
  int verseNumber;
  @HiveField(2)
  int pageNumber;
  @HiveField(3)
  String note;
  @HiveField(4)
  DateTime createdAt;
  Note(
      {required this.suraNumber,
      required this.note,
      required this.createdAt,
      required this.verseNumber,
      required this.pageNumber});

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};

    data['sura_number'] = suraNumber;
    data['verse_number'] = verseNumber;
    data['note'] = note;
    data['created_at'] = createdAt.toIso8601String();

    return data;
  }
}
