import 'package:hive/hive.dart';

part 'quran_tafseer.g.dart';

@HiveType(typeId: 11)
class QuranTafseer extends HiveObject {
  // "id": 1,
  //       "title": "تفسير البغوي",
  //       "name": "baghawy",
  //       "author": "الحسين بن مسعود البغوي",
  //       "ord": null,
  //       "status": 1,
  //       "downloaded": 0,
  //       "deletable": 1,
  //       "size": "2.3",
  //       "created_at": "0000-00-00 00:00:00",
  //       "updated_at": "2019-02-21 22:42:17"
  @HiveField(0)
  int id;
  @HiveField(1)
  String title;
  @HiveField(2)
  String name;
  @HiveField(3)
  String author;
  @HiveField(4)
  int? order;
  @HiveField(5, defaultValue: 1)
  int status;
  @HiveField(6)
  bool downloaded;
  @HiveField(7)
  bool deletable;
  @HiveField(8)
  double size;
  @HiveField(9)
  String createdAt;
  @HiveField(10)
  String updatedAt;
  // constructor
  QuranTafseer({
    required this.id,
    required this.title,
    required this.name,
    required this.author,
    this.order,
    required this.status,
    required this.downloaded,
    required this.deletable,
    required this.size,
    required this.createdAt,
    required this.updatedAt,
  });
  // from json
  factory QuranTafseer.fromJson(Map<String, dynamic> json) {
    return QuranTafseer(
      id: json['id'] as int,
      title: json['title'] as String,
      name: json['name'] as String,
      author: json['author'] as String,
      order: json['order'] as int?,
      status: json['status'] as int,
      downloaded: json['downloaded'] as int == 1,
      deletable: json['deletable'] as int == 1,
      size: double.parse(json['size'].toString()),
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'name': name,
      'author': author,
      'order': order,
      'status': status,
      'downloaded': downloaded ? 1 : 0,
      'deletable': deletable ? 1 : 0,
      'size': size,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // fromJson
  // factory QuranTafseer.fromJson(Map<String, dynamic> json) => QuranTafseer(
  //       id: json["id"] as int,
  //       title: json["title"] as String,
  //       name: json["name"] as String,
  //       author: json["author"] as String,
  //       order: json["ord"] as int?,
  //       status: json["status"] as int,
  //       createdAt: json["created_at"] as String,
  //       updatedAt: json["updated_at"] as String,
  //       downloaded: json["downloaded"] as int == 1,
  //       deletable: json["deletable"] == 1 ? true : false,
  //       size: json["size"].touDoble(),
  //     );
}
