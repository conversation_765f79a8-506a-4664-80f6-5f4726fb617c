// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_verse.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrVerseAdapter extends TypeAdapter<TdbrVerse> {
  @override
  final int typeId = 16;

  @override
  TdbrVerse read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrVerse(
      id: fields[0] as int,
      suraNumber: fields[1] as int,
      verseNumber: fields[2] as int,
      verseId: fields[3] as int,
      tdbrType: fields[4] as String,
      tdbrId: fields[5] as int?,
      status: fields[6] as int,
      createdAt: fields[7] as String,
      updatedAt: fields[8] as String,
    )..tdbr = (fields[9] as HiveList?)?.castHiveList();
  }

  @override
  void write(BinaryWriter writer, TdbrVerse obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.suraNumber)
      ..writeByte(2)
      ..write(obj.verseNumber)
      ..writeByte(3)
      ..write(obj.verseId)
      ..writeByte(4)
      ..write(obj.tdbrType)
      ..writeByte(5)
      ..write(obj.tdbrId)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.tdbr);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrVerseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
