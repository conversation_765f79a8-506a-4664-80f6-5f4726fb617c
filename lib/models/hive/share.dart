import 'package:hive/hive.dart';

import 'user.dart';

part 'share.g.dart';

@HiveType(typeId: 15)
class Share {
  @HiveField(0)
  int id;
  @HiveField(1)
  bool share;
  @HiveField(2)
  String shareTable;
  @HiveField(3)
  int itemId;
  @HiveField(4)
  int status;
  @HiveField(5)
  int userId;
  @HiveField(6)
  String createdAt;
  @HiveField(7)
  String updatedAt;
  // links
  @HiveField(8)
  HiveList<User>? user;
  @HiveField(9)
  HiveList? item;
  // constructor
  Share({
    required this.id,
    required this.share,
    required this.shareTable,
    required this.itemId,
    required this.status,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
  });
  // fromJson
  factory Share.fromJson(Map<String, dynamic> json) => Share(
        id: json['id'] as int,
        share: json['share'] == 1 ? true : false,
        shareTable: json['share_table'] as String,
        itemId: json['item_id'] as int,
        status: json['status'] as int,
        userId: json['user_id'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'share': share ? 1 : 0,
        'share_table': shareTable,
        'item_id': itemId,
        'status': status,
        'user_id': userId,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
