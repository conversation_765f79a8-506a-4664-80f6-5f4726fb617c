// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_timing.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranTimingAdapter extends TypeAdapter<QuranTiming> {
  @override
  final int typeId = 6;

  @override
  QuranTiming read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranTiming(
      endAt: fields[3] as double?,
      id: fields[0] as int,
      quranReciterId: fields[1] as int,
      startAt: fields[2] as double,
      suraNumber: fields[4] as int,
      verseNumber: fields[6] as int,
    );
  }

  @override
  void write(BinaryWriter writer, QuranTiming obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.quranReciterId)
      ..writeByte(2)
      ..write(obj.startAt)
      ..writeByte(3)
      ..write(obj.endAt)
      ..writeByte(4)
      ..write(obj.suraNumber)
      ..writeByte(6)
      ..write(obj.verseNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranTimingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
