// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_tadabor.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrTadaborAdapter extends TypeAdapter<TdbrTadabor> {
  @override
  final int typeId = 28;

  @override
  TdbrTadabor read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrTadabor(
      id: fields[0] as int,
      fullText: fields[1] as String,
      sourceId: fields[2] as int,
      status: fields[3] as int,
      userId: fields[4] as int,
      createdAt: fields[5] as String,
      updatedAt: fields[6] as String,
      verseNumber: fields[9] as int,
      suraNumber: fields[10] as int,
    )
      ..user = (fields[7] as HiveList?)?.castHiveList()
      ..source = fields[8] as int?;
  }

  @override
  void write(BinaryWriter writer, TdbrTadabor obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.fullText)
      ..writeByte(2)
      ..write(obj.sourceId)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.userId)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.user)
      ..writeByte(8)
      ..write(obj.source)
      ..writeByte(9)
      ..write(obj.verseNumber)
      ..writeByte(10)
      ..write(obj.suraNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrTadaborAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
