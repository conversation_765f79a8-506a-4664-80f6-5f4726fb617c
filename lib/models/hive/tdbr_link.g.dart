// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_link.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrLinkAdapter extends TypeAdapter<TdbrLink> {
  @override
  final int typeId = 20;

  @override
  TdbrLink read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrLink(
      id: fields[0] as int,
      tdbrType: fields[1] as String,
      tdbrId: fields[2] as int?,
      url: fields[3] as String,
      details: fields[4] as String?,
      startAt: fields[5] as String?,
      endAt: fields[6] as String?,
      status: fields[7] as int,
      createdAt: fields[8] as String,
      updatedAt: fields[9] as String,
      page: fields[12] as int?,
      type: fields[11] as String?,
    )..tdbr = (fields[10] as HiveList?)?.castHiveList();
  }

  @override
  void write(BinaryWriter writer, TdbrLink obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.tdbrType)
      ..writeByte(2)
      ..write(obj.tdbrId)
      ..writeByte(3)
      ..write(obj.url)
      ..writeByte(4)
      ..write(obj.details)
      ..writeByte(5)
      ..write(obj.startAt)
      ..writeByte(6)
      ..write(obj.endAt)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt)
      ..writeByte(10)
      ..write(obj.tdbr)
      ..writeByte(11)
      ..write(obj.type)
      ..writeByte(12)
      ..write(obj.page);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrLinkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
