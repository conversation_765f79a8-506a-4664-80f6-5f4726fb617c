// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_sura_header.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranSuraHeaderAdapter extends TypeAdapter<QuranSuraHeader> {
  @override
  final int typeId = 1;

  @override
  QuranSuraHeader read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranSuraHeader(
      height: fields[3] as double,
      id: fields[0] as int,
      pageNumber: fields[1] as int,
      width: fields[2] as double,
      x: fields[4] as double,
      y: fields[5] as double,
    );
  }

  @override
  void write(BinaryWriter writer, QuranSuraHeader obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.pageNumber)
      ..writeByte(2)
      ..write(obj.width)
      ..writeByte(3)
      ..write(obj.height)
      ..writeByte(4)
      ..write(obj.x)
      ..writeByte(5)
      ..write(obj.y);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranSuraHeaderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
