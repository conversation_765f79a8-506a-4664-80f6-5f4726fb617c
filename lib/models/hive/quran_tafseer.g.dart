// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_tafseer.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranTafseerAdapter extends TypeAdapter<QuranTafseer> {
  @override
  final int typeId = 11;

  @override
  QuranTafseer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranTafseer(
      id: fields[0] as int,
      title: fields[1] as String,
      name: fields[2] as String,
      author: fields[3] as String,
      order: fields[4] as int?,
      status: fields[5] == null ? 1 : fields[5] as int,
      downloaded: fields[6] as bool,
      deletable: fields[7] as bool,
      size: fields[8] as double,
      createdAt: fields[9] as String,
      updatedAt: fields[10] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuranTafseer obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.author)
      ..writeByte(4)
      ..write(obj.order)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.downloaded)
      ..writeByte(7)
      ..write(obj.deletable)
      ..writeByte(8)
      ..write(obj.size)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranTafseerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
