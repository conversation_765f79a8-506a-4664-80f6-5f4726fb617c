import 'package:hive/hive.dart';

part 'quran_page_line.g.dart';

@HiveType(typeId: 3)
class QuranPageLine extends HiveObject {
  QuranPageLine({
    required this.height,
    required this.id,
    required this.pageNumber,
    required this.suraNumber,
    required this.verseId,
    required this.verseNumber,
    required this.width,
    required this.x,
    required this.y,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  int pageNumber;
  @HiveField(2)
  int suraNumber;
  @HiveField(3)
  int verseId;
  @HiveField(4)
  int verseNumber;
  @HiveField(5)
  double width;
  @HiveField(6)
  double height;
  @HiveField(7)
  double x;
  @HiveField(8)
  double y;
}
