// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'share.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ShareAdapter extends TypeAdapter<Share> {
  @override
  final int typeId = 15;

  @override
  Share read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Share(
      id: fields[0] as int,
      share: fields[1] as bool,
      shareTable: fields[2] as String,
      itemId: fields[3] as int,
      status: fields[4] as int,
      userId: fields[5] as int,
      createdAt: fields[6] as String,
      updatedAt: fields[7] as String,
    )
      ..user = (fields[8] as HiveList?)?.castHiveList()
      ..item = (fields[9] as HiveList?)?.castHiveList();
  }

  @override
  void write(BinaryWriter writer, Share obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.share)
      ..writeByte(2)
      ..write(obj.shareTable)
      ..writeByte(3)
      ..write(obj.itemId)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.userId)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt)
      ..writeByte(8)
      ..write(obj.user)
      ..writeByte(9)
      ..write(obj.item);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShareAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
