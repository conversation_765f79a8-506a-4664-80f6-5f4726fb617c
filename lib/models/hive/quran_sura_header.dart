import 'package:hive/hive.dart';

part 'quran_sura_header.g.dart';

@HiveType(typeId: 1)
class QuranSuraHeader extends HiveObject {
  QuranSuraHeader({
    required this.height,
    required this.id,
    required this.pageNumber,
    required this.width,
    required this.x,
    required this.y,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  int pageNumber;
  @HiveField(2)
  double width;
  @HiveField(3)
  double height;
  @HiveField(4)
  double x;
  @HiveField(5)
  double y;
}
