// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_sura.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranSuraAdapter extends TypeAdapter<QuranSura> {
  @override
  final int typeId = 5;

  @override
  QuranSura read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranSura(
      id: fields[0] as int,
      isMakkiah: fields[3] as int,
      name: fields[1] as String,
      nameWithDiac: fields[2] as String,
      order: fields[4] as int,
      pageNumber: fields[5] as int,
      unicode: fields[6] as String,
      versesCount: fields[7] as int,
      nameEn: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuranSura obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.nameWithDiac)
      ..writeByte(3)
      ..write(obj.isMakkiah)
      ..writeByte(4)
      ..write(obj.order)
      ..writeByte(5)
      ..write(obj.pageNumber)
      ..writeByte(6)
      ..write(obj.unicode)
      ..writeByte(7)
      ..write(obj.versesCount)
      ..writeByte(8)
      ..write(obj.nameEn);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranSuraAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
