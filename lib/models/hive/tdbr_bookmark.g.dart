// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_bookmark.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrBookmarkAdapter extends TypeAdapter<TdbrBookmark> {
  @override
  final int typeId = 50;

  @override
  TdbrBookmark read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrBookmark(
      tdbrId: fields[0] as int,
      tdbrType: fields[1] as String,
      createdAt: fields[2] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, TdbrBookmark obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.tdbrId)
      ..writeByte(1)
      ..write(obj.tdbrType)
      ..writeByte(2)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrBookmarkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
