// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'source_bookmark.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SourceBookmarkAdapter extends TypeAdapter<SourceBookmark> {
  @override
  final int typeId = 51;

  @override
  SourceBookmark read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SourceBookmark(
      sourceId: fields[0] as int,
      createdAt: fields[1] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SourceBookmark obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.sourceId)
      ..writeByte(1)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SourceBookmarkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
