import 'package:hive/hive.dart';

part 'bookmark.g.dart';

@HiveType(typeId: 7)
class Bookmark extends HiveObject {
  @HiveField(0)
  final int id;
  @HiveField(1)
  final int pageNumber;
  @HiveField(2)
  final int? suraNumber;
  @HiveField(3)
  final int colorIndex;
  @HiveField(4)
  final int? verseNumber;
  @HiveField(5)
  final DateTime createdAt;
  @HiveField(6)
  int type;

  Bookmark({
    required this.pageNumber,
    required this.suraNumber,
    required this.colorIndex,
    required this.verseNumber,
    required this.createdAt,
    required this.type,
    required this.id,
  });

  Map<String, dynamic> toJson() {
    var data = <String, dynamic>{};
    data['page_number'] = pageNumber;

    if (suraNumber != null) {
      data['sura_number'] = suraNumber;
    }
    data['color_index'] = colorIndex;

    if (verseNumber != null) {
      data['verse_number'] = verseNumber;
    }
    data['created_at'] = createdAt.toIso8601String();
    data['type'] = type;

    // data['id'] = id;

    return data;
  }
}
