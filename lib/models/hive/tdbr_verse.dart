import 'package:hive/hive.dart';

part 'tdbr_verse.g.dart';

@HiveType(typeId: 16)
class TdbrVerse {
  @HiveField(0)
  int id;
  @HiveField(1)
  int suraNumber;
  @HiveField(2)
  int verseNumber;
  @HiveField(3)
  int verseId;
  @HiveField(4)
  String tdbrType;
  @HiveField(5)
  int? tdbrId;
  @HiveField(6)
  int status;
  @HiveField(7)
  String createdAt;
  @HiveField(8)
  String updatedAt;
  // links
  @HiveField(9)
  HiveList? tdbr;

  // constructor
  TdbrVerse({
    required this.id,
    required this.suraNumber,
    required this.verseNumber,
    required this.verseId,
    required this.tdbrType,
    required this.tdbrId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // fromJson
  factory TdbrVerse.fromJson(Map<String, dynamic> json) => TdbrVerse(
        id: json['id'] as int,
        suraNumber: json['sora_num'] as int,
        verseNumber: json['ayah_num'] as int,
        verseId: json['ayah_id'] as int,
        tdbrType: json['tdbr_type'] as String,
        tdbrId: json['tdbr_id'] as int?,
        status: json['status'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'sora_num': suraNumber,
        'ayah_num': verseNumber,
        'ayah_id': verseId,
        'tdbr_type': tdbrType,
        'tdbr_id': tdbrId,
        'status': status,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
