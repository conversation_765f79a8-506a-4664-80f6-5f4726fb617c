import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 29)
class User extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  bool showName;
  // constructor
  User({required this.id, required this.name, required this.showName});
  // fromJson
  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'] as int,
        name: json['name'] as String,
        showName: json['show_name'] == 1,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'show_name': showName ? 1 : 0,
      };
}
