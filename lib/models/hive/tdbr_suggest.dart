import 'package:hive/hive.dart';

import 'tdbr_source.dart';
import 'user.dart';

part 'tdbr_suggest.g.dart';

@HiveType(typeId: 26)
class TdbrSuggest extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String fullText;
  @HiveField(2)
  int sourceId;
  @HiveField(3)
  int status;
  @HiveField(4)
  int userId;
  @HiveField(5)
  String createdAt;
  @HiveField(6)
  String updatedAt;
  // links
  @HiveField(7)
  HiveList<User>? user;
  @HiveField(8)
  HiveList<TdbrSource>? source;
  // verse and sura numbers
  @HiveField(9)
  int verseNumber;
  @HiveField(10)
  int suraNumber;
  // constructor
  TdbrSuggest({
    required this.id,
    required this.fullText,
    required this.sourceId,
    required this.status,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    this.verseNumber = 0,
    this.suraNumber = 0,
  });
  // fromJson
  factory TdbrSuggest.fromJson(Map<String, dynamic> json) => TdbrSuggest(
        id: json['id'] as int,
        fullText: json['fulltext'] as String,
        sourceId: (json['source'] as int?) ?? 0,
        status: json['status'] as int,
        userId: json['user_id'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'fulltext': fullText,
        'source': sourceId,
        'status': status,
        'user_id': userId,
        'created_at': createdAt,
        'updated_at': updatedAt,
        // 'verse_number': verseNumber,
        // 'sura_number': suraNumber,
      };
}
