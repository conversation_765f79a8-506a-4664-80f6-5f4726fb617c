import 'package:hive/hive.dart';

part 'werd.g.dart';

@HiveType(typeId: 12)
class Werd extends HiveObject {
  @HiveField(0)
  int? id;
  @HiveField(1)
  late String name;
  @HiveField(2)
  late int type;
  @HiveField(3)
  late int startPage;
  @HiveField(4)
  late int endPage;
  @HiveField(5)
  late int perPage;
  @HiveField(6)
  late int currentPage;
  @HiveField(7)
  late DateTime startDate;
  @HiveField(8)
  late DateTime endDate;
  @HiveField(9)
  late bool isCompleted;
  @HiveField(10)
  late bool isActive;
  @HiveField(11)
  late bool withAlarm;
  @HiveField(12)
  DateTime? alarmTime;
  // constructor
  Werd({
    required this.id,
    required this.name,
    required this.type,
    required this.startPage,
    required this.endPage,
    required this.perPage,
    required this.currentPage,
    required this.startDate,
    required this.endDate,
    required this.isCompleted,
    required this.isActive,
    required this.withAlarm,
    required this.alarmTime,
  });
  // empty constructor
  Werd.empty()
      : isActive = true,
        isCompleted = false;
}
