// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_media.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrMediaAdapter extends TypeAdapter<TdbrMedia> {
  @override
  final int typeId = 21;

  @override
  TdbrMedia read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrMedia(
      id: fields[0] as int,
      url: fields[1] as String,
      title: fields[2] as String,
      details: fields[3] as String,
      sourceId: fields[4] as int,
      status: fields[5] as int,
      userId: fields[6] as int,
      startAt: fields[7] as String,
      endAt: fields[8] as String,
      createdAt: fields[9] as String,
      updatedAt: fields[10] as String,
      verseNumber: fields[13] as int,
      suraNumber: fields[14] as int,
    )
      ..user = (fields[11] as HiveList?)?.castHiveList()
      ..source = (fields[12] as HiveList?)?.castHiveList();
  }

  @override
  void write(BinaryWriter writer, TdbrMedia obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.url)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.details)
      ..writeByte(4)
      ..write(obj.sourceId)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.userId)
      ..writeByte(7)
      ..write(obj.startAt)
      ..writeByte(8)
      ..write(obj.endAt)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt)
      ..writeByte(11)
      ..write(obj.user)
      ..writeByte(12)
      ..write(obj.source)
      ..writeByte(13)
      ..write(obj.verseNumber)
      ..writeByte(14)
      ..write(obj.suraNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrMediaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
