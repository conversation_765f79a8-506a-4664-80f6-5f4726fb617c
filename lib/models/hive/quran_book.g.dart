// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_book.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranBookAdapter extends TypeAdapter<QuranBook> {
  @override
  final int typeId = 13;

  @override
  QuranBook read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranBook(
      id: fields[0] as int,
      title: fields[1] as String,
      detail: fields[2] as String,
      path: fields[3] as String,
      pagesCount: fields[4] as int,
      size: fields[5] as double,
      status: fields[6] as int,
      order: fields[7] as int,
      isDownloaded: fields[8] as bool,
      createdAt: fields[9] as String,
      updatedAt: fields[10] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuranBook obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.detail)
      ..writeByte(3)
      ..write(obj.path)
      ..writeByte(4)
      ..write(obj.pagesCount)
      ..writeByte(5)
      ..write(obj.size)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.order)
      ..writeByte(8)
      ..write(obj.isDownloaded)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranBookAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
