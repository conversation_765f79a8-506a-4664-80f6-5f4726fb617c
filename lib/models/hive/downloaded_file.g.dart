// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'downloaded_file.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DownloadedFileAdapter extends TypeAdapter<DownloadedFile> {
  @override
  final int typeId = 9;

  @override
  DownloadedFile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DownloadedFile(
      filePath: fields[0] as String,
    );
  }

  @override
  void write(BinaryWriter writer, DownloadedFile obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.filePath);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DownloadedFileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
