import 'package:hive/hive.dart';

part 'tdbr_tadabor_category.g.dart';

@HiveType(typeId: 27)
class TdbrTadaborCategory extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String createdAt;
  @HiveField(3)
  String updatedAt;
  // constructor
  TdbrTadaborCategory({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
  });
  // fromJson
  factory TdbrTadaborCategory.fromJson(Map<String, dynamic> json) =>
      TdbrTadaborCategory(
        id: json['id'] as int,
        name: json['name'] as String,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
