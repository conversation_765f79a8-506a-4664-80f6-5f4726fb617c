import 'package:hive/hive.dart';
part 'quran_sura.g.dart';

@HiveType(typeId: 5)
class QuranSura {
  QuranSura({
    required this.id,
    required this.isMakkiah,
    required this.name,
    required this.nameWithDiac,
    required this.order,
    required this.pageNumber,
    required this.unicode,
    required this.versesCount,
    required this.nameEn,
  });
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String nameWithDiac;
  @HiveField(3)
  int isMakkiah;
  @HiveField(4)
  int order;
  @HiveField(5)
  int pageNumber;
  @HiveField(6)
  String unicode;
  @HiveField(7)
  int versesCount;
  @HiveField(8)
  String nameEn;
}
