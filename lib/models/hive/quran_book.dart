import 'package:hive/hive.dart';

part 'quran_book.g.dart';

@HiveType(typeId: 13)
class QuranBook extends HiveObject {
  @HiveField(0)
  late int id;
  @HiveField(1)
  late String title;
  @HiveField(2)
  late String detail;
  @HiveField(3)
  late String path;
  @HiveField(4)
  late int pagesCount;
  @HiveField(5)
  late double size;
  @HiveField(6)
  late int status;
  @HiveField(7)
  late int order;
  @HiveField(8)
  late bool isDownloaded;
  @HiveField(9)
  late String createdAt;
  @HiveField(10)
  late String updatedAt;
  // constructor
  QuranBook({
    required this.id,
    required this.title,
    required this.detail,
    required this.path,
    required this.pagesCount,
    required this.size,
    required this.status,
    required this.order,
    required this.isDownloaded,
    required this.createdAt,
    required this.updatedAt,
  });
  // empty constructor
  QuranBook.empty();
  // toJson
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'detail': detail,
      'path': path,
      'pagesCount': pagesCount,
      'size': size,
      'status': status,
      'order': order,
      'isDownloaded': isDownloaded,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // fromJson
  static QuranBook fromJson(Map<String, dynamic> json) {
    return QuranBook(
      id: json['id'] as int,
      title: json['title'] as String,
      detail: json['details'] as String,
      path: json['path'] as String,
      pagesCount: json['pages'] as int,
      size: json['size'].toDouble(),
      status: json['status'] as int,
      order: json['ord'] as int,
      isDownloaded: json['downloaded'] == 1 ? true : false,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }
}
