import 'package:hive/hive.dart';

part 'tafseer.g.dart';

@HiveType(typeId: 10)
class Tafseer extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  int suraNumber;
  @HiveField(2)
  int verseNumber;
  @HiveField(3)
  String text;
  // constructor
  <PERSON><PERSON><PERSON>({
    required this.id,
    required this.suraNumber,
    required this.verseNumber,
    required this.text,
  });
  // fromJson
  factory Tafseer.fromJson(Map<String, dynamic> json) => Tafseer(
        id: json["id"] ?? (json["index"] ?? "0") as int,
        suraNumber: json["sura"] as int,
        verseNumber: json["aya"] as int,
        text: json["text"] as String,
      );
  // tojson
  Map<String, dynamic> toJson() => {
        "id": id,
        'sura': suraNumber,
        'aya': verseNumber,
        'text': text,
      };
}
