import 'package:hive/hive.dart';

part 'quran_timing.g.dart';

@HiveType(typeId: 6)
class QuranTiming {
  QuranTiming({
    required this.endAt,
    required this.id,
    required this.quranReciterId,
    required this.startAt,
    required this.suraNumber,
    // required this.verseId,
    required this.verseNumber,
  });
  @HiveField(0)
  int id;
  @HiveField(1)
  int quranReciterId;
  @HiveField(2)
  double startAt;
  @HiveField(3)
  double? endAt;
  @HiveField(4)
  int suraNumber;
  // @HiveField(5)
  // int verseId;
  @HiveField(6)
  int verseNumber;
  Map<String, dynamic> toJson() => {
        "id": id,
        "sura_number": suraNumber,
        "verse_number": verseNumber,
      };

  factory QuranTiming.fromJson(json) => QuranTiming(
      id: int.parse(json["id"].toString()),
      endAt: double.tryParse(json["end_at"].toString()),
      startAt: double.parse(json["start_at"].toString()),
      quranReciterId: json['reciter_id'],
      suraNumber: int.parse(json['sura_number'].toString()),
      // verseId: 0,
      verseNumber: int.parse(json["verse_number"].toString()));
}
