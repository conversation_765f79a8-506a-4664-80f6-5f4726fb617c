import 'package:hive/hive.dart';

part 'quran_reciter.g.dart';

@HiveType(typeId: 4)
class QuranReciter extends HiveObject {
  QuranReciter(
      {required this.enName,
      required this.filesUrl,
      required this.id,
      required this.name,
      required this.size,
      this.source,
      this.path,
      this.downloaded});
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String enName;
  @HiveField(3)
  String filesUrl;
  @HiveField(4)
  int size;
  String? source;
  String? path;
  bool? downloaded;

  factory QuranReciter.fromJson(json) => QuranReciter(
      enName: json["en_name"],
      name: json["name"],
      id: int.parse(json["id"].toString()),
      filesUrl: json["files_url"],
      size: 0,
      source: json["source"],
      path: json["timings_path"],
      downloaded: json['downloaded'] == 1 ? true : false);

  Map<String, dynamic> toJson() {
    return {
      "name": name,
      "files_url": filesUrl,
      "id": id,
    };
  }
}
