import 'package:hive/hive.dart';

import 'tdbr_source.dart';
import 'user.dart';

part 'tdbr_media.g.dart';

@HiveType(typeId: 21)
class TdbrMedia extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String url;
  @HiveField(2)
  String title;
  @HiveField(3)
  String details;
  @HiveField(4)
  int sourceId;
  @HiveField(5)
  int status;
  @HiveField(6)
  int userId;
  @HiveField(7)
  String startAt;
  @HiveField(8)
  String endAt;
  @HiveField(9)
  String createdAt;
  @HiveField(10)
  String updatedAt;
  // links
  @HiveField(11)
  HiveList<User>? user;
  @HiveField(12)
  HiveList<TdbrSource>? source;
  @HiveField(13)
  int verseNumber;
  @HiveField(14)
  int suraNumber;

  // constructor
  TdbrMedia({
    required this.id,
    required this.url,
    required this.title,
    required this.details,
    required this.sourceId,
    required this.status,
    required this.userId,
    required this.startAt,
    required this.endAt,
    required this.createdAt,
    required this.updatedAt,
    this.verseNumber = 0,
    this.suraNumber = 0,
  });
  // fromJson
  factory TdbrMedia.fromJson(Map<String, dynamic> json) => TdbrMedia(
        id: json['id'] as int,
        url: (json['url'] as String?) ?? '',
        title: json['title'] as String? ?? "",
        details: json['details'] as String,
        sourceId: (json['source'] as int?) ?? 0,
        status: json['status'] as int,
        userId: json['user_id'] as int,
        startAt: (json['start_at'] as String?) ?? '',
        endAt: (json['end_at'] as String?) ?? '',
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'url': url,
        'title': title,
        'details': details,
        'source': sourceId,
        'status': status,
        'user_id': userId,
        'start_at': startAt,
        'end_at': endAt,
        'created_at': createdAt,
        'updated_at': updatedAt,
        // 'verse_number': verseNumber,
        // 'sura_number': suraNumber,
      };
}
