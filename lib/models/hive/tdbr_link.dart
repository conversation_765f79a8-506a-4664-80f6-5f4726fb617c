import 'package:hive/hive.dart';

part 'tdbr_link.g.dart';

@HiveType(typeId: 20)
class TdbrLink extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String tdbrType;
  @HiveField(2)
  int? tdbrId;
  @HiveField(3)
  String url;
  @HiveField(4)
  String? details;
  @HiveField(5)
  String? startAt;
  @HiveField(6)
  String? endAt;
  @HiveField(7)
  int status;
  @HiveField(8)
  String createdAt;
  @HiveField(9)
  String updatedAt;
  // links
  @HiveField(10)
  HiveList? tdbr;

  @HiveField(11)
  String? type;
  @HiveField(12)
  int? page;
  // constructor
  TdbrLink(
      {required this.id,
      required this.tdbrType,
      required this.tdbrId,
      required this.url,
      required this.details,
      required this.startAt,
      required this.endAt,
      required this.status,
      required this.createdAt,
      required this.updatedAt,
      required this.page,
      required this.type});
  // fromJson
  factory TdbrLink.fromJson(Map<String, dynamic> json) => TdbrLink(
        id: json['id'] as int,
        tdbrType: json['tdbr_type'] as String,
        tdbrId: json['tdbr_id'] as int?,
        url: json['url'] as String,
        details: json['details'] as String?,
        startAt: json['start_at'] as String?,
        endAt: json['end_at'] as String?,
        status: json['status'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
        page: json['page'],
        type: json['type'],
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'tdbr_type': tdbrType,
        'tdbr_id': tdbrId,
        'url': url,
        'details': details,
        'start_at': startAt,
        'end_at': endAt,
        'status': status,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
