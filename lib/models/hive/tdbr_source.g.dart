// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tdbr_source.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TdbrSourceAdapter extends TypeAdapter<TdbrSource> {
  @override
  final int typeId = 25;

  @override
  TdbrSource read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TdbrSource(
      id: fields[0] as int,
      name: fields[1] as String,
      details: fields[2] as String?,
      categoryId: fields[3] as int,
      status: fields[4] as int,
      createdAt: fields[5] as String,
      updatedAt: fields[6] as String,
    )
      ..category = (fields[7] as HiveList?)?.castHiveList()
      ..tadabors = (fields[8] as HiveList?)?.castHiveList();
  }

  @override
  void write(BinaryWriter writer, TdbrSource obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.details)
      ..writeByte(3)
      ..write(obj.categoryId)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.tadabors);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TdbrSourceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
