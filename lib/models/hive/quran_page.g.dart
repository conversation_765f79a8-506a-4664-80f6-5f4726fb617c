// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_page.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranPageAdapter extends TypeAdapter<QuranPage> {
  @override
  final int typeId = 0;

  @override
  QuranPage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranPage(
      pageNumber: fields[0] as int,
      hizbNumber: fields[1] as int,
      partNumber: fields[2] as int,
      quarter: fields[3] as int,
      quarterNumber: fields[4] as int,
      suraNumber: fields[5] as int,
      suraName: fields[6] as String,
      versesCount: fields[7] as int,
      firstVerseNumber: fields[8] as int,
      firstSuraNumber: fields[9] as int,
      lastVerseNumber: fields[10] as int,
      lastSuraNumber: fields[11] as int,
      suarVersesCount: (fields[12] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, QuranPage obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.pageNumber)
      ..writeByte(1)
      ..write(obj.hizbNumber)
      ..writeByte(2)
      ..write(obj.partNumber)
      ..writeByte(3)
      ..write(obj.quarter)
      ..writeByte(4)
      ..write(obj.quarterNumber)
      ..writeByte(5)
      ..write(obj.suraNumber)
      ..writeByte(6)
      ..write(obj.suraName)
      ..writeByte(7)
      ..write(obj.versesCount)
      ..writeByte(8)
      ..write(obj.firstVerseNumber)
      ..writeByte(9)
      ..write(obj.firstSuraNumber)
      ..writeByte(10)
      ..write(obj.lastVerseNumber)
      ..writeByte(11)
      ..write(obj.lastSuraNumber)
      ..writeByte(12)
      ..write(obj.suarVersesCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranPageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
