import 'package:hive/hive.dart';

import 'user.dart';

part 'like.g.dart';

@HiveType(typeId: 14)
class Like {
  @HiveField(0)
  int id;
  @HiveField(1)
  int userId;
  @HiveField(2)
  bool like;
  @HiveField(3)
  String likeTable;
  @HiveField(4)
  int itemId;
  @HiveField(5)
  int status;
  @HiveField(6)
  String createdAt;
  @HiveField(7)
  String updatedAt;
  // links
  @HiveField(8)
  HiveList<User>? user;

  // constructor
  Like({
    required this.id,
    required this.userId,
    required this.like,
    required this.likeTable,
    required this.itemId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });
  // fromJson
  factory Like.fromJson(Map<String, dynamic> json) => Like(
        id: json['id'] as int,
        userId: json['user_id'] as int,
        like: json['like'] == 1 ? true : false,
        likeTable: json['like_table'] as String,
        itemId: json['item_id'] as int,
        status: json['status'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'user_id': userId,
        'like': like ? 1 : 0,
        'like_table': likeTable,
        'item_id': itemId,
        'status': status,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
