// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_reciter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranReciterAdapter extends TypeAdapter<QuranReciter> {
  @override
  final int typeId = 4;

  @override
  QuranReciter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranReciter(
      enName: fields[2] as String,
      filesUrl: fields[3] as String,
      id: fields[0] as int,
      name: fields[1] as String,
      size: fields[4] as int,
    );
  }

  @override
  void write(BinaryWriter writer, QuranReciter obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.enName)
      ..writeByte(3)
      ..write(obj.filesUrl)
      ..writeByte(4)
      ..write(obj.size);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranReciterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
