// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'werd.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WerdAdapter extends TypeAdapter<Werd> {
  @override
  final int typeId = 12;

  @override
  Werd read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Werd(
      id: fields[0] as int?,
      name: fields[1] as String,
      type: fields[2] as int,
      startPage: fields[3] as int,
      endPage: fields[4] as int,
      perPage: fields[5] as int,
      currentPage: fields[6] as int,
      startDate: fields[7] as DateTime,
      endDate: fields[8] as DateTime,
      isCompleted: fields[9] as bool,
      isActive: fields[10] as bool,
      withAlarm: fields[11] as bool,
      alarmTime: fields[12] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Werd obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.startPage)
      ..writeByte(4)
      ..write(obj.endPage)
      ..writeByte(5)
      ..write(obj.perPage)
      ..writeByte(6)
      ..write(obj.currentPage)
      ..writeByte(7)
      ..write(obj.startDate)
      ..writeByte(8)
      ..write(obj.endDate)
      ..writeByte(9)
      ..write(obj.isCompleted)
      ..writeByte(10)
      ..write(obj.isActive)
      ..writeByte(11)
      ..write(obj.withAlarm)
      ..writeByte(12)
      ..write(obj.alarmTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WerdAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
