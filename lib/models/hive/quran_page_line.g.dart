// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_page_line.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranPageLineAdapter extends TypeAdapter<QuranPageLine> {
  @override
  final int typeId = 3;

  @override
  QuranPageLine read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranPageLine(
      height: fields[6] as double,
      id: fields[0] as int,
      pageNumber: fields[1] as int,
      suraNumber: fields[2] as int,
      verseId: fields[3] as int,
      verseNumber: fields[4] as int,
      width: fields[5] as double,
      x: fields[7] as double,
      y: fields[8] as double,
    );
  }

  @override
  void write(BinaryWriter writer, QuranPageLine obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.pageNumber)
      ..writeByte(2)
      ..write(obj.suraNumber)
      ..writeByte(3)
      ..write(obj.verseId)
      ..writeByte(4)
      ..write(obj.verseNumber)
      ..writeByte(5)
      ..write(obj.width)
      ..writeByte(6)
      ..write(obj.height)
      ..writeByte(7)
      ..write(obj.x)
      ..writeByte(8)
      ..write(obj.y);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranPageLineAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
