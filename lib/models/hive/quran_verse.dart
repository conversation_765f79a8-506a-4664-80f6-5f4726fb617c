import 'package:hive/hive.dart';

part 'quran_verse.g.dart';

@HiveType(typeId: 2)
class QuranVerse extends HiveObject {
  QuranVerse({
    required this.id,
    required this.pageNumber,
    required this.partNumber,
    required this.suraNumber,
    required this.translation,
    required this.verseNumber,
    required this.verseWithDiac,
    required this.verseWithoutDiac,
  });
  @HiveField(0)
  int id;
  @HiveField(1)
  int pageNumber;
  @HiveField(2)
  int suraNumber;
  @HiveField(3)
  String translation;
  @HiveField(4)
  int verseNumber;
  @HiveField(5)
  String verseWithDiac;
  @HiveField(6)
  String verseWithoutDiac;
  @HiveField(7)
  int partNumber;
}
