import 'package:hive/hive.dart';

part 'quran_page.g.dart';

@HiveType(typeId: 0)
class QuranPage extends HiveObject {
  @HiveField(0)
  int pageNumber;
  @HiveField(1)
  int hizbNumber;
  @HiveField(2)
  int partNumber;
  @HiveField(3)
  int quarter;
  @HiveField(4)
  int quarterNumber;
  @HiveField(5)
  int suraNumber;
  @HiveField(6)
  String suraName;
  @HiveField(7)
  int versesCount;
  @HiveField(8)
  int firstVerseNumber;
  @HiveField(9)
  int firstSuraNumber;
  @HiveField(10)
  int lastVerseNumber;
  @HiveField(11)
  int lastSuraNumber;
  @HiveField(12)
  Map<String, int> suarVersesCount;

  // constructor
  QuranPage({
    required this.pageNumber,
    required this.hizbNumber,
    required this.partNumber,
    required this.quarter,
    required this.quarterNumber,
    required this.suraNumber,
    required this.suraName,
    required this.versesCount,
    required this.firstVerseNumber,
    required this.firstSuraNumber,
    required this.lastVerseNumber,
    required this.lastSuraNumber,
    required this.suarVersesCount,
  });
}
