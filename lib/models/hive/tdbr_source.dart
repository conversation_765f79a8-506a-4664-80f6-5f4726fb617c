import 'package:hive/hive.dart';

import 'tdbr_tadabor_category.dart';

part 'tdbr_source.g.dart';

@HiveType(typeId: 25)
class TdbrSource extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String? details;
  @HiveField(3)
  int categoryId;
  @HiveField(4)
  int status;
  @HiveField(5)
  String createdAt;
  @HiveField(6)
  String updatedAt;
  // links
  @HiveField(7)
  HiveList<TdbrTadaborCategory>? category;
  @HiveField(8)
  HiveList? tadabors;
  // constructor
  TdbrSource({
    required this.id,
    required this.name,
    this.details,
    required this.categoryId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });
  // fromJson
  factory TdbrSource.fromJson(Map<String, dynamic> json) => TdbrSource(
        id: json['id'] as int,
        name: json['name'] as String,
        details: json['details'] as String?,
        categoryId: json['cat'] as int,
        status: json['status'] as int,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
      );
  // toJson
  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'details': details,
        'cat': categoryId,
        'status': status,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
