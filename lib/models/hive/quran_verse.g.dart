// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_verse.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuranVerseAdapter extends TypeAdapter<QuranVerse> {
  @override
  final int typeId = 2;

  @override
  QuranVerse read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuranVerse(
      id: fields[0] as int,
      pageNumber: fields[1] as int,
      partNumber: fields[7] as int,
      suraNumber: fields[2] as int,
      translation: fields[3] as String,
      verseNumber: fields[4] as int,
      verseWithDiac: fields[5] as String,
      verseWithoutDiac: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuranVerse obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.pageNumber)
      ..writeByte(2)
      ..write(obj.suraNumber)
      ..writeByte(3)
      ..write(obj.translation)
      ..writeByte(4)
      ..write(obj.verseNumber)
      ..writeByte(5)
      ..write(obj.verseWithDiac)
      ..writeByte(6)
      ..write(obj.verseWithoutDiac)
      ..writeByte(7)
      ..write(obj.partNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuranVerseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
