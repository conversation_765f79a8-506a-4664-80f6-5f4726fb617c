class Sync {
  Sync({
    required this.id,
    required this.table,
    required this.rowId,
    required this.action,
    required this.userId,
    required this.createAt,
    required this.updateAt,
  });

  final int id;
  final String table;
  final int? rowId;
  final int action;
  final int userId;
  final String createAt;
  final String updateAt;

  factory Sync.fromJson(Map<String, dynamic> json) => Sync(
      id: json['id'],
      table: json['table'],
      rowId: json['row_id'],
      action: json['action'],
      userId: json['user_id'],
      createAt: json['created_at'],
      updateAt: json['updated_at']);

  Map<String, dynamic> toJson() => {
        "id": id,
        "table": table,
        "row_id": rowId,
        "action": action,
        "user_id": userId,
        "created_at": createAt,
        "updated_at": updateAt
      };
}
