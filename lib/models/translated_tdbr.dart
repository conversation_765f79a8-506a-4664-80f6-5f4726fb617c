import 'dart:convert';

class TranslatedTdbr {
    int? id;
    String? fulltext;
    int? source;
    int? status;
    int? userId;
    DateTime? createdAt;
    DateTime? updatedAt;
    int? tdbrRulesId;
    String? locale;

    TranslatedTdbr({
        this.id,
        this.fulltext,
        this.source,
        this.status,
        this.userId,
        this.createdAt,
        this.updatedAt,
        this.tdbrRulesId,
        this.locale,
    });

    factory TranslatedTdbr.fromRawJson(String str) => TranslatedTdbr.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory TranslatedTdbr.fromJson(Map<String, dynamic> json) => TranslatedTdbr(
        id: json["id"],
        fulltext: json["fulltext"],
        source: json["source"],
        status: json["status"],
        userId: json["user_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        tdbrRulesId: json["tdbr_rules_id"],
        locale: json["locale"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "fulltext": fulltext,
        "source": source,
        "status": status,
        "user_id": userId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "tdbr_rules_id": tdbrRulesId,
        "locale": locale,
    };
}
