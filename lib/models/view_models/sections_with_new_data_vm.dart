class SectionsWithNewDataVm {
  //'tdbr_ayat','tdbr_consider','tdbr_links','tdbr_media','tdbr_pray','tdbr_questions','tdbr_rules','tdbr_sources','tdbr_suggest','tdbr_tadabor','tdbr_comparable','tdbr_eloquence','tdbr_tadabor_cats','comments','likes','users','sync','tafser','shares','tags','books','quran_books'
  int? tdbrConsiders;
  int? tdbrComparables;
  int? tdbrEloquences;
  int? tdbrLinks;
  int? tdbrMedia;
  int? tdbrPrays;
  int? tdbrQuestions;
  int? tdbrRules;
  int? tdbrSuggests;
  int? tdbrTadabors;
  int? tdbrTadaborCategories;
  int? tdbrVerses;
  int? tdbrSources;
  int? likes;
  int? shares;
  int? quranBooks;
  int? tafseers;
  int? sync;
  int? users;

  // constructor
  SectionsWithNewDataVm({
    this.tdbrConsiders,
    this.tdbrComparables,
    this.tdbrEloquences,
    this.tdbrLinks,
    this.tdbrMedia,
    this.tdbrPrays,
    this.tdbrQuestions,
    this.tdbrRules,
    this.tdbrSuggests,
    this.tdbrTadabors,
    this.tdbrTadaborCategories,
    this.tdbrVerses,
    this.tdbrSources,
    this.likes,
    this.shares,
    this.quranBooks,
    this.tafseers,
    this.sync,
    this.users,
  });
  // fromJson
  factory SectionsWithNewDataVm.fromJson(Map<String, dynamic> json) {
    return SectionsWithNewDataVm(
      tdbrConsiders: json["tdbr_consider"] as int?,
      tdbrComparables: json["tdbr_comparable"] as int?,
      tdbrEloquences: json["tdbr_eloquence"] as int?,
      tdbrLinks: json["tdbr_links"] as int?,
      tdbrMedia: json["tdbr_media"] as int?,
      tdbrPrays: json["tdbr_pray"] as int?,
      tdbrQuestions: json["tdbr_questions"] as int?,
      tdbrRules: json["tdbr_rules"] as int?,
      tdbrSuggests: json["tdbr_suggest"] as int?,
      tdbrTadabors: json["tdbr_tadabor"] as int?,
      tdbrTadaborCategories: json["tdbr_tadabor_cats"] as int?,
      tdbrVerses: json["tdbr_ayat"] as int?,
      tdbrSources: json["tdbr_sources"] as int?,
      likes: json["likes"] as int?,
      shares: json["shares"] as int?,
      quranBooks: json["quran_books"] as int?,
      tafseers: json["tafser"] as int?,
      sync: json["sync"] as int?,
      users: json["users"] as int?,
    );
  }
  // toJson
  Map<String, dynamic> toJson() {
    return {
      "tdbr_consider": tdbrConsiders,
      "tdbr_comparable": tdbrComparables,
      "tdbr_eloquence": tdbrEloquences,
      "tdbr_links": tdbrLinks,
      "tdbr_media": tdbrMedia,
      "tdbr_pray": tdbrPrays,
      "tdbr_questions": tdbrQuestions,
      "tdbr_rules": tdbrRules,
      "tdbr_suggest": tdbrSuggests,
      "tdbr_tadabor": tdbrTadabors,
      "tdbr_tadabor_cats": tdbrTadaborCategories,
      "tdbr_ayat": tdbrVerses,
      "tdbr_sources": tdbrSources,
      "likes": likes,
      "shares": shares,
      "quran_books": quranBooks,
      "tafser": tafseers,
      "sync": sync,
      "users": users,
    };
  }
}
