import 'package:tadars/models/hive/tafseer.dart';

class TafseerSearchVM {
  List<Tafseer> allTafseers;
  String searchKey;
  TafseerSearchVM({required this.allTafseers, required this.searchKey});
  Map<String, dynamic> toJson() {
    return {
      'searchKey': searchKey,
      'allTafseers': allTafseers.map((e) => e.toJson()).toList()
    };
  }

  factory TafseerSearchVM.fromJson(Map<String, dynamic> json) =>
      TafseerSearchVM(
          allTafseers: (json['allTafseers'] as List)
              .map((e) => Tafseer.fromJson(e))
              .toList(),
          searchKey: json['searchKey']);
}
