import 'package:quran_core/quran_core.dart';
import 'package:sticky_and_expandable_list/sticky_and_expandable_list.dart';

class QuranIndexSection extends ExpandableListSection {
  QuranIndexSection({
    required this.items,
    this.header = '',
    this.expanded = true,
  });
  QuranIndexSection.empty();
  late bool expanded;

  //return item model list.
  late List<Surah> items;

  //example header, optional
  late String header;
  @override
  List? getItems() {
    return items;
  }

  @override
  bool isSectionExpanded() {
    return expanded;
  }

  @override
  void setSectionExpanded(bool expanded) {
    this.expanded = expanded;
  }
}
