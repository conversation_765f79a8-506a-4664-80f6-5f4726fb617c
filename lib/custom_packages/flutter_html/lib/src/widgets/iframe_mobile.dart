import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart' as webview;
import 'package:html/dom.dart' as dom;

import '../../flutter_html.dart';

/// [IframeContentElement is a [ReplacedElement] with web content.
class IframeContentElement extends ReplacedElement {
  final String? src;
  final double? width;
  final double? height;
  final NavigationDelegate? navigationDelegate;
  final UniqueKey key = UniqueKey();

  IframeContentElement({
    required String name,
    required this.src,
    required this.width,
    required this.height,
    required dom.Element node,
    required this.navigationDelegate,
  }) : super(name: name, style: Style(), node: node, elementId: node.id);

  @override
  Widget toWidget(RenderContext context) {
    final sandboxMode = attributes["sandbox"];
    var controller = webview.WebViewController()
      ..setJavaScriptMode(
        sandboxMode == null || sandboxMode == "allow-scripts"
            ? webview.JavaScriptMode.unrestricted
            : webview.JavaScriptMode.disabled,
      )
      ..setNavigationDelegate(
        webview.NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (webview.WebResourceError error) {},
          onNavigationRequest: (webview.NavigationRequest request) async {
            final result = await navigationDelegate!(NavigationRequest(
              url: request.url,
              isForMainFrame: request.isMainFrame,
            ));
            if (result == NavigationDecision.prevent) {
              return webview.NavigationDecision.prevent;
            } else {
              return webview.NavigationDecision.navigate;
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(src!));
    return Container(
      width: width ?? (height ?? 150) * 2,
      height: height ?? (width ?? 300) / 2,
      child: ContainerSpan(
        style: context.style,
        newContext: context,
        child: webview.WebViewWidget(
          key: key,
          gestureRecognizers: {
            Factory<VerticalDragGestureRecognizer>(
                () => VerticalDragGestureRecognizer())
          },
          controller: controller,
        ),
      ),
    );
  }
}
