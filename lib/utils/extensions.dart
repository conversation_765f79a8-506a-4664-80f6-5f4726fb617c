import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/quran_suar_icons.dart';
import 'package:intl/intl.dart';

extension PlayerDuration on Duration {
  String toStringTime() {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitHours = twoDigits(inHours.remainder(60));
    String twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(inSeconds.remainder(60));
    return "$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
  }
}

extension DateTimeExtension on DateTime {
  //  format date
  String format(String format) {
    var formatter = DateFormat(format);
    return formatter.format(this);
  }

  // from timeOfDay
  DateTime fromTimeOfDay(TimeOfDay timeOfDay) {
    return DateTime(
      year,
      month,
      day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
  }

  // to TimeOfDay
  TimeOfDay toTimeOfDay() {
    return TimeOfDay(hour: hour, minute: minute);
  }
}

extension TimeOfDateExtension on TimeOfDay {
  // to DateTime
  DateTime toDateTime() {
    return DateTime(
      1970,
      1,
      1,
      hour,
      minute,
    );
  }
}

extension quranInt on int {
  String toArabicNumber() {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    var result = toString();
    for (int i = 0; i < english.length; i++) {
      result = result.replaceAll(english[i], arabic[i]);
    }
    return result;
  }

  String toLocaleNumber() {
    if (Get.locale?.languageCode == "ar") {
      return toArabicNumber();
    } else {
      return toString();
    }
  }

  String toArabicNumberReverse() {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    var result = toString();
    for (int i = 0; i < english.length; i++) {
      result = result.replaceAll(english[i], arabic[i]);
    }
    var result2 = "";
    for (int i = result.length - 1; i >= 0; i--) {
      result2 += result[i];
    }
    return result2;
  }

  int converToQuranPartNumber() {
    if (this < 22) return 1;
    if (this >= 602) return 30;
    return ((this - 1) / 20).ceil();
  }

  IconData toSuraIconData() {
    IconData icon;
    switch (this) {
      case 1:
        icon = QuranSuarIcons.sura1;
        break;
      case 2:
        icon = QuranSuarIcons.sura2;
        break;
      case 3:
        icon = QuranSuarIcons.sura3;
        break;
      case 4:
        icon = QuranSuarIcons.sura4;
        break;
      case 5:
        icon = QuranSuarIcons.sura5;
        break;
      case 6:
        icon = QuranSuarIcons.sura6;
        break;
      case 7:
        icon = QuranSuarIcons.sura7;
        break;
      case 8:
        icon = QuranSuarIcons.sura8;
        break;
      case 9:
        icon = QuranSuarIcons.sura9;
        break;
      case 10:
        icon = QuranSuarIcons.sura10;
        break;
      case 11:
        icon = QuranSuarIcons.sura11;
        break;
      case 12:
        icon = QuranSuarIcons.sura12;
        break;
      case 13:
        icon = QuranSuarIcons.sura13;
        break;
      case 14:
        icon = QuranSuarIcons.sura14;
        break;
      case 15:
        icon = QuranSuarIcons.sura15;
        break;
      case 16:
        icon = QuranSuarIcons.sura16;
        break;
      case 17:
        icon = QuranSuarIcons.sura17;
        break;
      case 18:
        icon = QuranSuarIcons.sura18;
        break;
      case 19:
        icon = QuranSuarIcons.sura19;
        break;
      case 20:
        icon = QuranSuarIcons.sura20;
        break;
      case 21:
        icon = QuranSuarIcons.sura21;
        break;
      case 22:
        icon = QuranSuarIcons.sura22;
        break;
      case 23:
        icon = QuranSuarIcons.sura23;
        break;
      case 24:
        icon = QuranSuarIcons.sura24;
        break;
      case 25:
        icon = QuranSuarIcons.sura25;
        break;
      case 26:
        icon = QuranSuarIcons.sura26;
        break;
      case 27:
        icon = QuranSuarIcons.sura27;
        break;
      case 28:
        icon = QuranSuarIcons.sura28;
        break;
      case 29:
        icon = QuranSuarIcons.sura29;
        break;
      case 30:
        icon = QuranSuarIcons.sura30;
        break;
      case 31:
        icon = QuranSuarIcons.sura31;
        break;
      case 32:
        icon = QuranSuarIcons.sura32;
        break;
      case 33:
        icon = QuranSuarIcons.sura33;
        break;
      case 34:
        icon = QuranSuarIcons.sura34;
        break;
      case 35:
        icon = QuranSuarIcons.sura35;
        break;
      case 36:
        icon = QuranSuarIcons.sura36;
        break;
      case 37:
        icon = QuranSuarIcons.sura37;
        break;
      case 38:
        icon = QuranSuarIcons.sura38;
        break;
      case 39:
        icon = QuranSuarIcons.sura39;
        break;
      case 40:
        icon = QuranSuarIcons.sura40;
        break;
      case 41:
        icon = QuranSuarIcons.sura41;
        break;
      case 42:
        icon = QuranSuarIcons.sura42;
        break;
      case 43:
        icon = QuranSuarIcons.sura43;
        break;
      case 44:
        icon = QuranSuarIcons.sura44;
        break;
      case 45:
        icon = QuranSuarIcons.sura45;
        break;
      case 46:
        icon = QuranSuarIcons.sura46;
        break;
      case 47:
        icon = QuranSuarIcons.sura47;
        break;
      case 48:
        icon = QuranSuarIcons.sura48;
        break;
      case 49:
        icon = QuranSuarIcons.sura49;
        break;
      case 50:
        icon = QuranSuarIcons.sura50;
        break;
      case 51:
        icon = QuranSuarIcons.sura51;
        break;
      case 52:
        icon = QuranSuarIcons.sura52;
        break;
      case 53:
        icon = QuranSuarIcons.sura53;
        break;
      case 54:
        icon = QuranSuarIcons.sura54;
        break;
      case 55:
        icon = QuranSuarIcons.sura55;
        break;
      case 56:
        icon = QuranSuarIcons.sura56;
        break;
      case 57:
        icon = QuranSuarIcons.sura57;
        break;
      case 58:
        icon = QuranSuarIcons.sura58;
        break;
      case 59:
        icon = QuranSuarIcons.sura59;
        break;
      case 60:
        icon = QuranSuarIcons.sura60;
        break;
      case 61:
        icon = QuranSuarIcons.sura61;
        break;
      case 62:
        icon = QuranSuarIcons.sura62;
        break;
      case 63:
        icon = QuranSuarIcons.sura63;
        break;
      case 64:
        icon = QuranSuarIcons.sura64;
        break;
      case 65:
        icon = QuranSuarIcons.sura65;
        break;
      case 66:
        icon = QuranSuarIcons.sura66;
        break;
      case 67:
        icon = QuranSuarIcons.sura67;
        break;
      case 68:
        icon = QuranSuarIcons.sura68;
        break;
      case 69:
        icon = QuranSuarIcons.sura69;
        break;
      case 70:
        icon = QuranSuarIcons.sura70;
        break;
      case 71:
        icon = QuranSuarIcons.sura71;
        break;
      case 72:
        icon = QuranSuarIcons.sura72;
        break;
      case 73:
        icon = QuranSuarIcons.sura73;
        break;
      case 74:
        icon = QuranSuarIcons.sura74;
        break;
      case 75:
        icon = QuranSuarIcons.sura75;
        break;
      case 76:
        icon = QuranSuarIcons.sura76;
        break;
      case 77:
        icon = QuranSuarIcons.sura77;
        break;
      case 78:
        icon = QuranSuarIcons.sura78;
        break;
      case 79:
        icon = QuranSuarIcons.sura79;
        break;
      case 80:
        icon = QuranSuarIcons.sura80;
        break;
      case 81:
        icon = QuranSuarIcons.sura81;
        break;
      case 82:
        icon = QuranSuarIcons.sura82;
        break;
      case 83:
        icon = QuranSuarIcons.sura83;
        break;
      case 84:
        icon = QuranSuarIcons.sura84;
        break;
      case 85:
        icon = QuranSuarIcons.sura85;
        break;
      case 86:
        icon = QuranSuarIcons.sura86;
        break;
      case 87:
        icon = QuranSuarIcons.sura87;
        break;
      case 88:
        icon = QuranSuarIcons.sura88;
        break;
      case 89:
        icon = QuranSuarIcons.sura89;
        break;
      case 90:
        icon = QuranSuarIcons.sura90;
        break;
      case 91:
        icon = QuranSuarIcons.sura91;
        break;
      case 92:
        icon = QuranSuarIcons.sura92;
        break;
      case 93:
        icon = QuranSuarIcons.sura93;
        break;
      case 94:
        icon = QuranSuarIcons.sura94;
        break;
      case 95:
        icon = QuranSuarIcons.sura95;
        break;
      case 96:
        icon = QuranSuarIcons.sura96;
        break;
      case 97:
        icon = QuranSuarIcons.sura97;
        break;
      case 98:
        icon = QuranSuarIcons.sura98;
        break;
      case 99:
        icon = QuranSuarIcons.sura99;
        break;
      case 100:
        icon = QuranSuarIcons.sura100;
        break;
      case 101:
        icon = QuranSuarIcons.sura101;
        break;
      case 102:
        icon = QuranSuarIcons.sura102;
        break;
      case 103:
        icon = QuranSuarIcons.sura103;
        break;
      case 104:
        icon = QuranSuarIcons.sura104;
        break;
      case 105:
        icon = QuranSuarIcons.sura105;
        break;
      case 106:
        icon = QuranSuarIcons.sura106;
        break;
      case 107:
        icon = QuranSuarIcons.sura107;
        break;
      case 108:
        icon = QuranSuarIcons.sura108;
        break;
      case 109:
        icon = QuranSuarIcons.sura109;
        break;
      case 110:
        icon = QuranSuarIcons.sura110;
        break;
      case 111:
        icon = QuranSuarIcons.sura111;
        break;
      case 112:
        icon = QuranSuarIcons.sura112;
        break;
      case 113:
        icon = QuranSuarIcons.sura113;
        break;
      case 114:
        icon = QuranSuarIcons.sura114;
        break;
      default:
        icon = QuranSuarIcons.sura114;
    }
    return icon;
  }
}

extension quranString on String {
  String get arabicNormalize => toLowerCase()
      .replaceAll('\u0610', '') //ARABIC SIGN SALLALLAHOU ALAYHE WA SALLAM
      .replaceAll('\u0611', '') //ARABIC SIGN ALAYHE ASSALLAM
      .replaceAll('\u0612', '') //ARABIC SIGN RAHMATULLAH ALAYHE
      .replaceAll('\u0613', '') //ARABIC SIGN RADI ALLAHOU ANHU
      .replaceAll('\u0614', '') //ARABIC SIGN TAKHALLUS

      //Remove koranic anotation
      .replaceAll('\u0615', '') //ARABIC SMALL HIGH TAH
      .replaceAll(
          '\u0616', '') //ARABIC SMALL HIGH LIGATURE ALEF WITH LAM WITH YEH
      .replaceAll('\u0617', '') //ARABIC SMALL HIGH ZAIN
      .replaceAll('\u0618', '') //ARABIC SMALL FATHA
      .replaceAll('\u0619', '') //ARABIC SMALL DAMMA
      .replaceAll('\u061A', '') //ARABIC SMALL KASRA
      .replaceAll('\u06D6',
          '') //ARABIC SMALL HIGH LIGATURE SAD WITH LAM WITH ALEF MAKSURA
      .replaceAll('\u06D7',
          '') //ARABIC SMALL HIGH LIGATURE QAF WITH LAM WITH ALEF MAKSURA
      .replaceAll('\u06D8', '') //ARABIC SMALL HIGH MEEM INITIAL FORM
      .replaceAll('\u06D9', '') //ARABIC SMALL HIGH LAM ALEF
      .replaceAll('\u06DA', '') //ARABIC SMALL HIGH JEEM
      .replaceAll('\u06DB', '') //ARABIC SMALL HIGH THREE DOTS
      .replaceAll('\u06DC', '') //ARABIC SMALL HIGH SEEN
      .replaceAll('\u06DD', '') //ARABIC END OF AYAH
      .replaceAll('\u06DE', '') //ARABIC START OF RUB EL HIZB
      .replaceAll('\u06DF', '') //ARABIC SMALL HIGH ROUNDED ZERO
      .replaceAll('\u06E0', '') //ARABIC SMALL HIGH UPRIGHT RECTANGULAR ZERO
      .replaceAll('\u06E1', '') //ARABIC SMALL HIGH DOTLESS HEAD OF KHAH
      .replaceAll('\u06E2', '') //ARABIC SMALL HIGH MEEM ISOLATED FORM
      .replaceAll('\u06E3', '') //ARABIC SMALL LOW SEEN
      .replaceAll('\u06E4', '') //ARABIC SMALL HIGH MADDA
      .replaceAll('\u06E5', '') //ARABIC SMALL WAW
      .replaceAll('\u06E6', '') //ARABIC SMALL YEH
      .replaceAll('\u06E7', '') //ARABIC SMALL HIGH YEH
      .replaceAll('\u06E8', '') //ARABIC SMALL HIGH NOON
      .replaceAll('\u06E9', '') //ARABIC PLACE OF SAJDAH
      .replaceAll('\u06EA', '') //ARABIC EMPTY CENTRE LOW STOP
      .replaceAll('\u06EB', '') //ARABIC EMPTY CENTRE HIGH STOP
      .replaceAll('\u06EC', '') //ARABIC ROUNDED HIGH STOP WITH FILLED CENTRE
      .replaceAll('\u06ED', '') //ARABIC SMALL LOW MEEM

      //Remove tatweel
      .replaceAll('\u0640', '')

      //Remove tashkeel
      .replaceAll('\u064B', '') //ARABIC FATHATAN
      .replaceAll('\u064C', '') //ARABIC DAMMATAN
      .replaceAll('\u064D', '') //ARABIC KASRATAN
      .replaceAll('\u064E', '') //ARABIC FATHA
      .replaceAll('\u064F', '') //ARABIC DAMMA
      .replaceAll('\u0650', '') //ARABIC KASRA
      .replaceAll('\u0651', '') //ARABIC SHADDA
      .replaceAll('\u0652', '') //ARABIC SUKUN
      .replaceAll('\u0653', '') //ARABIC MADDAH ABOVE
      .replaceAll('\u0654', '') //ARABIC HAMZA ABOVE
      .replaceAll('\u0655', '') //ARABIC HAMZA BELOW
      .replaceAll('\u0656', '') //ARABIC SUBSCRIPT ALEF
      .replaceAll('\u0657', '') //ARABIC INVERTED DAMMA
      .replaceAll('\u0658', '') //ARABIC MARK NOON GHUNNA
      .replaceAll('\u0659', '') //ARABIC ZWARAKAY
      .replaceAll('\u065A', '') //ARABIC VOWEL SIGN SMALL V ABOVE
      .replaceAll('\u065B', '') //ARABIC VOWEL SIGN INVERTED SMALL V ABOVE
      .replaceAll('\u065C', '') //ARABIC VOWEL SIGN DOT BELOW
      .replaceAll('\u065D', '') //ARABIC REVERSED DAMMA
      .replaceAll('\u065E', '') //ARABIC FATHA WITH TWO DOTS
      .replaceAll('\u065F', '') //ARABIC WAVY HAMZA BELOW
      .replaceAll('\u0670', '') //ARABIC LETTER SUPERSCRIPT ALEF

      //Replace Waw Hamza Above by Waw
      .replaceAll('\u0624', '\u0648')

      //Replace Ta Marbuta by Ha
      .replaceAll('\u0629', '\u0647')

      //Replace Ya
      // and Ya Hamza Above by Alif Maksura
      .replaceAll('\u064A', '\u0649')
      .replaceAll('\u0626', '\u0649')

      // Replace Alifs with Hamza Above/Below
      // and with Madda Above by Alif
      .replaceAll('\u0622', '\u0627')
      .replaceAll('\u0623', '\u0627')
      .replaceAll('\u0625', '\u0627');
}

extension Dialog on GetInterface {
  Future<T?> customDialog<T>({
    String title = "",
    TextStyle? titleStyle,
    Widget? content,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    VoidCallback? onCustom,
    Color? cancelTextColor,
    Color? confirmTextColor,
    String? textConfirm,
    String? textCancel,
    String? textCustom,
    Widget? confirm,
    Widget? cancel,
    Widget? custom,
    Color? backgroundColor,
    bool barrierDismissible = true,
    Color? buttonColor,
    String middleText = "Dialog made in 3 lines of code",
    TextStyle? middleTextStyle,
    double radius = 20.0,
    //   ThemeData themeData,
    List<Widget>? actions,

    // onWillPop Scope
    WillPopCallback? onWillPop,
  }) {
    var leanCancel = onCancel != null || textCancel != null;
    var leanConfirm = onConfirm != null || textConfirm != null;
    actions ??= [];

    if (cancel != null) {
      actions.add(cancel);
    } else {
      if (leanCancel) {
        actions.add(TextButton(
          style: TextButton.styleFrom(
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            shape: RoundedRectangleBorder(
                side: BorderSide(
                    color: buttonColor ?? theme.colorScheme.secondary,
                    width: 2,
                    style: BorderStyle.solid),
                borderRadius: BorderRadius.circular(100)),
          ),
          onPressed: () {
            onCancel?.call();
          },
          child: Text(
            textCancel ?? "Cancel",
            style: TextStyle(
                color: cancelTextColor ?? theme.colorScheme.secondary),
          ),
        ));
      }
    }
    if (confirm != null) {
      actions.add(confirm);
    } else {
      if (leanConfirm) {
        actions.add(TextButton(
            style: TextButton.styleFrom(
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              backgroundColor: buttonColor ?? theme.colorScheme.secondary,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100)),
            ),
            child: Text(
              textConfirm ?? "Ok",
              style:
                  TextStyle(color: confirmTextColor ?? theme.colorScheme.background),
            ),
            onPressed: () {
              onConfirm?.call();
            }));
      }
    }

    Widget baseAlertDialog = AlertDialog(
      titlePadding: const EdgeInsets.all(8),
      contentPadding: const EdgeInsets.all(8),

      backgroundColor: backgroundColor ?? theme.dialogBackgroundColor,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(radius))),
      title: Text(title, textAlign: TextAlign.center, style: titleStyle),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          content ??
              Text(middleText,
                  textAlign: TextAlign.center, style: middleTextStyle),
          const SizedBox(height: 16),
          ButtonTheme(
            minWidth: 78.0,
            height: 34.0,
            child: Wrap(
              alignment: WrapAlignment.center,
              spacing: 8,
              runSpacing: 8,
              children: actions,
            ),
          )
        ],
      ),
      // actions: actions, // ?? <Widget>[cancelButton, confirmButton],
      buttonPadding: EdgeInsets.zero,
    );

    if (onWillPop != null) {
      return dialog<T>(
        WillPopScope(
          onWillPop: onWillPop,
          child: baseAlertDialog,
        ),
        barrierDismissible: barrierDismissible,
      );
    }

    return dialog<T>(
      baseAlertDialog,
      barrierDismissible: barrierDismissible,
    );
  }
}
extension IosProductName on String {
  static Map<String, String> productNames = {
    "i386": "iOS Simulator 32-bit",
    "x86_64": "iOS Simulator 64-bit",
    "arm64": "iPhone Simulator ARM",
    "iPhone1,1": "iPhone 1st Gen",
    "iPhone1,2": "iPhone 3G 2nd Gen",
    "iPhone2,1": "iPhone 3GS 3rd Gen",
    "iPhone3,1": "iPhone 4",
    "iPhone3,2": "iPhone 4 (GSM) Rev A",
    "iPhone3,3": "iPhone 4 (CDMA)",
    "iPhone4,1": "iPhone 4s",
    "iPhone5,1": "iPhone 5 (GSM)",
    "iPhone5,2": "iPhone 5 (CDMA+LTE)",
    "iPhone5,3": "iPhone 5c (GSM)",
    "iPhone5,4": "iPhone 5c (Global)",
    "iPhone6,1": "iPhone 5s (GSM)",
    "iPhone6,2": "iPhone 5s (Global)",
    "iPhone7,1": "iPhone 6 Plus",
    "iPhone7,2": "iPhone 6",
    "iPhone8,1": "iPhone 6s",
    "iPhone8,2": "iPhone 6s Plus",
    "iPhone8,4": "iPhone SE",
    "iPhone9,1": "iPhone 7",
    "iPhone9,2": "iPhone 7 Plus",
    "iPhone9,3": "iPhone 7 (no CDMA)",
    "iPhone9,4": "iPhone 7 Plus (no CDMA)",
    "iPhone10,1": "iPhone 8",
    "iPhone10,2": "iPhone 8 Plus",
    "iPhone10,3": "iPhone X",
    "iPhone10,4": "iPhone 8 (no CDMA)",
    "iPhone10,5": "iPhone 8 Plus (no CDMA)",
    "iPhone10,6": "iPhone X (no CDMA)",
    "iPhone11,2": "iPhone XS",
    "iPhone11,4": "iPhone XS Max (China)",
    "iPhone11,6": "iPhone XS Max",
    "iPhone11,8": "iPhone XR",
    "iPhone12,1": "iPhone 11",
    "iPhone12,3": "iPhone 11 Pro",
    "iPhone12,5": "iPhone 11 Pro Max",
    "iPhone12,8": "iPhone SE 2nd Gen",
    "iPhone13,1": "iPhone 12 mini",
    "iPhone13,2": "iPhone 12",
    "iPhone13,3": "iPhone 12 Pro",
    "iPhone13,4": "iPhone 12 Pro Max",
    "iPhone14,2": "iPhone 13 Pro",
    "iPhone14,3": "iPhone 13 Pro Max",
    "iPhone14,4": "iPhone 13 mini",
    "iPhone14,5": "iPhone 13",
    "iPhone14,6": "iPhone SE 3rd Gen",
    "iPhone14,7": "iPhone 14",
    "iPhone14,8": "iPhone 14 Plus",
    "iPhone15,2": "iPhone 14 Pro",
    "iPhone15,3": "iPhone 14 Pro Max",
    "iPod1,1": "iPod 1st Gen",
    "iPod2,1": "iPod 2nd Gen",
    "iPod3,1": "iPod 3rd Gen",
    "iPod4,1": "iPod 4th Gen",
    "iPod5,1": "iPod 5th Gen",
    "iPod7,1": "iPod 6th Gen",
    "iPod9,1": "iPod 7th Gen",
    "iPad1,1": "iPad 1st Gen (WiFi)",
    "iPad1,2": "iPad 1st Gen (3G)",
    "iPad2,1": "iPad 2nd Gen (WiFi)",
    "iPad2,2": "iPad 2nd Gen (GSM)",
    "iPad2,3": "iPad 2nd Gen (CDMA)",
    "iPad2,4": "iPad 2nd Gen New Revision",
    "iPad2,5": "iPad mini 1st Gen (WiFi)",
    "iPad2,6": "iPad mini 1st Gen (GSM+LTE)",
    "iPad2,7": "iPad mini 1st Gen (CDMA+LTE)",
    "iPad3,1": "iPad 3rd Gen (WiFi)",
    "iPad3,2": "iPad 3rd Gen (CDMA)",
    "iPad3,3": "iPad 3rd Gen (GSM)",
    "iPad3,4": "iPad 4th Gen (WiFi)",
    "iPad3,5": "iPad 4th Gen (GSM+LTE)",
    "iPad3,6": "iPad 4th Gen (CDMA+LTE)",
    "iPad4,1": "iPad Air 1st Gen (WiFi)",
    "iPad4,2": "iPad Air 1st Gen (GSM+CDMA)",
    "iPad4,3": "iPad Air 1st Gen (China)",
    "iPad4,4": "iPad mini 2nd Gen (WiFi)",
    "iPad4,5": "iPad mini 2nd Gen (WiFi+Cellular)",
    "iPad4,6": "iPad mini 2nd Gen (China)",
    "iPad4,7": "iPad mini 3rd Gen (WiFi)",
    "iPad4,8": "iPad mini 3rd Gen (WiFi+Cellular)",
    "iPad4,9": "iPad mini 3rd Gen (China)",
    "iPad5,1": "iPad mini 4th Gen (WiFi)",
    "iPad5,2": "iPad mini 4th Gen (WiFi+Cellular)",
    "iPad5,3": "iPad Air 2 (WiFi)",
    "iPad5,4": "iPad Air 2 (WiFi+Cellular)",
    "iPad6,3": "iPad Pro 1st Gen (9.7 inch, WiFi)",
    "iPad6,4": "iPad Pro 1st Gen (9.7 inch, WiFi+Cellular)",
    "iPad6,7": "iPad Pro 1st Gen (12.9 inch, WiFi)",
    "iPad6,8": "iPad Pro 1st Gen (12.9 inch, WiFi+Cellular)",
    "iPad6,11": "iPad 5th Gen (WiFi)",
    "iPad6,12": "iPad 5th Gen (WiFi+Cellular)",
    "iPad7,1": "iPad Pro 2nd Gen (12.9 inch, WiFi)",
    "iPad7,2": "iPad Pro 2nd Gen (12.9 inch, WiFi+Cellular)",
    "iPad7,3": "iPad Pro 2nd Gen (10.5 inch, WiFi)",
    "iPad7,4": "iPad Pro 2nd Gen (10.5 inch, WiFi+Cellular)",
    "iPad7,5": "iPad 6th Gen (WiFi)",
    "iPad7,6": "iPad 6th Gen (WiFi+Cellular)",
    "iPad7,11": "iPad 7th Gen (WiFi)",
    "iPad7,12": "iPad 7th Gen (WiFi+Cellular)",
    "iPad8,1": "iPad Pro 3rd Gen (11 inch, WiFi)",
    "iPad8,2": "iPad Pro 3rd Gen (11 inch, WiFi, 1TB)",
    "iPad8,3": "iPad Pro 3rd Gen (11 inch, WiFi+Cellular)",
    "iPad8,4": "iPad Pro 3rd Gen (11 inch, WiFi+Cellular, 1TB)",
    "iPad8,5": "iPad Pro 3rd Gen (12.9 inch, WiFi)",
    "iPad8,6": "iPad Pro 3rd Gen (12.9 inch, WiFi, 1TB)",
    "iPad8,7": "iPad Pro 3rd Gen (12.9 inch, WiFi+Cellular)",
    "iPad8,8": "iPad Pro 3rd Gen (12.9 inch, WiFi+Cellular, 1TB)",
    "iPad8,9": "iPad Pro 4th Gen (11 inch, WiFi)",
    "iPad8,10": "iPad Pro 4th Gen (11 inch, WiFi+Cellular)",
    "iPad8,11": "iPad Pro 4th Gen (12.9 inch, WiFi)",
    "iPad8,12": "iPad Pro 4th Gen (12.9 inch, WiFi+Cellular)",
    "iPad11,1": "iPad mini 5th Gen (WiFi)",
    "iPad11,2": "iPad mini 5th Gen (WiFi+Cellular)",
    "iPad11,3": "iPad Air 3rd Gen (WiFi)",
    "iPad11,4": "iPad Air 3rd Gen (WiFi+Cellular)",
    "iPad11,6": "iPad 8th Gen (WiFi)",
    "iPad11,7": "iPad 8th Gen (WiFi+Cellular)",
    "iPad12,1": "iPad 9th Gen (WiFi)",
    "iPad12,2": "iPad 9th Gen (WiFi+Cellular)",
    "iPad13,1": "iPad Air 4th Gen (WiFi)",
    "iPad13,2": "iPad Air 4th Gen (WiFi+Cellular)",
    "iPad13,4": "iPad Pro 3rd Gen (11 inch, WiFi)",
    "iPad13,5": "iPad Pro 3rd Gen (11 inch, WiFi)",
    "iPad13,6": "iPad Pro 3rd Gen (11 inch, WiFi+Cellular)",
    "iPad13,7": "iPad Pro 3rd Gen (11 inch, WiFi+Cellular)",
    "iPad13,8": "iPad Pro 5th Gen (12.9 inch, WiFi)",
    "iPad13,9": "iPad Pro 5th Gen (12.9 inch, WiFi)",
    "iPad13,10": "iPad Pro 5th Gen (12.9 inch, WiFi+Cellular)",
    "iPad13,11": "iPad Pro 5th Gen (12.9 inch, WiFi+Cellular)",
    "iPad13,16": "iPad Air 5th Gen (WiFi)",
    "iPad13,17": "iPad Air 5th Gen (WiFi+Cellular)",
    "iPad13,18": "iPad 10th Gen",
    "iPad13,19": "iPad 10th Gen",
    "iPad14,1": "iPad mini 6th Gen (WiFi)",
    "iPad14,2": "iPad mini 6th Gen (WiFi+Cellular)",
    "iPad14,3-A": "iPad Pro 4th Gen (11 inch)",
    "iPad14,3-B": "iPad Pro 4th Gen (11 inch)",
    "iPad14,4-A": "iPad Pro 4th Gen (11 inch)",
    "iPad14,4-B": "iPad Pro 4th Gen (11 inch)",
    "iPad14,5-A": "iPad Pro 6th Gen (12.9 inch)",
    "iPad14,5-B": "iPad Pro 6th Gen (12.9 inch)",
    "iPad14,6-A": "iPad Pro 6th Gen (12.9 inch)",
    "iPad14,6-B": "iPad Pro 6th Gen (12.9 inch)",
    "Watch1,1": "Apple Watch 1st Gen 38mm case",
    "Watch1,2": "Apple Watch 1st Gen 42mm case",
    "Watch2,6": "Apple Watch Series 1 38mm case",
    "Watch2,7": "Apple Watch Series 1 42mm case",
    "Watch2,3": "Apple Watch Series 2 38mm case",
    "Watch2,4": "Apple Watch Series 2 42mm case",
    "Watch3,1": "Apple Watch Series 3 38mm case (GPS+Cellular)",
    "Watch3,2": "Apple Watch Series 3 42mm case (GPS+Cellular)",
    "Watch3,3": "Apple Watch Series 3 38mm case (GPS)",
    "Watch3,4": "Apple Watch Series 3 42mm case (GPS)",
    "Watch4,1": "Apple Watch Series 4 40mm case (GPS)",
    "Watch4,2": "Apple Watch Series 4 44mm case (GPS)",
    "Watch4,3": "Apple Watch Series 4 40mm case (GPS+Cellular)",
    "Watch4,4": "Apple Watch Series 4 44mm case (GPS+Cellular)",
    "Watch5,1": "Apple Watch Series 5 40mm case (GPS)",
    "Watch5,2": "Apple Watch Series 5 44mm case (GPS)",
    "Watch5,3": "Apple Watch Series 5 40mm case (GPS+Cellular)",
    "Watch5,4": "Apple Watch Series 5 44mm case (GPS+Cellular)",
    "Watch5,9": "Apple Watch SE 40mm case (GPS)",
    "Watch5,10": "Apple Watch SE 44mm case (GPS)",
    "Watch5,11": "Apple Watch SE 40mm case (GPS+Cellular)",
    "Watch5,12": "Apple Watch SE 44mm case (GPS+Cellular)",
    "Watch6,1": "Apple Watch Series 6 40mm case (GPS)",
    "Watch6,2": "Apple Watch Series 6 44mm case (GPS)",
    "Watch6,3": "Apple Watch Series 6 40mm case (GPS+Cellular)",
    "Watch6,4": "Apple Watch Series 6 44mm case (GPS+Cellular)",
    "Watch6,6": "Apple Watch Series 7 41mm case (GPS)",
    "Watch6,7": "Apple Watch Series 7 45mm case (GPS)",
    "Watch6,8": "Apple Watch Series 7 41mm case (GPS+Cellular)",
    "Watch6,9": "Apple Watch Series 7 45mm case (GPS+Cellular)",
    "Watch6,10": "Apple Watch SE 40mm case (GPS)",
    "Watch6,11": "Apple Watch SE 44mm case (GPS)",
    "Watch6,12": "Apple Watch SE 40mm case (GPS+Cellular)",
    "Watch6,13": "Apple Watch SE 44mm case (GPS+Cellular)",
    "Watch6,14": "Apple Watch Series 8 41mm case (GPS)",
    "Watch6,15": "Apple Watch Series 8 45mm case (GPS)",
    "Watch6,16": "Apple Watch Series 8 41mm case (GPS+Cellular)",
    "Watch6,17": "Apple Watch Series 8 45mm case (GPS+Cellular)",
    "Watch6,18": "Apple Watch Ultra"
  };
  String? get toIosName {
    return productNames[this];
  }
}



extension IteratorExt<T> on Iterable<T> {
  /// Returns [Iterable] that passes only unique elements.
  /// Elements is comparing by [keySelector].
  /// When some element repeats than [onDuplicate] calls to either ignore it
  /// (if [onDuplicate] is not provided or if it returns `null`)
  /// or replace with a new value.
  /// If element returning by [onDuplicate] is equals to original element or has
  /// a key that already exists it will be ignored.
  Iterable<T> distinctBy(Object Function(T) keySelector,
          {T Function(T old, T current)? onDuplicate}) =>
      _DistinctIterable(this, keySelector, onDuplicate);
}

class _DistinctIterable<T> extends Iterable<T> {
  final Iterable<T> _iterable;
  final Object Function(T) _keySelector;
  final T? Function(T old, T current) _onDuplicate;
  final Map<Object, T> uniqueElements = {};

  _DistinctIterable(
    this._iterable,
    this._keySelector,
    T Function(T, T)? onDuplicate,
  ) : _onDuplicate = onDuplicate ?? ((old, curr) => null);

  @override
  Iterator<T> get iterator {
    for (var element in _iterable) {
      final key = _keySelector(element);

      final old = uniqueElements[key];
      if (old != null) {
        final value = _onDuplicate(old, element);
        if (value != null) uniqueElements[_keySelector(value)] = value;
      } else {
        uniqueElements[key] = element;
      }
    }

    return uniqueElements.values.iterator;
  }
}
