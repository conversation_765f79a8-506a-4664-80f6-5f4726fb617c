// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:flutter/widgets.dart';

@immutable
class _QuranUIIconsData extends IconData {
  const _QuranUIIconsData(int codePoint)
      : super(
          codePoint,
          fontFamily: 'QuranUIIcons',
        );
}

@immutable
class QuranUIIcons {
  const QuranUIIcons._();

  static const IconData basmala = _QuranUIIconsData(0xe000);
  static const IconData bookmarkFilled = _QuranUIIconsData(0xe001);
  static const IconData bookmarkOutllined = _QuranUIIconsData(0xe002);
  static const IconData bookmark = _QuranUIIconsData(0xe003);
  static const IconData cardView = _QuranUIIconsData(0xe004);
  static const IconData check = _QuranUIIconsData(0xe005);
  static const IconData clock = _QuranUIIconsData(0xe006);
  static const IconData cloud = _QuranUIIconsData(0xe007);
  static const IconData configrations = _QuranUIIconsData(0xe008);
  static const IconData copy = _QuranUIIconsData(0xe009);
  static const IconData dashboard = _QuranUIIconsData(0xe00a);
  static const IconData downArrow = _QuranUIIconsData(0xe00b);
  static const IconData downloadCloud = _QuranUIIconsData(0xe00c);
  static const IconData download = _QuranUIIconsData(0xe00d);
  static const IconData edit1 = _QuranUIIconsData(0xe00e);
  static const IconData edit = _QuranUIIconsData(0xe00f);
  static const IconData fastForward = _QuranUIIconsData(0xe010);
  static const IconData flag = _QuranUIIconsData(0xe011);
  static const IconData headphone = _QuranUIIconsData(0xe012);
  static const IconData hide = _QuranUIIconsData(0xe013);
  static const IconData home = _QuranUIIconsData(0xe014);
  static const IconData image = _QuranUIIconsData(0xe015);
  static const IconData information = _QuranUIIconsData(0xe016);
  static const IconData justify = _QuranUIIconsData(0xe017);
  static const IconData language = _QuranUIIconsData(0xe018);
  static const IconData loop = _QuranUIIconsData(0xe019);
  static const IconData loop1 = _QuranUIIconsData(0xe01a);
  static const IconData loop2 = _QuranUIIconsData(0xe01b);
  static const IconData loop3 = _QuranUIIconsData(0xe01c);
  static const IconData loop4 = _QuranUIIconsData(0xe01d);
  static const IconData loop5 = _QuranUIIconsData(0xe01e);
  static const IconData mail = _QuranUIIconsData(0xe01f);
  static const IconData maximize = _QuranUIIconsData(0xe020);
  static const IconData menu = _QuranUIIconsData(0xe021);
  static const IconData minimize = _QuranUIIconsData(0xe022);
  static const IconData minus = _QuranUIIconsData(0xe023);
  static const IconData moon = _QuranUIIconsData(0xe024);
  static const IconData next = _QuranUIIconsData(0xe025);
  static const IconData note = _QuranUIIconsData(0xe026);
  static const IconData pageView = _QuranUIIconsData(0xe027);
  static const IconData path18 = _QuranUIIconsData(0xe028);
  static const IconData pause1 = _QuranUIIconsData(0xe029);
  static const IconData pause = _QuranUIIconsData(0xe02a);
  static const IconData play = _QuranUIIconsData(0xe02b);
  static const IconData plus = _QuranUIIconsData(0xe02c);
  static const IconData previous = _QuranUIIconsData(0xe02d);
  static const IconData rewind = _QuranUIIconsData(0xe02e);
  static const IconData save = _QuranUIIconsData(0xe02f);
  static const IconData settings = _QuranUIIconsData(0xe030);
  static const IconData share = _QuranUIIconsData(0xe031);
  static const IconData show = _QuranUIIconsData(0xe032);
  static const IconData shuffle = _QuranUIIconsData(0xe033);
  static const IconData stop = _QuranUIIconsData(0xe034);
  static const IconData sun = _QuranUIIconsData(0xe035);
  static const IconData sura1 = _QuranUIIconsData(0xe036);
  static const IconData text = _QuranUIIconsData(0xe037);
  static const IconData translation = _QuranUIIconsData(0xe038);
  static const IconData trash1 = _QuranUIIconsData(0xe039);
  static const IconData trash = _QuranUIIconsData(0xe03a);
  static const IconData upArrow = _QuranUIIconsData(0xe03b);
  static const IconData video = _QuranUIIconsData(0xe03c);
  static const IconData volume = _QuranUIIconsData(0xe03d);
  static const IconData x = _QuranUIIconsData(0xe03e);
}
