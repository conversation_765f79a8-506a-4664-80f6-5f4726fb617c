import 'package:tadars/enums/tdbr_type.dart';

class SettingsConstants {
  static const String wakelockEnableKey = "wakelock_enabled";
  static const String clockVisibilityKey = "clock_visibility";

  static const String navVisibilityKey = "nav_visibility";

  static const String lastPageKey = "last_page";
  static const String pageColorKey = "page_color";
  static const String backColorKey = "back_page_color";
  static const String pageTextColorKey = "page_text_color";
  static const String isCustomColorKey = "is_custom_color";
  static const String fontSizeKey = "font_size";
  static const String quranFontScaleKey = "quran_font_size";
  static const String appLocaleCodeKey = "app_locale_code";
  static const String appDarkModeEnabledKey = "app_dark_mode_enabled";
  static const String reciter = "new_reciter";
  static const String autoScroll = "auto_Scroll";
  static const String loop = "loop";
  static const String tafseerId = "tafseer_id";
  // quran book id
  static const String quranBookId = "quran_book_id";
  // show waqf count key
  static const String showWaqfatCountKey = "show_waqfat_count";
  // sync db on app start
  static const String syncDbOnAppStartKey = "sync_db_on_app_start";
  // show progress dialog when sync
  static const String showProgressDialogWhenSyncKey =
      "show_progress_dialog_when_sync";
  // tadabor enabled
  static const String tadaborEnabledKey = "tadabor_enabled";
  // eloquence enabled
  static const String eloquenceEnabledKey = "eloquence_enabled";
  // consider enabled
  static const String considerEnabledKey = "consider_enabled";
  // rule enabled
  static const String ruleEnabledKey = "rule_enabled";
  // suggest enabled
  static const String suggestEnabledKey = "suggest_enabled";
  // pray enabled
  static const String prayEnabledKey = "pray_enabled";
  // media enabled
  static const String mediaEnabledKey = "media_enabled";
  // comparable enabled
  static const String comparableEnabledKey = "comparable_enabled";
  // question enabled
  static const String questionEnabledKey = "question_enabled";

  // hive db version
  static const String hiveDbVersion = "hive_db_version";

  // tdrs  db version
  static const String tdrsDbVersion = "tdrs_db_version";
  // reciter db version
  static const String reciterDbVersion = "reciter_db_version";

  // translation book id
  static const String translationBookId = "translation_book_id";

  // last verse intro
  static const String lastVerseIntro = "last_verse_intro";


  // translation book id
  static const String waqfatAItranslation = "waqfat_ai_translation";

  // First time language selection flag
  static const String firstTimeLanguageSelectedKey =
      "first_time_language_selected";

  // Language data imported flag
  static const String languageDataImportedKey = "language_data_imported";

  // tdbr enabled key list
  static Map<TdbrType, String> tdbrEnabledKeyList = {
    TdbrType.tadabor: tadaborEnabledKey,
    TdbrType.eloquence: eloquenceEnabledKey,
    TdbrType.consider: considerEnabledKey,
    TdbrType.rule: ruleEnabledKey,
    TdbrType.suggest: suggestEnabledKey,
    TdbrType.pray: prayEnabledKey,
    TdbrType.media: mediaEnabledKey,
    TdbrType.comparable: comparableEnabledKey,
    TdbrType.question: questionEnabledKey
  };
}
