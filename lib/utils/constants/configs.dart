class Configs {
  // base url
  static const String baseUrl = 'https://tadars.com/';
  // api url
  static const String apiUrl = '$baseUrl/app/api?ac=';
  // tafseer url
  static const String tafseerDownloadUrl =
      '$baseUrl/download/tafser_hive/tafseer_';
  // get tafseer download url
  static String getTafseerDownloadUrl(String name) =>
      '$tafseerDownloadUrl$name.zip';
  static String getTafseerForUrl(String url) =>
      url.replaceAll(tafseerDownloadUrl, '').replaceAll('.zip', '');
      
  
  // get quran books download url
  static String getQuranBooksDownloadUrl(String path) =>
      '$baseUrl$path/all.zip';
  //get quran books preview url
  static String getQuranBooksPreviewUrl(String path, int number) =>
      '$baseUrl$path/${number.toString().padLeft(3, '0')}.png';
  // get quran book page file path
  static String getQuranBookPageFilePath(
      int pageNumber, String docomentDirector, int bookId) {
    return '$docomentDirector/books/$bookId/${pageNumber.toString().padLeft(3, '0')}.png';
  }
}
