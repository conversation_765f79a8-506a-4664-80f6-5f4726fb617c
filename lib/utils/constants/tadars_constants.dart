import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/enums/tdbr_type.dart';

import 'boxes.dart';
import 'settings.dart';

class TadarsConstants {
  TadarsConstants._();
  static List<TdbrType> tdbrTypes = [
    TdbrType.tadabor,
    TdbrType.consider,
    TdbrType.eloquence,
    TdbrType.rule,
    TdbrType.suggest,
    TdbrType.pray,
    TdbrType.media,
    TdbrType.comparable,
    TdbrType.question
  ];


  static Map<TdbrType, String> tdbrKeyTranlation = {
    TdbrType.tadabor: "تدبر".tr,
    TdbrType.consider: "تذكر واعتبار".tr,
    TdbrType.eloquence: "أسرار بلاغية".tr,
    TdbrType.rule: "أحكام واّداب".tr,
    TdbrType.suggest: "اقتراحات أعمال بالأيات".tr,
    TdbrType.pray: "دعاء ومناجاة".tr,
    TdbrType.media: "تفسير وتدارس".tr,
    TdbrType.comparable: "متشابه".tr,
    TdbrType.question: "التساؤلات".tr,
  };
  static Map<String, String> tdbrTranlation = {
    "tdbr_tadabor": "تدبر".tr,
    "tdbr_consider": "تذكر واعتبار".tr,
    "tdbr_eloquence": "أسرار بلاغية".tr,
    "tdbr_rule": "أحكام واّداب".tr,
    "tdbr_suggest": "اقتراحات أعمال بالأيات".tr,
    "tdbr_pray": "دعاء ومناجاة".tr,
    "tdbr_media": "تفسير وتدارس".tr,
    "tdbr_comparable": "متشابه".tr,
    "tdbr_question": "التساؤلات".tr,
  };

  // tdbr mapping
  static Map<TdbrType, String> tdbrMapping = {
    TdbrType.tadabor: "tadabor",
    TdbrType.consider: "consider",
    TdbrType.eloquence: "eloquence",
    TdbrType.rule: "rules",
    TdbrType.suggest: "suggest",
    TdbrType.pray: "pray",
    TdbrType.media: "media",
    TdbrType.comparable: "comparable",
    TdbrType.question: "question",
  };
  // tdbr mapping for db
  static Map<TdbrType, String> tdbrMappingForDb = {
    TdbrType.tadabor: "tdbr_tadabor",
    TdbrType.consider: "tdbr_consider",
    TdbrType.eloquence: "tdbr_eloquence",
    TdbrType.rule: "tdbr_rules",
    TdbrType.suggest: "tdbr_suggest",
    TdbrType.pray: "tdbr_pray",
    TdbrType.media: "tdbr_media",
    TdbrType.comparable: "tdbr_comparable",
    TdbrType.question: "tdbr_questions",
  };
// tdbr_tadabor ,tdbr_consider,tdbr_eloquence,tdbr_rules,tdbr_suggest,tdbr_pray,tdbr_media,tdbr_comparable,tdbr_questions

// get enabled tdbr types
  static List<TdbrType> getEnabledTdbrTypes() {
    List<TdbrType> enabledTdbrTypes = [];
    for (TdbrType tdbrType in tdbrTypes) {
      if (Hive.box(Boxes.settings).get(
                                    SettingsConstants.tdbrEnabledKeyList[tdbrType],
                                    defaultValue: true)) {
        enabledTdbrTypes.add(tdbrType);
      }
    }
    return enabledTdbrTypes;
  }

}
