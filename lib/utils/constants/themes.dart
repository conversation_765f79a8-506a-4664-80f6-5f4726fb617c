import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class AppThemes {
  static ThemeData lightTheme(String localeCode) => ThemeData(
        focusColor: CustomColors.accentColor,
        useMaterial3: false,
        primaryColor: CustomColors.primaryColor,
        fontFamily: localeCode == "ar"
            ? CommonConstants.samimFontFamily
            : CommonConstants.coconFontFamily,
        brightness: Brightness.light,
        listTileTheme: ListTileThemeData(
            textColor: Colors.grey[700],
            titleTextStyle: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              fontFamily: localeCode == "ar"
                  ? CommonConstants.samimFontFamily
                  : CommonConstants.coconFontFamily,
            )),
        tabBarTheme: TabBarTheme(
          labelColor: CustomColors.primaryColor,
          unselectedLabelColor: Colors.grey,
          indicator: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: CustomColors.primaryColor,
                width: 2,
              ),
            ),
          ),
        ),
        appBarTheme: AppBarTheme(
          color: CustomColors.appBarBackgroundColor,
          centerTitle: true,
          iconTheme: IconThemeData(color: CustomColors.primaryColor),
          elevation: 3,
          shadowColor: Colors.grey.withOpacity(0.2),
          titleTextStyle: TextStyle(
            color: CustomColors.primaryColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
            fontFamily: CommonConstants.samimFontFamily,
          ),
        ),
      
        scaffoldBackgroundColor: Colors.white,
        colorScheme: ThemeData().colorScheme.copyWith(
              primary: CustomColors.accentColor,
              secondary: CustomColors.accentColor,
            ),
        inputDecorationTheme: InputDecorationTheme(
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
          ),
          hoverColor: CustomColors.primaryColor,
          focusColor: CustomColors.primaryColor,
          fillColor: Colors.white,
          filled: true,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 16,
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              width: 0.1,
              color: Colors.grey.shade500,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              width: 0.1,
              color: CustomColors.primaryColor,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              width: 0.1,
              color: Colors.red,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              width: 0.1,
              color: Colors.grey.shade500,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          alignLabelWithHint: true,
        ),
      );
  static ThemeData darkTheme(String localeCode) => ThemeData(
      useMaterial3: false,
      fontFamily: localeCode == "ar"
          ? CommonConstants.samimFontFamily
          : CommonConstants.coconFontFamily,
      brightness: Brightness.dark,
      appBarTheme: Get.theme.appBarTheme.copyWith(
        color: CustomColors.primaryColor.withOpacity(0.3),
        centerTitle: true,
        iconTheme: const IconThemeData(
          color: CustomColors.darkTextColor,
        ),
      ),
      primaryColor: CustomColors.darkPrimaryColor,
      scaffoldBackgroundColor: CustomColors.darkScaffoldBackgroundColor,
      focusColor: CustomColors.primaryColor,
      indicatorColor: Colors.white,
      textTheme: Get.theme.textTheme.apply(
        bodyColor: CustomColors.darkTextColor,
        displayColor: CustomColors.darkTextColor,
        fontFamily: CommonConstants.samimFontFamily,
      ),
      iconTheme:
          Get.theme.iconTheme.copyWith(color: CustomColors.darkTextColor),
      colorScheme: ThemeData().colorScheme.copyWith(
            primary: CustomColors.accentColor,
            secondary: CustomColors.accentColor,
            brightness: Brightness.dark,
          ),
      inputDecorationTheme: InputDecorationTheme(
        hintStyle: TextStyle(
          color: Colors.grey.shade500,
        ),
        hoverColor: CustomColors.darkPrimaryColor,
        focusColor: CustomColors.darkPrimaryColor,
        fillColor: Colors.black,
        filled: true,
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 16,
        ),
        border: OutlineInputBorder(
          borderSide: BorderSide(
            width: 0.1,
            color: Colors.grey.shade500,
          ),
          borderRadius: CommonStyles.borderRadius,
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            width: 0.1,
            color: CustomColors.primaryColor,
          ),
          borderRadius: CommonStyles.borderRadius,
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            width: 0.1,
            color: Colors.red,
          ),
          borderRadius: CommonStyles.borderRadius,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            width: 0.1,
            color: Colors.grey.shade500,
          ),
          borderRadius: CommonStyles.borderRadius,
        ),
        alignLabelWithHint: true,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: CustomColors.darkPrimaryColor,
      ));
}
