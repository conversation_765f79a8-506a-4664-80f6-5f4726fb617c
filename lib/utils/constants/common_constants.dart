import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/quran_ui_icons.dart';

class CommonConstants {
  //fonts
  static const String cairoFontFamily = "Cairo";
  static const String numbersFontFamily = "NumberFont";
  static const String kitabFontFamily = "Kitab";
  static const String coconFontFamily = "Cocon";
  static const String samimFontFamily = "Samim";
  static const String hafsFontFamily = "Hafs";
  static const String amiriFontFamily = "Amiri";
  static const String adwaaAlsalafFontFamily = "adwaa-alsalaf";

  // assets
  static const String quranPagesAssetsPath = "assets/images/quran_pages/";
  static const String pageLeftBackgroundAsset =
      "assets/images/page_right_bg.png";
  static const String pageRightBackgroundAsset =
      "assets/images/page_left_bg.png";

  static const Map<int, IconData> loopIcons = {
    1: QuranUIIcons.loop,
    2: QuranUIIcons.loop2,
    3: QuranUIIcons.loop3,
    4: QuranUIIcons.loop4,
    5: QuranUIIcons.loop5,
  };

  static Map<int, String> getLoopStrings() {
    return {
      1: "مرة واحدة".tr,
      2: "مرتين".tr,
      3: "ثلاث مرات".tr,
      4: "اربع مرات".tr,
      5: "غير محدود".tr,
    };
  }

  //api url

  static String apiUrl = "https://api.tadars.com/v1/";
}
