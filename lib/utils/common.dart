import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_book.dart';
import 'package:tadars/services/network_service.dart';

class Common {
  static String convertToArabicNumber(int input) {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    var result = "";
    for (int i = 0; i < english.length; i++) {
      result = input.toString().replaceAll(english[i], arabic[i]);
    }
    return result;
  }

  static showChangeReciterBottomSheet() async {
    var reciters = await QuranReciterDatabaseProvider.getAllReciters();
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              child: Text("إختر اسم القارئ".tr),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: reciters.length,
                itemBuilder: (contet, i) {
                  return Column(
                    children: [
                      ListTile(
                        onTap: () async {
                          BoxesHelper.setSettingReciterId(reciters[i].id);
                          QuranAudioService.setReciter(reciters[i].id);
                          Get.back();
                          Get.back();
                        },
                        title: Text(
                          reciters[i].name.tr,
                        ),
                        trailing: QuranAudioService.currentReciter.value?.id ==
                                reciters[i].id
                            ? Icon(
                                Icons.check,
                                color: Get.theme.primaryColor,
                              )
                            : null,
                      ),
                      const Divider()
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  static QuranBook madinaQuranBook = QuranBook(
    id: 0,
    title: 'مصحف المدينة',
    detail: 'رواية حفص عن عاصم',
    createdAt: '',
    isDownloaded: true,
    order: 0,
    pagesCount: 604,
    path: '',
    size: 0,
    status: 1,
    updatedAt: '',
  );
}
