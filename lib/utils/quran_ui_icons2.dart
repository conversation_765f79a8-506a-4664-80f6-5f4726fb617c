/// Flutter icons QuranUI2
/// Copyright (C) 2022 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  QuranUI2
///      fonts:
///       - asset: fonts/QuranUI2.ttf
///
///
///
import 'package:flutter/widgets.dart';

class QuranUIIcons2 {
  QuranUIIcons2._();

  static const _kFontFam = 'QuranUIIcons2';
  static const String? _kFontPkg = null;

  static const IconData addNote =
      IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData werd =
      IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData book =
      IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData bookOpened =
      IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData news =
      IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
