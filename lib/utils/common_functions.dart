import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:html/parser.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:tadars/controllers/add_tdbr_controller.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/setting_controller.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/reciter_sql_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/hive/quran_reciter.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/tadars_constants.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/view/components/form_fields/custom_dropdown_form_field.dart';
import 'package:tadars/view/components/form_fields/custom_text_form_field.dart';
import 'package:url_launcher/url_launcher.dart';

import '../view/components/buttons/custom_outlined_button.dart';
import '../view/components/buttons/cutom_filled_button.dart';
import '../view/components/dialogs/tadars_dialog.dart';
import '../view/pages/home/<USER>/drawers/search_text_field.dart';

class CommonFunctions {
  static void showErrorMessage(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeAllSnackbars();
    }
    Get.snackbar("خطأ".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.red,
        snackStyle: SnackStyle.FLOATING,
        shouldIconPulse: true,
        margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        // borderRadius: 0,
        duration: const Duration(seconds: 5));
  }

  static void showSuccessMessage(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    Get.snackbar("نجاح".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.green,
        margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        duration: const Duration(seconds: 2));
  }

  static void handleError(Object err) {
    if (kDebugMode) {
      print(err.runtimeType);
    }
    if (err.runtimeType == DioException) {
      var error = err as DioException;
      if (error.type == DioExceptionType.badResponse) {
        // print(error.error);
        if (error.type == DioExceptionType.badResponse) {
          if (error.response?.statusCode == 400) {
            if (error.response?.data is String) {
              showErrorMessage(error.response?.data.toString() ?? "");
            } else {
              showErrorMessage(
                  (error.response?.data as Map).values.first[0].toString());
            }
          } else if (error.response?.statusCode == 403) {
            if (kDebugMode) {
              print((error.response?.data as Map).values.first);
            }
            showErrorMessage(
                (error.response?.data as Map).values.first[0].toString());
          } else if (error.response?.statusCode == 404) {
            showErrorMessage("غير قادر على الاتصال بالسرفر".tr);
          } else if (error.response?.statusCode == 500) {
            if (kDebugMode) {
              print(error.response?.data);
            }
            showErrorMessage("عذراً حدث خطأ في السرفر".tr);
          } else {
            showErrorMessage(error.response?.data[0]);
            if (kDebugMode) {
              print(error.response?.data);
            }
          }
        } else {
          showErrorMessage("تأكد من اتصالك بالانترنت وعاود المحاولة لاحقاً".tr);
        }
      } else if (error.type == DioExceptionType.unknown) {
        showErrorMessage("تأكد من اتصالك بالانترنت وعاود المحاولة لاحقاً".tr);
      }
    }
  }

  static void showChangeLocaleBottomSheet() {
    Get.bottomSheet(
      SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  "اختر اللغة".tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Divider(
                indent: 16,
                endIndent: 16,
                height: 1,
              ),
              ListTile(
                title: Text("العربية".tr),
                onTap: () {
                  Get.back();
                  Get.updateLocale(const Locale("ar"));
                },
                trailing: Get.locale?.languageCode == "ar"
                    ? Icon(
                        Icons.check,
                        color: Get.theme.primaryColor,
                      )
                    : null,
              ),
              const Divider(
                height: 1,
              ),
              ListTile(
                title: Text("English".tr),
                onTap: () {
                  Get.back();
                  Get.updateLocale(const Locale("en"));
                },
                trailing: Get.locale?.languageCode == "en"
                    ? Icon(Icons.check, color: Get.theme.primaryColor)
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  static String stripeHtmlTags(String html) {
    var str = html.replaceAll(RegExp(r">[\t ]+<"), "><");
    str = str.replaceAll(RegExp(r'<(\/tr)[^>]*>(?<content>[^<]*)<tr>'), '\n');
    str = str.replaceAll('</td><td>', ':');
    str = str.replaceAll('  ', ' ');
    str = str.replaceAll(RegExp(r'<[^>]*>'), ' ');
    str = str.replaceAll(RegExp(r":\n"), "\n");
    return str;
  }

  static String removeHtmlTags(String html) {
    var str = html.replaceAll(RegExp(r">[\t ]+<"), "");
    str = str.replaceAll(RegExp(r'<(\/tr)[^>]*>(?<content>[^<]*)<tr>'), '');
    str = str.replaceAll('</td><td>', '');
    str = str.replaceAll('  ', ' ');
    str = str.replaceAll(RegExp(r'<[^>]*>'), '');
    str = str.replaceAll(RegExp(r":\n"), "");
    return str;
  }

  static String parseHtmlString(String htmlString) {
    final document = parse(htmlString);
    final String parsedString =
        parse(document.body?.text).documentElement?.text ?? "";

    return parsedString;
  }

  static String? convertUrlToId(String url, {bool trimWhitespaces = true}) {
    if (!url.contains("http") && (url.length == 11)) return url;
    if (trimWhitespaces) url = url.trim();

    for (var exp in [
      RegExp(
          r"^https:\/\/(?:www\.|m\.)?youtube\.com\/watch\?v=([_\-a-zA-Z0-9]{11}).*$"),
      RegExp(
          r"^https:\/\/(?:www\.|m\.)?youtube(?:-nocookie)?\.com\/embed\/([_\-a-zA-Z0-9]{11}).*$"),
      RegExp(r"^https:\/\/youtu\.be\/([_\-a-zA-Z0-9]{11}).*$")
    ]) {
      Match? match = exp.firstMatch(url);
      if (match != null && match.groupCount >= 1) return match.group(1);
    }

    return null;
  }

  static bool isValidYoutubeUrl(url) {
    var exp = RegExp(
        r"^(?:https?:\/\/)?(?:m\.|www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$");
    if (exp.hasMatch(url)) {
      return true;
    }
    return false;
  }

  static bool isValidUrl(String url) {
    return url.startsWith('http');
  }

  static bool isValidImageUrl(String url) {
    String ext = url.split('.').last.toLowerCase();
    print("url: $url");
    print("ext: $ext");
    return ext == 'png' || ext == 'jpg' || ext == 'jpeg' || ext == 'gif';
  }

  static void launchURL(String url) {
    if (url.isNotEmpty) {
      if (isValidUrl(url)) {
        launchUrl(Uri.parse(url));
      }
    }
  }

  static Duration parseDuration(String s) {
    int hours = 0;
    int minutes = 0;
    int seconds;
    List<String> parts = s.split(':');
    if (parts.isEmpty) {
      return Duration.zero;
    }
    print("part length  ${parts.length}");
    if (parts.length > 2) {
      hours = int.parse(parts[parts.length - 3]);
    }
    if (parts.length > 1) {
      minutes = int.parse(parts[parts.length - 2]);
    }
    seconds = (int.parse(parts[parts.length - 1]));
    return Duration(hours: hours, minutes: minutes, seconds: seconds);
  }

  static void showLoginDailog() {
    Map<String, dynamic> data = {};
    final key = GlobalKey<FormState>();

    Get.dialog(TadarsDialog(
      title: 'تسجيل الدخول'.tr,
      content: Form(
        key: key,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "مرحبا !",
                style: TextStyle(
                    color: CustomColors.primaryColor,
                    fontSize: 20,
                    fontWeight: FontWeight.bold),
              ),
              CustomTextFormField(
                titleText: "البريد الألكتروني".tr,
                hintText: "مثلا <EMAIL>".tr,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "هذا الحقل مطلوب";
                  }
                  return null;
                },
                onSave: (value) {
                  data.addAll({"email": value?.trim()});
                },
              ),
              CustomTextFormField(
                titleText: "كلمة السري".tr,
                hintText: "كلمة السر".tr,
                maxLine: 1,
                obscureText: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "هذا الحقل مطلوب";
                  }
                  return null;
                },
                onSave: (value) {
                  data.addAll({"password": value?.trim()});
                },
              ),
              const SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      showForgetPasswordDailog();
                    },
                    child: Container(
                        padding: const EdgeInsets.only(top: 4, bottom: 8),
                        child: const Text(
                          "هل نسيت كلمة السر؟",
                          style: TextStyle(color: Colors.grey),
                        )),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  CustomFilledButton(
                    // color: Get.theme.primaryColor,
                    color: CustomColors.primaryColor,

                    textColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    text: "تسجيل الدخول",
                    onPressed: () {
                      if (!key.currentState!.validate()) {
                        return;
                      }
                      key.currentState?.save();
                      SettingController.instance.login(data);
                    },
                    textStyle: const TextStyle(fontSize: 15),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  CustomFilledButton(
                    color: CustomColors.primaryColor,
                    textColor: Colors.white,
                    text: "المتابعة كزائر",
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    textStyle: const TextStyle(fontSize: 15),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                ],
              ),
              Container(
                margin: const EdgeInsets.only(top: 5),
                child: InkWell(
                  onTap: () {
                    Get.back();
                    showRegisterDailog();
                  },
                  child: Container(
                      padding: const EdgeInsets.only(top: 8, bottom: 8),
                      child: const Text(
                        "لايوجد لديك حساب؟",
                        style: TextStyle(
                          color: Colors.grey,
                        ),
                      )),
                ),
              ),
            ],
          ),
        ),
      ),
    ));
  }

  static void showRegisterDailog() {
    final key = GlobalKey<FormState>();

    final Map<String, dynamic> data = {};
    Get.dialog(
      TadarsDialog(
        title: 'إنشاء حساب',
        content: Form(
          key: key,
          child: Container(
            width: double.maxFinite,
            decoration: BoxDecoration(borderRadius: CommonStyles.borderRadius),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "مرحبا !",
                  style: TextStyle(
                      color: CustomColors.primaryColor,
                      fontSize: 20,
                      fontWeight: FontWeight.bold),
                ),
                CustomTextFormField(
                  titleText: "الاسم".tr,
                  hintText: "الاسم".tr,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"name": value?.trim()});
                  },
                ),
                CustomTextFormField(
                  titleText: "البريد الألكتروني".tr,
                  hintText: "مثلا <EMAIL>".tr,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"email": value?.trim()});
                  },
                ),
                CustomTextFormField(
                  titleText: "كلمة السر".tr,
                  hintText: "كلمة السر".tr,
                  obscureText: true,
                  maxLine: 1,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"password": value?.trim()});
                  },
                ),
                // CustomTextFormField(
                //   titleText: "تاكيد كلمة السر".tr,
                //   hintText: "تاكيد كلمة السر".tr,
                //   validator: (value) {
                //     if (value == null || value.trim().isEmpty) {
                //       return "هذا الحقل مطلوب";
                //     }
                //     return null;
                //   },
                //   obscureText: true,
                //   maxLine: 1,
                // ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomFilledButton(
                        color: CustomColors.primaryColor,
                        textColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        text: "إنشاء حساب",
                        onPressed: () {
                          if (!key.currentState!.validate()) {
                            return;
                          }
                          key.currentState?.save();
                          SettingController.instance.newUser(data);
                        },
                        textStyle: const TextStyle(fontSize: 15),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      CustomFilledButton(
                        color: CustomColors.primaryColor,
                        textColor: Colors.white,
                        text: "المتابعة كزائر",
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        textStyle: const TextStyle(fontSize: 15),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ],
                  ),
                ),
                // Container(
                //     margin: const EdgeInsets.symmetric(
                //         vertical: 10),
                //     child: InkWell(
                //         onTap: () {},
                //         child: Text(
                //           "انشاء حساب",
                //           style: TextStyle(
                //               color: Get
                //                   .theme.primaryColor),
                //
                //
                //
                //)))
                Container(
                  margin: const EdgeInsets.only(top: 5),
                  child: InkWell(
                    onTap: () {
                      Get.back();
                      showLoginDailog();
                    },
                    child: Container(
                        padding: const EdgeInsets.only(top: 5, bottom: 5),
                        child: const Text(
                          "يوجد لديك حساب بالفعل؟",
                          style: TextStyle(
                            color: Colors.grey,
                          ),
                        )),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static void showForgetPasswordDailog() {
    Map<String, dynamic> data = {};
    final key = GlobalKey<FormState>();
    Get.dialog(
      TadarsDialog(
        title: 'تغيير كلمة المرور',
        content: Form(
          key: key,
          child: Container(
            width: double.maxFinite,
            decoration: BoxDecoration(borderRadius: CommonStyles.borderRadius),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextFormField(
                  titleText: "البريد الألكتروني".tr,
                  hintText: "مثلا <EMAIL>".tr,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"email": value?.trim()});
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      CustomFilledButton(
                        // color: Get.theme.primaryColor,
                        color: CustomColors.primaryColor,

                        textColor: Colors.white,
                        padding: const EdgeInsets.all(10),
                        text: "ارسال رابط تغير كلمة المرور",
                        onPressed: () {
                          if (!key.currentState!.validate()) {
                            return;
                          }
                          key.currentState?.save();
                          SettingController.instance.forgetPassword(data);
                        },
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      CustomOutlinedButton(
                        color: CustomColors.primaryColor,
                        textColor: Colors.white,
                        text: "الغاء الأمر",
                        padding: const EdgeInsets.all(10),
                        textStyle: const TextStyle(fontSize: 12),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  static void showAddLinkDailog() {
    Map<String, dynamic> data = {};
    final key = GlobalKey<FormState>();
//start
    String starSec = '00';
    String starMin = '00';
    String starHor = '00';
//end
    String endSec = '00';
    String endMin = '00';
    String endHor = '00';

    Get.dialog(AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: CommonStyles.borderRadius,
      ),
      titlePadding: EdgeInsets.zero,
      title: Container(
        decoration: Get.isDarkMode
            ? const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.white, width: 0.1)))
            : null,
        child: Container(
          decoration: BoxDecoration(
              color: Get.theme.primaryColor,
              borderRadius: BorderRadius.vertical(
                  top: CommonStyles.borderRadius.bottomLeft)),
          padding: const EdgeInsets.all(10),
          child: const Text(
            "إضافة رابط",
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      insetPadding: const EdgeInsets.all(20),
      content: SingleChildScrollView(
        child: Form(
          key: key,
          child: Container(
            width: double.maxFinite,
            decoration: BoxDecoration(borderRadius: CommonStyles.borderRadius),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              CustomTextFormField(
                titleText: "رابط المادة".tr,
                hintText: "رابط المادة".tr,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return "هذا الحقل مطلوب";
                  }
                  return null;
                },
                onSave: (value) {
                  data.addAll({"url": value?.trim()});
                },
              ),
              CustomTextFormField(
                titleText: "تفاصيل الرابط".tr,
                hintText: "تفاصيل الرابط".tr,
                minLines: 2,
                onSave: (value) {
                  data.addAll({"details": value?.trim()});
                },
              ),
              Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        margin: const EdgeInsetsDirectional.only(
                          start: 13.0,
                          top: 16,
                        ),
                        child: const Text("وقت بداية المحاضرة")),
                    Container(
                      padding: const EdgeInsets.all(13),
                      // decoration: BoxDecoration(
                      //     borderRadius: CommonStyles.borderRadius,
                      //     border: Border.all(color: Colors.grey, width: 0.1)),
                      width: double.maxFinite,
                      child: Row(
                        children: [
                          CustomNumberPaicker(
                            minValue: 0,
                            maxValue: 60,
                            value: 0,
                            onChanged: (value) {
                              starSec = value < 9
                                  ? value.toString().padLeft(2, '0')
                                  : value.toString();
                            },
                          ),
                          const Text("ث",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                          CustomNumberPaicker(
                              minValue: 0,
                              maxValue: 60,
                              value: 0,
                              onChanged: (value) {
                                starMin = value < 9
                                    ? value.toString().padLeft(2, '0')
                                    : value.toString();
                              }),
                          const Text("د",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                          CustomNumberPaicker(
                              minValue: 0,
                              maxValue: 10,
                              value: 0,
                              onChanged: (value) {
                                starHor = value < 9
                                    ? value.toString().padLeft(2, '0')
                                    : value.toString();
                              }),
                          const Text("س",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                        ],
                      ),

                      // NumberPicker(
                      //     minValue: 0, maxValue: 100, value: 0, onChanged: (vaue) {})
                    )
                  ]),
              Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        margin: const EdgeInsetsDirectional.only(
                          start: 13.0,
                          top: 16,
                        ),
                        child: const Text("وقت نهاية المحاضرة")),
                    SizedBox(
                      // padding: const EdgeInsets.all(13),
                      // decoration: BoxDecoration(
                      //     borderRadius: CommonStyles.borderRadius,
                      //     border: Border.all(color: Colors.grey, width: 0.1)),
                      width: double.maxFinite,
                      child: Row(
                        children: [
                          CustomNumberPaicker(
                            minValue: 0,
                            maxValue: 60,
                            value: 0,
                            onChanged: (value) {
                              endSec = value < 9
                                  ? value.toString().padLeft(2, '0')
                                  : value.toString();
                            },
                          ),
                          const Text("ث",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                          CustomNumberPaicker(
                              minValue: 0,
                              maxValue: 60,
                              value: 0,
                              onChanged: (value) {
                                endMin = value < 9
                                    ? value.toString().padLeft(2, '0')
                                    : value.toString();
                              }),
                          const Text("د",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                          CustomNumberPaicker(
                              minValue: 0,
                              maxValue: 10,
                              value: 0,
                              onChanged: (value) {
                                endHor = value < 9
                                    ? value.toString().padLeft(2, '0')
                                    : value.toString();
                              }),
                          const Text("س",
                              style: TextStyle(
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold)),
                        ],
                      ),

                      // NumberPicker(
                      //     minValue: 0, maxValue: 100, value: 0, onChanged: (vaue) {})
                    )
                  ]),
            ]),
          ),
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        CustomFilledButton(
          // color: Get.theme.primaryColor,
          color: CustomColors.primaryColor,

          textColor: Colors.white,
          padding: const EdgeInsets.all(10),
          text: "إضافة الرابط",
          onPressed: () {
            if (!key.currentState!.validate()) {
              return;
            }
            key.currentState?.save();
            data.addAll({
              "start_at": "$starHor:$starMin:$starSec" == "00:00:00"
                  ? null
                  : "$starHor:$starMin:$starSec"
            });
            data.addAll({
              "end_at": "$endHor:$endMin:$endSec" == "00:00:00"
                  ? null
                  : "$endHor:$endMin:$endSec"
            });
            AddTdbrController.to.links.value.add(data);
            AddTdbrController.to.links.refresh();
            CommonFunctions.showSuccessMessage("تم إضافة الرابط بنجاح");
          },
          textStyle: const TextStyle(fontSize: 15),
        ),
        const SizedBox(
          height: 10,
        ),
        CustomFilledButton(
          color: Colors.transparent,
          textColor: Colors.black,
          text: "الغاء الأمر",
          padding: const EdgeInsets.all(10),
          textStyle: const TextStyle(fontSize: 15, color: Colors.black),
          onPressed: () {
            Get.back();
          },
        ),
      ],
    ));
  }

  static void showAddAyahDailog(int soraNumber, int ayahNumber) {
    Map<String, dynamic> data = {};
    final key = GlobalKey<FormState>();

    var soraNumber0 = Rx<int>(soraNumber);

    var verses = Rx<List<QuranVerse>>(Hive.box<QuranVerse>(Boxes.quranVerses)
        .values
        .where((e) => e.suraNumber == soraNumber0.value)
        .toList());

    Get.dialog(AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: CommonStyles.borderRadius,
      ),
      titlePadding: EdgeInsets.zero,
      title: Container(
        decoration: Get.isDarkMode
            ? const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.white, width: 0.1)))
            : null,
        child: Container(
          decoration: BoxDecoration(
              color: Get.theme.primaryColor,
              borderRadius: BorderRadius.vertical(
                  top: CommonStyles.borderRadius.bottomLeft)),
          padding: const EdgeInsets.all(10),
          child: const Text(
            "إضافة اية",
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      insetPadding: const EdgeInsets.all(20),
      content: SingleChildScrollView(
        child: Form(
          key: key,
          child: Container(
            width: double.maxFinite,
            decoration: BoxDecoration(borderRadius: CommonStyles.borderRadius),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              ValueListenableBuilder<Box<QuranSura>>(
                  valueListenable:
                      Hive.box<QuranSura>(Boxes.quranSuar).listenable(),
                  builder: (context, box, child) {
                    return CustomDropDownFormField<int>(
                      value: soraNumber0.value,
                      items: box.values
                          .map((e) => DropdownMenuItem(
                              value: e.id, child: Text(e.name)))
                          .toList(),
                      titleText: "اختر السورة".tr,
                      hintText: "اختر السورة".tr,
                      onChanged: (value) {
                        soraNumber0.value = value ?? 1;
                        verses.value = Hive.box<QuranVerse>(Boxes.quranVerses)
                            .values
                            .where((e) => e.suraNumber == soraNumber0.value)
                            .toList();
                        ayahNumber = 1;
                      },
                      validator: (value) {
                        return null;

                        // if (value == null || value.trim().isEmpty) {
                        //   return "هذا الحقل مطلوب";
                        // }
                        // return null;
                      },
                      onSaved: (value) {
                        data.addAll({"sora_num": value});
                      },
                    );
                  }),
              Obx(() => CustomDropDownFormField<int>(
                    isExpanded: true,
                    // key: Key(_soraNumber.value.toString()),
                    style: Get.theme.popupMenuTheme.textStyle,
                    value: ayahNumber,

                    items: List.generate(verses.value.length, (index) {
                      return DropdownMenuItem(
                          value: verses.value[index].verseNumber,
                          child: Text(
                            "${index + 1}-  ${verses.value[index].verseWithDiac}",
                            overflow: TextOverflow.ellipsis,
                          ));
                    }),
                    titleText: "اختر الاية".tr,
                    hintText: "اختر الأية".tr,
                    validator: (value) {
                      return null;

                      // if (value == null || value.trim().isEmpty) {
                      //   return "هذا الحقل مطلوب";
                      // }
                      // return null;
                    },
                    onSaved: (value) {
                      data.addAll({"ayah_num": value});
                    },
                  )),
              // CustomTextFormField(
              //     titleText: "تفاصيل الرابط".tr,
              //     hintText: "تفاصيل الرابط".tr,
              //     minLines: 2,
              //     validator: (value) {
              //       if (value == null || value.trim().isEmpty) {
              //         return "هذا الحقل مطلوب";
              //       }
              //       return null;
              //     },
              //     onSave: (value) {
              //       data.addAll({"email": value?.trim()});
              //     },
              //   ),
            ]),
          ),
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        CustomFilledButton(
          // color: Get.theme.primaryColor,
          color: CustomColors.primaryColor,

          textColor: Colors.white,
          padding: const EdgeInsets.all(10),
          text: "إضافة",
          onPressed: () {
            if (!key.currentState!.validate()) {
              return;
            }
            key.currentState?.save();

            data.addAll({
              "ayah_id": Hive.box<QuranVerse>(Boxes.quranVerses)
                  .values
                  .where((e) =>
                      e.suraNumber == data['sora_num'] &&
                      e.verseNumber == data['ayah_num'])
                  .first
                  .id
            });

            if (AddTdbrController.to.ayats.value.any((element) =>
                element['sora_num'] == data['sora_num'] &&
                element['ayah_num'] == data['ayah_num'])) {
              CommonFunctions.showErrorMessage("هذه الأية مضافة مسبقا");
              return;
            } else {
              AddTdbrController.to.ayats.value.add(data);
              AddTdbrController.to.ayats.refresh();
              CommonFunctions.showSuccessMessage("تم إضافة الايه");
            }
            data = {};
          },

          textStyle: const TextStyle(fontSize: 15),
        ),
        const SizedBox(
          height: 10,
        ),
        CustomFilledButton(
          color: Colors.transparent,
          textColor: Colors.black,
          text: "الغاء الأمر",
          padding: const EdgeInsets.all(10),
          textStyle: const TextStyle(fontSize: 15, color: Colors.black),
          onPressed: () {
            Get.back();
          },
        ),
      ],
    ));
  }

  static Future<Map<String, Object?>?> showSourcesDailog() {
    return Get.dialog<Map<String, Object?>>(AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: CommonStyles.borderRadius,
      ),
      titlePadding: EdgeInsets.zero,
      title: Container(
        decoration: Get.isDarkMode
            ? const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.white, width: 0.1)))
            : null,
        child: Container(
          decoration: BoxDecoration(
              color: Get.theme.primaryColor,
              borderRadius: BorderRadius.vertical(
                  top: CommonStyles.borderRadius.bottomLeft)),
          padding: const EdgeInsets.all(10),
          child: const Text(
            "مصادر الوقفات",
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      insetPadding: const EdgeInsets.all(20),
      // scrollable: true,

      content: FutureBuilder<List<Map<String, Object?>>?>(
          future: SqlHelper.getSources(),
          builder: (context, snap) {
            if (snap.connectionState == ConnectionState.waiting) {
              return SizedBox(
                  height: Get.height * 0.7,
                  child: const Center(child: CircularProgressIndicator()));
            }

            var searchKey = Rx<List<Map<String, Object?>>?>(snap.data);

            return SizedBox(
                height: Get.height * 0.7,
                width: double.maxFinite,
                child: Column(
                  children: [
                    SearchTextField(
                      hintText: 'بحث..',
                      fillColor: CustomColors.primaryColor.withOpacity(0.2),
                      onChanged: (value) {
                        searchKey.value = snap.data!.where((e) {
                          // print(e["name"].toString());
                          return e["name"]
                              .toString()
                              .replaceAll(RegExp('[أ,آ,إ]'), "ا")
                              .replaceAll(RegExp('ة'), "ه")
                              .contains(value
                                  .replaceAll(RegExp('[أ,آ,إ]'), "ا")
                                  .replaceAll(RegExp('ة'), "ه"));
                        }).toList();
                      },
                    ),
                    Obx(
                      () => Expanded(
                        child: Scrollbar(
                          child: ListView.builder(
                              itemCount: searchKey.value!.length,
                              physics: const BouncingScrollPhysics(
                                  parent: AlwaysScrollableScrollPhysics()),
                              itemBuilder: (context, index) {
                                return InkWell(
                                  onTap: () {
                                    Get.back(result: searchKey.value![index]);
                                  },
                                  child: Container(
                                    width: double.maxFinite,
                                    decoration: const BoxDecoration(
                                      border: Border(
                                          bottom: BorderSide(
                                              color: Colors.grey, width: 0.1)),
                                      // borderRadius: CommonStyles.borderRadius
                                    ),
                                    padding: const EdgeInsets.all(15),
                                    child:
                                        // Row(
                                        //   mainAxisAlignment: MainAxisAlignment.spaceAround,
                                        //   children: [
                                        // Icon(QuranUIIcons2.addNote, color: Colors.grey),
                                        Text(searchKey.value![index]["name"]
                                            as String),
                                    // ],
                                    // )
                                  ),
                                );
                              }),
                        ),
                      ),
                    ),
                  ],
                ));
          }),
    ));
  }

  static Future<Map<String, Object?>?> showManageLinksDailog() {
    return Get.dialog<Map<String, Object?>>(AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: CommonStyles.borderRadius,
      ),
      titlePadding: EdgeInsets.zero,
      title: Container(
        decoration: Get.isDarkMode
            ? const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.white, width: 0.1)))
            : null,
        child: Container(
          decoration: BoxDecoration(
              color: Get.theme.primaryColor,
              borderRadius: BorderRadius.vertical(
                  top: CommonStyles.borderRadius.bottomLeft)),
          padding: const EdgeInsets.all(10),
          child: const Text(
            "مصادر الوقفات",
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      insetPadding: const EdgeInsets.all(20),
      scrollable: true,
      content: FutureBuilder<List<Map<String, Object?>>?>(
          future: SqlHelper.getSources(),
          builder: (context, snap) {
            if (snap.connectionState == ConnectionState.waiting) {
              return SizedBox(
                  height: Get.height * 0.7,
                  child: const Center(child: CircularProgressIndicator()));
            }

            return SingleChildScrollView(
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: AddTdbrController.to.links.value
                      .map((e) => Column(children: [
                            ListTile(
                              trailing: InkWell(
                                  onTap: () {
                                    AddTdbrController.to.links.value
                                        .removeWhere((element) =>
                                            element['url'] == e['url']);
                                    AddTdbrController.to.links.refresh();
                                    if (AddTdbrController
                                        .to.links.value.isEmpty) {
                                      Get.back();
                                    }
                                  },
                                  child: const Icon(Icons.delete)),
                              minLeadingWidth: 20,
                              title: SizedBox(
                                width: double.maxFinite,
                                child: Text(
                                  e['url'],
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.end,
                                  maxLines: 2,
                                ),
                              ),
                            ),
                            Divider(
                              color: Colors.grey.shade200,
                            ),
                          ]))
                      .toList()
                  // [

                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   ),
                  //   Divider(
                  //     color: Colors.grey.shade200,
                  //   ),
                  //   ListTile(
                  //     trailing: const Icon(Icons.delete),
                  //     leading: Icon(Icons.edit),
                  //     minLeadingWidth: 20,
                  //     title: Container(
                  //       width: double.maxFinite,
                  //       child: Text(
                  //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
                  //         overflow: TextOverflow.ellipsis,
                  //         textAlign: TextAlign.end,
                  //         maxLines: 2,
                  //       ),
                  //     ),
                  //   )
                  // ],
                  ),
            );
            // SizedBox(
            //     height: Get.height * 0.7,
            //     width: double.maxFinite,
            //     child: ListView.builder(
            //         itemCount: snap.data!.length,
            //         itemBuilder: (context, index) {
            //           return InkWell(
            //             onTap: () {
            //               Get.back(result: snap.data![index]);
            //             },
            //             child: Container(
            //               width: double.maxFinite,
            //               decoration: const BoxDecoration(
            //                 border: Border(
            //                     bottom:
            //                         BorderSide(color: Colors.grey, width: 0.1)),
            //                 // borderRadius: CommonStyles.borderRadius
            //               ),
            //               padding: const EdgeInsets.all(15),
            //               child:
            //                   // Row(
            //                   //   mainAxisAlignment: MainAxisAlignment.spaceAround,
            //                   //   children: [
            //                   // Icon(QuranUIIcons2.addNote, color: Colors.grey),
            //                   Text(snap.data![index]["name"] as String),
            //               // ],
            //               // )
            //             ),
            //           );
            //         }));
          }),
    ));
  }

  static Future<Map<String, Object?>?> showManageAyatDailog() {
    return Get.dialog<Map<String, Object?>>(AlertDialog(
        contentPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: CommonStyles.borderRadius,
        ),
        titlePadding: EdgeInsets.zero,
        title: Container(
          decoration: Get.isDarkMode
              ? const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: Colors.white, width: 0.1)))
              : null,
          child: Container(
            decoration: BoxDecoration(
                color: Get.theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                    top: CommonStyles.borderRadius.bottomLeft)),
            padding: const EdgeInsets.all(10),
            child: const Text(
              "الآيات",
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        insetPadding: const EdgeInsets.all(20),
        scrollable: true,
        content: SingleChildScrollView(
          child: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              children: AddTdbrController.to.ayats.value
                  .map((el) => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            trailing: InkWell(
                                onTap: () {
                                  if (AddTdbrController.to.ayats.value.length >
                                      1) {
                                    AddTdbrController.to.ayats.value
                                        .removeWhere((element) =>
                                            element['ayah_num'] ==
                                                el['ayah_num'] &&
                                            element['sora_num'] ==
                                                el['sora_num']);
                                    AddTdbrController.to.ayats.refresh();
                                  } else {
                                    CommonFunctions.showErrorMessage(
                                        "يجب أن يكون هنالك آية واحة على الأقل"
                                            .tr);
                                  }
                                },
                                child: const Icon(Icons.remove_circle_rounded)),
                            // leading: Icon(Icons.edit),
                            minLeadingWidth: 20,
                            subtitle: Text(Hive.box<QuranSura>(Boxes.quranSuar)
                                .values
                                .where(
                                    (element) => element.id == el['sora_num'])
                                .first
                                .name),
                            title: SizedBox(
                              width: double.maxFinite,
                              child: Text(
                                Hive.box<QuranVerse>(Boxes.quranVerses)
                                    .values
                                    .where((e) =>
                                        e.suraNumber == el['sora_num'] &&
                                        e.verseNumber == el['ayah_num'])
                                    .first
                                    .verseWithDiac,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),
                          ),
                          Divider(
                            color: Colors.grey.shade200,
                          ),
                        ],
                      ))
                  .toList()
              //  [
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     subtitle: Text("الفاتحة"),
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "الحمدلله رب العالمين",
              //         overflow: TextOverflow.ellipsis,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   ),
              //   Divider(
              //     color: Colors.grey.shade200,
              //   ),
              //   ListTile(
              //     trailing: const Icon(Icons.delete),
              //     leading: Icon(Icons.edit),
              //     minLeadingWidth: 20,
              //     title: Container(
              //       width: double.maxFinite,
              //       child: Text(
              //         "https://www.platform.ye:2083/cpsess0975565348/frontend/paper_lantern/filemanager/index.html?dir=public_html%2fpublic",
              //         overflow: TextOverflow.ellipsis,
              //         textAlign: TextAlign.end,
              //         maxLines: 2,
              //       ),
              //     ),
              //   )
              // ],
              )),
        )));
  }

//to add new tdbr
  static void showAddNewTdbrDailog(int verseNumber, int soraNumber) {
    final key = GlobalKey<FormState>();
    // String text = "";
    final Map<String, dynamic> data = {};
    ValueNotifier<int> from = ValueNotifier<int>(verseNumber);
    var verse = Hive.box<QuranVerse>(Boxes.quranVerses).get(
      "${soraNumber.toString().padLeft(3, "0")}_${from.value.toString().padLeft(3, "0")}",
    );

    Get.dialog(AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: CommonStyles.borderRadius,
      ),
      titlePadding: EdgeInsets.zero,
      title: Container(
        decoration: Get.isDarkMode
            ? const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.white, width: 0.1)))
            : null,
        child: Container(
          decoration: BoxDecoration(
              color: Get.theme.primaryColor,
              borderRadius: BorderRadius.vertical(
                  top: CommonStyles.borderRadius.bottomLeft)),
          padding: const EdgeInsets.all(10),
          child: const Text(
            "إضافة وقفة جديدة",
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      insetPadding: EdgeInsets.zero,
      content: SingleChildScrollView(
        child: Form(
          key: key,
          child: Container(
            width: double.maxFinite,
            decoration: BoxDecoration(borderRadius: CommonStyles.borderRadius),
            margin: const EdgeInsets.symmetric(horizontal: 30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  height: 10,
                ),
                CustomTextFormField(
                  initialValue: verse?.verseWithDiac,
                  readOnly: true,
                  // onSave: (value) {
                  //   data.addAll({"name": value?.trim()});
                  // },
                ),

                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     CustomFilledButton(
                //       color: CustomColors.primaryColor,
                //       textColor: Colors.white,
                //       padding: const EdgeInsets.all(10),
                //       text: "إضافة اية أخرى",
                //       onPressed: () {
                //         if (!key.currentState!.validate()) {
                //           return;
                //         }
                //         key.currentState?.save();
                //         SettingController.instance.newUser(data);
                //       },
                //       textStyle: const TextStyle(fontSize: 15),
                //     ),
                //     const SizedBox(
                //       width: 10,
                //     ),
                //     CustomFilledButton(
                //       color: CustomColors.primaryColor,
                //       textColor: Colors.white,
                //       text: "",
                //       padding: const EdgeInsets.all(10),
                //       textStyle: const TextStyle(fontSize: 15),
                //       onPressed: () {
                //         Get.back();
                //       },
                //     ),
                //   ],
                // ),

                CustomDropDownFormField<String>(
                    titleText: "نوع الوقفة",
                    hintText: "نوع الوقفة",
                    items: TadarsConstants.tdbrTranlation.entries
                        .map((e) => DropdownMenuItem(
                            value: e.key, child: Text(e.value)))
                        .toList(),
                    onSaved: (value) {}),
                CustomTextFormField(
                  titleText: "نص الوقفة".tr,
                  hintText: "نص الوقفة".tr,
                  minLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"name": value?.trim()});
                  },
                ),
                CustomTextFormField(
                  titleText: "البريد الألكتروني".tr,
                  hintText: "مثلا <EMAIL>".tr,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"email": value?.trim()});
                  },
                ),
                FutureBuilder<List<Map<String, dynamic>>>(
                    future: HomeController.to.futuretdbrSorces,
                    initialData: const [
                      {"id": 1, "name": "بدون مصدر"},
                    ],
                    builder: (context, snap) {
                      if (snap.connectionState == ConnectionState.waiting) {
                        return const SizedBox.shrink();
                      }
                      return CustomDropDownFormField(
                        value: 1,
                        items: snap.data!
                            .map((e) => DropdownMenuItem(
                                  value: e["id"],
                                  child: Text(e['name'] ?? ""),
                                ))
                            .toList(),
                        titleText: "مصدر الوقفة",
                      );
                    }),
                CustomTextFormField(
                  titleText: "كلمة السري".tr,
                  hintText: "كلمة السر".tr,
                  obscureText: true,
                  maxLine: 1,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                  onSave: (value) {
                    data.addAll({"password": value?.trim()});
                  },
                ),
                // CustomTextFormField(
                //   titleText: "تاكيد كلمة السر".tr,
                //   hintText: "تاكيد كلمة السر".tr,
                //   validator: (value) {
                //     if (value == null || value.trim().isEmpty) {
                //       return "هذا الحقل مطلوب";
                //     }
                //     return null;
                //   },
                //   obscureText: true,
                //   maxLine: 1,
                // ),
              ],
            ),
          ),
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomFilledButton(
                  color: CustomColors.primaryColor,
                  textColor: Colors.white,
                  padding: const EdgeInsets.all(10),
                  text: "إنشاء حساب",
                  onPressed: () {
                    if (!key.currentState!.validate()) {
                      return;
                    }
                    key.currentState?.save();
                    SettingController.instance.newUser(data);
                  },
                  textStyle: const TextStyle(fontSize: 15),
                ),
                const SizedBox(
                  width: 10,
                ),
                CustomFilledButton(
                  color: CustomColors.primaryColor,
                  textColor: Colors.white,
                  text: "المتابعة كزائر",
                  padding: const EdgeInsets.all(10),
                  textStyle: const TextStyle(fontSize: 15),
                  onPressed: () {
                    Get.back();
                  },
                ),
              ],
            ),
            // Container(
            //     margin: const EdgeInsets.symmetric(
            //         vertical: 10),
            //     child: InkWell(
            //         onTap: () {},
            //         child: Text(
            //           "انشاء حساب",
            //           style: TextStyle(
            //               color: Get
            //                   .theme.primaryColor),
            //
            //
            //
            //)))
            Container(
              margin: const EdgeInsets.only(top: 5),
              child: InkWell(
                onTap: () {
                  Get.back();
                  showLoginDailog();
                },
                child: Container(
                    padding: const EdgeInsets.only(top: 5, bottom: 5),
                    child: const Text(
                      "يوجد لديك حساب بالفعل؟",
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    )),
              ),
            ),
          ],
        ),
      ],
    ));
  }

  static shareItem(int tdbrId, String type) {
    try {
      var body = {
        "itemId": tdbrId,
        "type": type,
        'uid': Hive.box(Boxes.settings).get("uid"),
        'token': Hive.box(Boxes.settings).get("user_token"),
      };
      // if()
      ApiHelper(Dio()).shareItem(body);
    } catch (e) {}
  }

  static bool isColorLight(Color color) {
    return ThemeData.estimateBrightnessForColor(color) == Brightness.light;
  }

  static Future<void> contact() async {
    var message = """السلام عليكم
\r\n
\r\n
\r\n
\r\n
""";
    var subject = "تدارس القرآن - ";
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    String buildNumber = packageInfo.buildNumber;

    if (Platform.isIOS) {
      subject += "آيفون";
      message += 'Sent from iOS\r\n';
      var iosInfo = await DeviceInfoPlugin().iosInfo;
      var systemName = iosInfo.systemName;
      var version = iosInfo.systemVersion;
      var name = iosInfo.utsname.machine.toIosName;

      message += '$systemName $version, $name\r\n';
    } else if (Platform.isAndroid) {
      subject += "أندرويد";
      message += 'Sent from Android\r\n';
      var androidInfo = await DeviceInfoPlugin().androidInfo;
      var release = androidInfo.version.release;
      var sdkInt = androidInfo.version.sdkInt;
      var manufacturer = androidInfo.manufacturer;
      var model = androidInfo.model;
      message += 'Android $release (SDK $sdkInt), $manufacturer $model\r\n';
    }
    message += "App Version: $version($buildNumber) \r\n";
    launchUrl(Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: encodeQueryParameters({
        'subject': subject,
        'body': message,
      }),
    ));
  }

  static String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  //download reciter timings
  static Future<bool> downloadReciterTimingsDb(
      String path, int reciterId) async {
    debugPrint("reciter id $reciterId");
    try {
      debugPrint("path   https://admin.tadars.com/$path");
      final rs = await Dio().get(
        "https://admin.tadars.com/$path",
        options: Options(responseType: ResponseType.plain),
      );

      var data = (json.decode(rs.data) as Map<String, dynamic>)
          .entries
          .map((MapEntry<String, dynamic> el) =>
              el.value as Map<String, dynamic>)
          .toList();

      await ReciterSqlHelper.insertIntoTable("timings", data);

      await ReciterSqlHelper.updateTableWhere(
          "reciters", {'downloaded': 1}, "id = ?", [reciterId]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static downloadRcirTimingsDailog() async {
    await Get.defaultDialog(
      title: "تحميل البيانات",
      content: WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: const Column(
          children: [
            SizedBox(
              height: 100,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
            Text(
              "جاري جلب البيانات ...",
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  static Future<bool> downloadReciterTiming(QuranReciter? reciter) async {
    CommonFunctions.downloadRcirTimingsDailog();

    var success = await CommonFunctions.downloadReciterTimingsDb(
        reciter?.path ?? "", reciter?.id ?? 0);

    Get.back();
    return success;
  }
}
