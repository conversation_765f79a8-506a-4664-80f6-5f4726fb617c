import 'package:get/get.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';
import 'package:tadars/view/pages/about/about_page.dart';
import 'package:tadars/view/pages/language_selection/language_selection_page.dart';
import 'package:tadars/view/pages/search/search_page.dart';
import 'package:tadars/view/pages/search/tafseer_search_page.dart';
import 'package:tadars/view/pages/search/waqafat_search_page.dart';
import 'package:tadars/view/pages/tafseer/tafseer_page.dart';
import 'package:tadars/view/pages/tdbr/add_tdbr_page.dart';
import '../view/pages/books/books_page.dart';
import '../view/pages/werd/manage_werd_page.dart';
import '../view/pages/werd/werds_page.dart';
import 'constants/routes.dart';
import '../view/pages/home/<USER>';
import '../view/pages/splash/splash_page.dart';

List<GetPage<dynamic>> appPages = [
  GetPage(
    name: Routes.homePage,
    page: () => const HomePage(),
  ),
  GetPage(
    name: Routes.splashPage,
    page: () => const SplashPage(),
  ),
  GetPage(
    name: Routes.searchPage,
    page: () => const SearchPage(),
  ),
  GetPage(
    name: Routes.waqafatSearchPage,
    page: () => const WaqafatSearchPage(),
  ),
  GetPage(
    name: Routes.tafseerSearchPage,
    page: () => const TafseerSearchPage(),
  ),
  GetPage(
    name: Routes.aboutPage,
    page: () => const AboutPage(),
  ),
  // tafseer page
  GetPage(
    name: Routes.tafseerPage,
    page: () => const TafseerPage(),
  ),
  // books page
  GetPage(
    name: Routes.booksPage,
    page: () => const BooksPage(),
  ),
  // werds page
  GetPage(
    name: Routes.werdsPage,
    page: () => const WerdsPage(),
  ),
  // manage werd page
  GetPage(
    name: Routes.manageWerdPage,
    page: () => const ManageWerdPage(),
  ),
  GetPage(
    name: Routes.newTdbr,
    page: () => const AddTdbrPage(),
  ),
  GetPage(name: Routes.introVersePage, page: () => const IntroVersePage()),
  GetPage(
    name: Routes.languageSelectionPage,
    page: () => const LanguageSelectionPage(),
  ),
];
