// import 'dart:io';
// import 'package:audio_service/audio_service.dart';
// import 'package:audio_session/audio_session.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/foundation.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:just_audio/just_audio.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:tadars/helpers/boxes_helper.dart';
// import 'package:tadars/helpers/reciter_sql_helper.dart';
// import 'package:tadars/models/hive/quran_sura.dart';
// import 'package:tadars/models/hive/quran_timing.dart';
// import 'package:tadars/services/network_service.dart';
// import 'package:tadars/utils/constants/boxes.dart';

// class AudioPlayerHandler extends BaseAudioHandler with QueueHandler, SeekHandler {
//   AudioPlayerHandler(int? loopCount, int? reciterId) {
//     _initialize(loopCount, reciterId);
//   }

//   final AudioPlayer _player = AudioPlayer();
//   late int _reciterId;
//   late int _suraNumber;
//   late int _verseNumber;
//   late int _loopCount;
//   late bool _firstVerse;
//   late QuranTiming? _timing;
//   late List<MediaItem> _queue;
//   late int _currentLoop;

//   // Getters and Setters
//   int get verseNumber => _verseNumber;
//   set verseNumber(int value) {
//     _firstVerse = value == 1;
//     _verseNumber = value;
//   }

//   // Initialize AudioPlayerHandler
//   Future<void> _initialize(int? loopCount, int? reciterId) async {
//     _loopCount = loopCount ?? 1;
//     _reciterId = reciterId ?? 1;
//     await AudioSession.instance.then((session) => session.configure(const AudioSessionConfiguration.music()));
//     await _initializeReciter();
//     _setupPlayerListeners();
//   }

//   // Setup listeners for audio player events
//   void _setupPlayerListeners() {
//     _player.playbackEventStream.map(_transformEvent).pipe(playbackState);
//     _player.positionStream.listen(_handlePositionStream);
//     _player.currentIndexStream.distinct().listen((index) {
//       if (index != null) _handleCurrentIndexStream(index);
//     });
//   }

//   // Initialize the reciter and media items
//   Future<void> _initializeReciter() async {
//     var suar = Hive.box<QuranSura>(Boxes.quranSuar);
//     var reciters = await ReciterSqlHelper.getAllReciters();
//     var reciter = reciters.firstWhere((re) => re.id == _reciterId);
//     await Hive.openBox<QuranTiming>(Boxes.reciterTimings(_reciterId));
    
//     // Map suras and create media items
//     _queue = suar.values.map((sura) => _createMediaItem(sura, reciter)).toList();
//     queue.add(_queue);

//     await _setAudioSource();
//   }

//   // Create MediaItem
//   MediaItem _createMediaItem(QuranSura sura, Reciter reciter) {
//     String id = '${reciter.filesUrl}${_getFilesUrlSuffix(_reciterId, sura.id)}${sura.id.toString().padLeft(3, "0")}.mp3';
//     int duration = _getSuraDuration(sura.id);
    
//     return MediaItem(
//       id: id,
//       album: sura.name,
//       artist: reciter.name,
//       title: sura.name,
//       extras: {
//         "sura_number": sura.id,
//         "reciter_id": reciter.id,
//         "last_verse": sura.versesCount,
//         "duration": duration,
//       },
//     );
//   }

//   // Set AudioSource for AudioPlayer
//   Future<void> _setAudioSource() async {
//     try {
//       var audioSources = _queue.map((item) {
//         var file = BoxesHelper.getDownloadedFile(item.id);
//         return AudioSource.uri(file != null ? File(file.filePath).uri : Uri.parse(item.id));
//       }).toList();

//       await _player.setAudioSource(
//         ConcatenatingAudioSource(children: audioSources, useLazyPreparation: false),
//         preload: false,
//       );
//     } catch (e) {
//       _handleError("setAudioSourceError", e);
//       stop();
//     }
//   }

//   // Handle media position stream and verse logic
//   Future<void> _handlePositionStream(Duration position) async {
//     if (_timing == null) return;

//     if (_firstVerse) {
//       _timing = await getVerseTiming(_suraNumber, _verseNumber);
//       _updateMediaItem(_suraNumber, _verseNumber);
//       _firstVerse = false;
//     } else if (position.inMilliseconds >= _timing!.endAt) {
//       await _handleVerseLoopOrSeek();
//     }
//   }

//   // Handle verse loop or seek to next verse
//   Future<void> _handleVerseLoopOrSeek() async {
//     if (_currentLoop < _loopCount) {
//       await seek(Duration(milliseconds: _timing?.startAt ?? 0));
//       _currentLoop++;
//     } else {
//       verseNumber++;
//       _timing = await getVerseTiming(_suraNumber, _verseNumber);
//       _updateMediaItem(_suraNumber, _verseNumber);
//     }
//   }

//   // Handle current index stream
//   Future<void> _handleCurrentIndexStream(int index) async {
//     _suraNumber = index + 1;
//     verseNumber = 1;
//     _updateMediaItem(_suraNumber, verseNumber);
//   }

//   // Update MediaItem
//   void _updateMediaItem(int suraNumber, int verseNumber) {
//     var mediaItem = _queue[suraNumber - 1];
//     mediaItem = mediaItem.copyWith(
//       displaySubtitle: "الآية ($verseNumber)",
//     );
//     mediaItem.add(mediaItem);
//   }

//   // Get sura duration
//   int _getSuraDuration(int suraId) {
//     var durations = ReciterSqlHelper.getSoarDuration(_reciterId);
//     return durations?.firstWhere((dur) => dur['sura_number'] == suraId)['duration'] ?? 0;
//   }

//   // Handle errors and notify listeners
//   void _handleError(String eventType, Object error) {
//     customEvent.add({
//       "type": eventType,
//       "args": {"error": error},
//     });
//   }

//   // Transform playback event to PlaybackState
//   PlaybackState _transformEvent(PlaybackEvent event) {
//     return PlaybackState(
//       controls: [
//         MediaControl.rewind,
//         _player.playing ? MediaControl.pause : MediaControl.play,
//         MediaControl.stop,
//         MediaControl.fastForward,
//       ],
//       androidCompactActionIndices: const [0, 1, 3],
//       processingState: const {
//         ProcessingState.idle: AudioProcessingState.idle,
//         ProcessingState.loading: AudioProcessingState.loading,
//         ProcessingState.buffering: AudioProcessingState.buffering,
//         ProcessingState.ready: AudioProcessingState.ready,
//         ProcessingState.completed: AudioProcessingState.completed,
//       }[_player.processingState]!,
//       playing: _player.playing,
//       updatePosition: _player.position,
//       bufferedPosition: _player.bufferedPosition,
//       speed: _player.speed,
//       queueIndex: event.currentIndex,
//     );
//   }

//   // Helper to get file URL suffix for reciters
//   String _getFilesUrlSuffix(int reciterId, int suraNumber) {
//     return (reciterId == 3 && [2, 27, 28, 58].contains(suraNumber)) ? "replaced/" : "";
//   }

//   // Public actions
//   @override
//   Future<void> play() async => _player.play();
//   @override
//   Future<void> pause() async => _player.pause();
//   @override
//   Future<void> stop() async => _player.stop();
//   @override
//   Future<void> seek(Duration position) async => _player.seek(position);
//   @override
//   Future<void> skipToNext() async => _player.seekToNext();
//   @override
//   Future<void> skipToPrevious() async => _player.seekToPrevious();
// }
