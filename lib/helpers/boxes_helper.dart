import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:quran_core/quran_core.dart' hide QuranPage;
import 'package:tadars/models/hive/bookmark.dart';
import 'package:tadars/models/hive/downloaded_file.dart';
import 'package:tadars/models/hive/note.dart';
import 'package:tadars/models/hive/quran_reciter.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/models/hive/tafseer.dart';
import 'package:tadars/models/hive/tdbr_bookmark.dart';
import 'package:tadars/models/view_models/sections_with_new_data_vm.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/configs.dart';
// ignore: unused_import
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/settings.dart';
import '../../../../../utils/extensions.dart';
import '../models/hive/quran_book.dart';
import '../models/hive/quran_page.dart';
import '../models/hive/quran_tafseer.dart';
import '../models/hive/source_bookmark.dart';
import '../models/hive/user.dart';
import '../models/hive/werd.dart';
import '../models/tafseer_search_vm.dart';

Future<List<Tafseer>> filterTafseer(Map<String, dynamic> searchVM) async {
  var vm = TafseerSearchVM.fromJson(searchVM);
  var key = vm.searchKey.arabicNormalize;
  return vm.allTafseers
      .where((element) => element.text.arabicNormalize.contains(key))
      .toList();
}

class BoxesHelper {
  // get page verses
  static List<QuranVerse> getPageVerses(int pageNumber) {
    var page = getPageById(pageNumber);
    return Hive.box<QuranVerse>(Boxes.quranVerses)
        .valuesBetween(
          startKey:
              "${page!.firstSuraNumber.toString().padLeft(3, "0")}_${page.firstVerseNumber.toString().padLeft(3, "0")}",
          endKey:
              "${page.lastSuraNumber.toString().padLeft(3, "0")}_${page.lastVerseNumber.toString().padLeft(3, "0")}",
        )
        .toList();
  }

  // get verse
  static QuranVerse? getVerse(int suraNumber, int verseNumber) {
    return Hive.box<QuranVerse>(Boxes.quranVerses).get(
      "${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}",
    );
  }

  // get next verse
  static QuranVerse? getNextVerse(int suraNumber, int verseNumber) {
    var verse = getVerse(suraNumber, verseNumber);
    if (verse == null) return null;
    var nextVerse = getVerse(verse.suraNumber, verse.verseNumber + 1);
    if (nextVerse == null) {
      var nextSura = getSura(verse.suraNumber + 1);
      if (nextSura == null) return null;
      return getVerse(nextSura.id, 1);
    }
    return nextVerse;
  }

  // get previous verse
  static QuranVerse? getPreviousVerse(int suraNumber, int verseNumber) {
    var verse = getVerse(suraNumber, verseNumber);
    if (verse == null) return null;
    var previousVerse = getVerse(verse.suraNumber, verse.verseNumber - 1);
    if (previousVerse == null) {
      var previousSura = getSura(verse.suraNumber - 1);
      if (previousSura == null) return null;
      return getVerse(previousSura.id, previousSura.versesCount);
    }
    return previousVerse;
  }

  // get sura
  static QuranSura? getSura(int suraNumber) {
    return Hive.box<QuranSura>(Boxes.quranSuar).get(suraNumber.toString());
  }

  // Notes
  static void addToNotes(Note note) {
    Hive.box<Note>(Boxes.notes).put(
      "${note.suraNumber.toString().padLeft(3, "0")}_${note.verseNumber.toString().padLeft(3, "0")}",
      note,
    );
  }

  static Note? getNote(int suraNumber, int verseNumber) {
    return Hive.box<Note>(Boxes.notes).get(
      "${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}",
    );
  }

  static QuranSura? getSuraById(int suraNumber) {
    return Hive.box<QuranSura>(Boxes.quranSuar).get(suraNumber);
  }

  static bool hasNote(int suraNumber, int verseNumber) {
    return getNote(suraNumber, verseNumber) == null ? false : true;
  }

  static List<Note> getAllNotes() {
    return Hive.box<Note>(Boxes.notes).values.toList();
  }

  static Future<void> deleteAllNotes() async {
    await Hive.box<Note>(Boxes.notes).clear();
  }

  static void deleteNote(int suraNumber, int verseNumber) {
    Hive.box<Note>(Boxes.notes).delete(
      "${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}",
    );
  }

  static ValueListenable<Box<Note>> getListenableNote(
    int suraNumber,
    int verseNumber,
  ) {
    return Hive.box<Note>(Boxes.notes).listenable(
      keys: [
        ("${verseNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}"),
      ],
    );
  }

  static ValueListenable<Box<Note>> getListenableNotes() {
    return Hive.box<Note>(Boxes.notes).listenable();
  }

  // bookmarks
  static void addPageToBookmarks(
    int pageNumber,
    int suraNumber,
    int verseNumber,
  ) {
    print("addPageToBookmarks");
    Hive.box<Bookmark>(Boxes.bookmarks).put(
      pageNumber,
      Bookmark(
        pageNumber: pageNumber,
        suraNumber: suraNumber,
        colorIndex: 0,
        verseNumber: verseNumber,
        createdAt: DateTime.now(),
        type: 2,
        id: 0,
      ),
    );
  }

  static void deletePageFromBookmarks(int pageNumber) {
    Hive.box<Bookmark>(Boxes.bookmarks).delete(pageNumber);
  }

  static void deleteFromBookmarks(int suraNumber, int verseNumber) {
    Hive.box<Bookmark>(Boxes.bookmarks).delete(
      suraNumber.toString().padLeft(3, "0") +
          verseNumber.toString().padLeft(3, "0"),
    );
  }

  static Bookmark? getPageBookmark(int pageNumber) {
    return Hive.box<Bookmark>(Boxes.bookmarks).get(pageNumber);
  }

  static bool hasPageBookmark(int pageNumber) {
    return getPageBookmark(pageNumber) == null ? false : true;
  }

  static void setLoopCount(int count) {
    Hive.box(Boxes.settings).put(SettingsConstants.loop, count);
  }

  static void setTranslationbookId(int id) {
    Hive.box(Boxes.settings).put(SettingsConstants.translationBookId, id);
  }

  static int getTranslationbookId() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.translationBookId, defaultValue: 1);
  }

  static void setSelectedWaqfatAITranslation(String lang) {
    Hive.box(Boxes.settings).put(SettingsConstants.waqfatAItranslation, lang);
  }

  static int getSelectedWaqfatTranslation() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.waqfatAItranslation, defaultValue: 'en');
  }

  static ValueListenable<Box<dynamic>>
  getSelectedWaqfatTranslationListenable() {
    return Hive.box(
      Boxes.settings,
    ).listenable(keys: [SettingsConstants.waqfatAItranslation]);
  }

  static int getLoopCount() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.loop, defaultValue: 1);
  }

  static void setSettingReciterId(int id) {
    Hive.box(Boxes.settings).put(SettingsConstants.reciter, id);
  }

  static double getSettingAutoScrollValue() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.autoScroll, defaultValue: 5.0);
  }

  static void setSettingAutoScrollValue(double value) {
    Hive.box(Boxes.settings).put(SettingsConstants.autoScroll, value);
  }

  static int getSettingReciterId() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.reciter, defaultValue: 28);
  }

  // Reciters
  static QuranReciter? getReciterById(int id) {
    return Hive.box<QuranReciter>(Boxes.quranReciters).get(id);
  }

  static List<QuranReciter> getAllReciters() {
    return Hive.box<QuranReciter>(Boxes.quranReciters).values.toList();
  }

  // Downloaded file
  static DownloadedFile? getDownloadedFile(String url) {
    return Hive.box<DownloadedFile>(Boxes.downloads).get(url);
  }

  static Future<DownloadedFile?> getLazyDownloadedFile(String url) async {
    //await Hive.openLazyBox<DownloadedFile>(Boxes.downloads);
    return Hive.box<DownloadedFile>(Boxes.downloads).get(url);
  }

  static Future<void> addFileToDownloads(String url, String filePath) async {
    await Hive.box<DownloadedFile>(
      Boxes.downloads,
    ).put(url, DownloadedFile(filePath: filePath));
  }

  static List<QuranSura> searchInSuar(String key) {
    return Hive.box<QuranSura>(Boxes.quranSuar).values
        .where(
          (element) =>
              element.name.arabicNormalize.contains(key) ||
              element.nameEn.contains(key),
        )
        .toList();
  }

  static Future<List<Tafseer>> searchInTafseer(
    String key,
    String tafseerName,
  ) async {
    if (!Hive.isBoxOpen("tafseer_$tafseerName")) {
      await Hive.openBox<Tafseer>("tafseer_$tafseerName");
    }
    var allTafseers = Hive.box<Tafseer>('tafseer_$tafseerName').values.toList();
    print('tafseer count ${allTafseers.length}');
    var data = await compute(
      filterTafseer,
      TafseerSearchVM(allTafseers: allTafseers, searchKey: key).toJson(),
    );
    return data;
  }

  static List<QuranVerse> searchInVerses(String key) {
    return Hive.box<QuranVerse>(Boxes.quranVerses).values
        .where(
          (element) => element.verseWithoutDiac.arabicNormalize.contains(key),
        )
        .toList();
  }

  static List<String> getSearchHistory(String key) {
    return Hive.box<String>(Boxes.searchHistory).values.toList();
  }

  static Future<void> addToSearchHistory(String key) async {
    await Hive.box<String>(Boxes.searchHistory).add(key);
  }

  static List<String> getWaqafatSearchHistory(String key) {
    return Hive.box<String>(Boxes.waqafatSearchHistory).values.toList();
  }

  static Future<void> addToWaqafatSearchHistory(String key) async {
    await Hive.box<String>(Boxes.waqafatSearchHistory).add(key);
  }

  static List<String> getTafseerSearchHistory(String key) {
    return Hive.box<String>(Boxes.tafseerSearchHistory).values.toList();
  }

  static Future<void> addToTafseerSearchHistory(String key) async {
    await Hive.box<String>(Boxes.tafseerSearchHistory).add(key);
  }

  // set tafseer id to setting
  static void setSettingTafseerId(int id) {
    Hive.box(Boxes.settings).put(SettingsConstants.tafseerId, id);
  }

  // get tafseer id from setting
  static int getSettingTafseerId() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.tafseerId, defaultValue: 6);
  }

  // get tafseer by id
  static QuranTafseer? getTafseerById(int id) {
    return Hive.box<QuranTafseer>(Boxes.quranTafseer).get(id);
  }

  // get last verse intro
  static Verse? getLastVerseIntro() {
    if (Hive.box(Boxes.settings).get(SettingsConstants.lastVerseIntro) !=
        null) {
      return Verse.fromJson(
        json.decode(
          Hive.box(Boxes.settings).get(SettingsConstants.lastVerseIntro),
        ),
      );
    }
    return null;
  }

  //set last verse intro
  static void setLastVerseIntro(Verse? verse) {
    Hive.box(Boxes.settings).put(
      SettingsConstants.lastVerseIntro,
      verse == null ? null : json.encode(verse.toJson()),
    );
  }

  static QuranTafseer? getTafseerByName(String name) {
    return Hive.box<QuranTafseer>(
      Boxes.quranTafseer,
    ).values.firstWhere((element) => element.name == name);
  }

  // update tafseer status
  static void updateTafseerStatus(int id, int status) {
    var tafseer = getTafseerById(id);
    if (tafseer != null) {
      tafseer.status = status;
      Hive.box<QuranTafseer>(Boxes.quranTafseer).put(id, tafseer);
    }
  }

  // update tafseer downloaded
  static void updateTafseerDownloaded(int id, bool downloaded) {
    var tafseer = getTafseerById(id);
    if (tafseer != null) {
      tafseer.downloaded = downloaded;
      Hive.box<QuranTafseer>(Boxes.quranTafseer).put(id, tafseer);
    }
  }

  // get all tafseers
  static List<QuranTafseer> getAllTafseers() {
    return Hive.box<QuranTafseer>(
      Boxes.quranTafseer,
    ).values.where((element) => element.status == 1).toList();
  }

  // get tafseer setting id as listenable
  static ValueListenable<Box<QuranTafseer>> getListenableTafseer() {
    return Hive.box<QuranTafseer>(Boxes.settings).listenable();
  }

  // get  setting   as listenable
  static ValueListenable<Box> getListenableSettingsTafseerId() {
    return Hive.box(
      Boxes.settings,
    ).listenable(keys: [SettingsConstants.tafseerId]);
  }

  // get setting tafseer id
  static int getSettingTafseerIdAsInt() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.tafseerId, defaultValue: 6);
  }

  // get page by id
  static QuranPage? getPageById(int id) {
    return Hive.box<QuranPage>(Boxes.quranPages).get(id);
  }

  // get tafseers by page number
  static Future<List<Tafseer>> getTafseersByPageNumber(
    String tafseerName,
    int pageNumber,
  ) async {
    var page = getPageById(pageNumber);
    if (!Hive.isBoxOpen("tafseer_$tafseerName")) {
      await Hive.openBox<Tafseer>("tafseer_$tafseerName");
    }
    return Hive.box<Tafseer>("tafseer_$tafseerName")
        .valuesBetween(
          startKey:
              "${page!.firstSuraNumber.toString().padLeft(3, "0")}_${page.firstVerseNumber.toString().padLeft(3, "0")}",
          endKey:
              "${page.lastSuraNumber.toString().padLeft(3, "0")}_${page.lastVerseNumber.toString().padLeft(3, "0")}",
        )
        .toList();
  }

  // get tafseers by verse
  static Future<Tafseer> getTafseersByVerseNumber(
    String tafseerName,
    int surahNumber,
    int verseNumber,
  ) async {
    // var page = getPageById(pageNumber);
    if (!Hive.isBoxOpen("tafseer_$tafseerName")) {
      await Hive.openBox<Tafseer>("tafseer_$tafseerName");
    }
    return Hive.box<Tafseer>("tafseer_$tafseerName")
        .valuesBetween(
          startKey:
              "${surahNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}",
          endKey:
              "${surahNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}",
        )
        .first;
  }

  // add tafseer to downloaded tafseers
  static Future<void> addTafseerToDownloaded(String name) async {
    var url = Configs.getTafseerDownloadUrl(name);
    await Hive.box<String>(Boxes.downloadedTafseers).put(url, name);
  }

  // get tafseer from downloaded tafseers
  static String? getTafseerFromDownloaded(String name) {
    var url = Configs.getTafseerDownloadUrl(name);
    return Hive.box<String>(Boxes.downloadedTafseers).get(url);
  }

  static List<String> getAllDownloadedTafseers() {
    var tafseers = ["sa3dy", "sraj"];
    var urls = Hive.box<String>(Boxes.downloadedTafseers).values.toList();
    for (var url in urls) {
      tafseers.add(Configs.getTafseerForUrl(url));
    }
    return tafseers;
  }

  // check if tafseer is downloaded
  static bool isTafseerDownloaded(String name) {
    var url = Configs.getTafseerDownloadUrl(name);
    return Hive.box<String>(Boxes.downloadedTafseers).containsKey(url);
  }

  // delete tafseer from downloaded tafseers
  static Future<void> deleteTafseerFromDownloaded(String name) async {
    var url = Configs.getTafseerDownloadUrl(name);
    await Hive.box<String>(Boxes.downloadedTafseers).delete(url);
    // delete file
    // documnet directory
    var dir = await getApplicationDocumentsDirectory();
    var file = File("${dir.path}/$name.hive");
    if (file.existsSync()) {
      file.deleteSync();
    }
  }

  // get all quran books
  static List<QuranBook> getAllQuranBooks() {
    return Hive.box<QuranBook>(Boxes.quranBooks).values.toList();
  }

  // get downloaded books
  static List<QuranBook> getDownloadedBooks() {
    return Hive.box<QuranBook>(
      Boxes.quranBooks,
    ).values.toList().where((element) => element.isDownloaded).toList();
  }

  // Check if first-time language selection has been completed
  static bool isFirstTimeLanguageSelected() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.firstTimeLanguageSelectedKey, defaultValue: false);
  }

  // Check if language data has been imported
  static bool isLanguageDataImported() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.languageDataImportedKey, defaultValue: false);
  }

  // get quran book by id
  static QuranBook? getQuranBookById(int id) {
    return Hive.box<QuranBook>(Boxes.quranBooks).get(id);
  }
  // update quran book status

  static void updateQuranBookStatus(int id, int status) {
    var book = getQuranBookById(id);
    if (book != null) {
      book.status = status;
      Hive.box<QuranBook>(Boxes.quranBooks).put(id, book);
    }
  }

  // get quran books as listenable
  static ValueListenable<Box<QuranBook>> getListenableQuranBooks() {
    return Hive.box<QuranBook>(Boxes.quranBooks).listenable();
  }

  // get quran book as listenable
  static ValueListenable<Box<QuranBook>> getListenableQuranBook(int id) {
    return Hive.box<QuranBook>(Boxes.quranBooks).listenable(keys: [id]);
  }

  // update quran book is downloaded
  static Future<void> updateQuranBookIsDownloaded(
    int id,
    bool isDownloaded,
  ) async {
    var book = getQuranBookById(id);
    if (book != null) {
      book.isDownloaded = isDownloaded;
      await Hive.box<QuranBook>(Boxes.quranBooks).put(id, book);
    }
  }

  // set setting quran book id
  static void setSettingQuranBookId(int id) {
    Hive.box(Boxes.settings).put(SettingsConstants.quranBookId, id);
  }

  // get setting quran book id
  static int getSettingQuranBookId() {
    return Hive.box(
      Boxes.settings,
    ).get(SettingsConstants.quranBookId, defaultValue: 0);
  }

  // get setting quran book id as listenable
  static ValueListenable<Box> getListenableSettingsQuranBookId() {
    return Hive.box(
      Boxes.settings,
    ).listenable(keys: [SettingsConstants.quranBookId]);
  }

  //to get auto increment id
  static int getAutoIncrementId(String key) {
    int id = (Hive.box(Boxes.hiveSequence).get(key) ?? 0) + 1;
    Hive.box(Boxes.hiveSequence).put(key, id);
    return id;
  }

  // put werd
  static Future<void> putWerd(Werd werd) async {
    await Hive.box<Werd>(Boxes.werds).put(werd.id, werd);
  }

  static Future<void> deleteWerd(int? id) async {
    await Hive.box<Werd>(Boxes.werds).delete(id);
  }

  // user crud
  // get User by id
  static User? getUser(int id) {
    return Hive.box<User>(Boxes.users).get(id);
  }

  // get all Users
  static List<User> getAllUsers() {
    return Hive.box<User>(Boxes.users).values.toList();
  }

  // delete User by id
  static void deleteUser(int id) async {
    await Hive.box<User>(Boxes.users).delete(id);
  }

  // put User
  static void putUser(User user) async {
    await Hive.box<User>(Boxes.users).put(user.id, user);
  }

  // get last user id
  static int getLastUserId() {
    var lastId = 0;
    try {
      var box = Hive.box<User>(Boxes.users);
      lastId = box.values.last.id;
    } catch (e) {
      if (e.runtimeType == StateError) lastId = 0;
    }
    return lastId;
  }
  // end user crud

  // get last quranBooks id
  static int getLastQuranBooksId() {
    var lastId = 0;
    try {
      var box = Hive.box<QuranBook>(Boxes.quranBooks);
      lastId = box.values.last.id;
    } catch (e) {
      if (e.runtimeType == StateError) lastId = 0;
    }
    return lastId;
  }

  // get last tafseers id
  static int getLastTafseersId() {
    var lastId = 0;
    try {
      var box = Hive.box<QuranTafseer>(Boxes.quranTafseer);
      lastId = box.values.last.id;
    } catch (e) {
      if (e.runtimeType == StateError) lastId = 0;
    }
    return lastId;
  }

  // get last sync id
  static int getLastSyncId() {
    var lastId = 0;
    try {
      var box = Hive.box(Boxes.settings);
      lastId = box.get("last_sync_id");
    } catch (e) {
      if (e.runtimeType == StateError) lastId = 0;
    }
    return lastId;
  }

  // get last users id
  static int getLastUsersId() {
    var lastId = 0;
    try {
      var box = Hive.box<User>(Boxes.users);
      lastId = box.values.last.id;
    } catch (e) {
      if (e.runtimeType == StateError) lastId = 0;
    }
    return lastId;
  }

  // get last ids
  static SectionsWithNewDataVm getLastIds() {
    var lastIds = SectionsWithNewDataVm();
    lastIds.users = getLastUserId();
    lastIds.quranBooks = getLastQuranBooksId();
    lastIds.tafseers = getLastTafseersId();

    return lastIds;
  }

  static int? getIdOrNull(key, id) {
    print(
      'setting value ${Hive.box(Boxes.settings).get(key, defaultValue: true)}',
    );
    return Hive.box(Boxes.settings).get(key, defaultValue: true) == true
        ? id
        : null;
  }

  //put value to any box
  static void putBox<T>(boxName, value) {
    // print(value[0].toJson());
    var v = <dynamic, T>{for (var e in value) e?.id: e};
    Hive.box<T>(boxName).putAll(v);
  }

  //delete  'tdbr_ayat','tdbr_consider','tdbr_links','tdbr_media','tdbr_pray','tdbr_questions','tdbr_rules','tdbr_sources','tdbr_suggest','tdbr_tadabor','tdbr_comparable','tdbr_eloquence','tdbr_tadabor_cats','comments','likes','users','sync','tafser','shares','tags','books','quran_books'

  static void deleteTdbr(String boxName, int id) {
    var box = Hive.box(boxName);
    box.delete(id);
  }

  static void deleteTafseer(int id) {
    var box = Hive.box<QuranTafseer>(Boxes.quranTafseer);
    box.delete(id);
  }

  static void deleteQuranBook(int id) {
    var box = Hive.box<QuranBook>(Boxes.quranBooks);
    box.delete(id);
  }

  static void putLastSyncId(int id) {
    Hive.box(Boxes.settings).put("last_sync_id", id);
  }

  // get user token from setting box
  static String getUserToken() {
    var box = Hive.box(Boxes.settings);
    return box.get("user_token", defaultValue: "");
  }

  // get uid from setting box
  static String getUid() {
    var box = Hive.box(Boxes.settings);
    return box.get("uid", defaultValue: "").toString();
  }

  // add tdbr to bookmark
  static void addTdbrToBookmark(int tdbrId, String tdbrType) {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    var bookmark = TdbrBookmark(
      tdbrId: tdbrId,
      tdbrType: tdbrType,
      createdAt: DateTime.now(),
    );
    box.put('${tdbrType}_$tdbrId', bookmark);
  }

  // remove tdbr from bookmark
  static void removeTdbrFromBookmark(int tdbrId, String tdbrType) {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    box.delete('${tdbrType}_$tdbrId');
  }

  // is tdbr exist in bookmark
  static bool isTdbrExistInBookmark(int tdbrId, String tdbrType) {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    return box.containsKey('${tdbrType}_$tdbrId');
  }

  // get all tdbr bookmarks
  static List<TdbrBookmark> getTdbrBookmarks(String tdbrType) {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    return box.values.where((element) => element.tdbrType == tdbrType).toList();
  }

  static List<int> getTdbrBookmarksIds(String tdbrType) {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    return box.values
        .where((element) => element.tdbrType == tdbrType)
        .map((e) => e.tdbrId)
        .toList();
  }

  // get Listenable tdbr bookmarks
  static ValueListenable<Box<TdbrBookmark>> getListenableTdbrBookmarks() {
    var box = Hive.box<TdbrBookmark>(Boxes.tdbrBookmarks);
    return box.listenable();
  }

  // add tdbr to bookmark
  static void addSourceToBookmark(int sourceId) {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    var bookmark = SourceBookmark(
      sourceId: sourceId,
      createdAt: DateTime.now(),
    );
    box.put(sourceId, bookmark);
  }

  // remove tdbr from bookmark
  static void removeSourceFromBookmark(int sourceId) {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    box.delete(sourceId);
  }

  // is tdbr exist in bookmark
  static bool isSourceExistInBookmark(int sourceId) {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    return box.containsKey(sourceId);
  }

  // get all tdbr bookmarks
  static List<SourceBookmark> getSourceBookmarks() {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    return box.values.toList();
  }

  static List<int> getSourceBookmarksIds() {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    return box.values.map((e) => e.sourceId).toList();
  }

  // get Listenable tdbr bookmarks
  static ValueListenable<Box<SourceBookmark>> getListenableSourceBookmarks() {
    var box = Hive.box<SourceBookmark>(Boxes.sourceBookmarks);
    return box.listenable();
  }

  // get listenable show wagafat count
  static ValueListenable<Box> getListenableShowWagafatCount() {
    var box = Hive.box(Boxes.settings);
    return box.listenable(keys: [SettingsConstants.showWaqfatCountKey]);
  }

  static int getHiveDbVersion() {
    var box = Hive.box(Boxes.settings);
    return box.get(SettingsConstants.hiveDbVersion, defaultValue: 0);
  }

  // set db version
  static void setHiveDbVersion(int version) {
    var box = Hive.box(Boxes.settings);
    box.put(SettingsConstants.hiveDbVersion, version);
  }

  //get tdb db version
  static int getTdrsDbVersion() {
    var box = Hive.box(Boxes.settings);
    return box.get(SettingsConstants.tdrsDbVersion, defaultValue: 0);
  }

  // set db version
  static void setTdrsDbVersion(int version) {
    var box = Hive.box(Boxes.settings);
    box.put(SettingsConstants.tdrsDbVersion, version);
  }

  //get reciter db version
  static int getReciterDbVersion() {
    var box = Hive.box(Boxes.settings);
    return box.get(SettingsConstants.reciterDbVersion, defaultValue: 0);
  }

  // set db version
  static void setReciterDbVersion(int version) {
    var box = Hive.box(Boxes.settings);
    box.put(SettingsConstants.reciterDbVersion, version);
  }

  static List<Bookmark> getAllBookmarks() {
    return Hive.box<Bookmark>(Boxes.bookmarks).values.toList();
  }

  static Future<void> deleteAllBookmarks() async {
    await Hive.box<Bookmark>(Boxes.bookmarks).clear();
  }
}
