import 'package:flutter/services.dart';
import 'package:sqlbrite/sqlbrite.dart';
import 'package:path/path.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/like.dart';
import 'package:tadars/models/hive/share.dart';
import 'package:tadars/models/hive/tdbr_comparable.dart';
import 'package:tadars/models/hive/tdbr_consider.dart';
import 'package:tadars/models/hive/tdbr_eloquence.dart';
import 'package:tadars/models/hive/tdbr_link.dart';
import 'package:tadars/models/hive/tdbr_media.dart';
import 'package:tadars/models/hive/tdbr_pray.dart';
import 'package:tadars/models/hive/tdbr_question.dart';
import 'package:tadars/models/hive/tdbr_rule.dart';
import 'package:tadars/models/hive/tdbr_source.dart';
import 'package:tadars/models/hive/tdbr_suggest.dart';
import 'package:tadars/models/hive/tdbr_tadabor.dart';

import '../enums/tdbr_type.dart';
import '../models/view_models/sections_with_new_data_vm.dart';
import '../utils/constants/tadars_constants.dart';

class SqlHelper {
  static BriteDatabase? _briteDB;

  static Future<BriteDatabase?> get briteDB async {
    if (_briteDB == null) {
      late Database db;
      var databasesPath = await getDatabasesPath();
      var path = join(databasesPath, "tdrs_db.sqlite");
      db = await openDatabase(
        path,
        version: 2,

        onUpgrade: (db, oldVersion, newVersion) async {
          print("on upgrade...");
          String sqlScript = await rootBundle.loadString(
            'assets/db/tdbr_transalation.sql',
          );

          List<String> statements = sqlScript.split(';');
          for (String statement in statements) {
            statement = statement.trim();
            if (statement.isNotEmpty) {
              await db.execute(statement);
            }
          }
        },
      );

      _briteDB = BriteDatabase(db);
    }
    return _briteDB;
  }

  static Future<List<int>> getTdbAyat(
    int suraNum,
    int ayahNum,
    String tdbrType,
  ) async {
    return (await (await briteDB)?.rawQuery(
          "select tdbr_id from tdbr_ayat where sora_num = ? and ayah_num = ? and tdbr_type = ?",
          [suraNum, ayahNum, tdbrType],
        ))?.map<int>((e) => e['tdbr_id'] as int? ?? 0).toList() ??
        [];
  }

  static Future<List<TdbrTadabor>> getTdbTadabor(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_tadabor");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_tadabor where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrTadabor tdbTadabor = TdbrTadabor.fromJson(e);
          tdbTadabor.verseNumber = verseNum;
          tdbTadabor.suraNumber = suraNum;
          return tdbTadabor;
        }).toList() ??
        [];
  }

  static Future<TdbrTadabor?> getMostSharedTadabor(
    int suraNum,
    int verseNum,
  ) async {
    var watch = Stopwatch()..start;
    var data = (await (await briteDB)?.rawQuery(
      'SELECT B.* FROM ( SELECT A.tdbr_id FROM tdbr_ayat AS A WHERE A.ayah_num = $verseNum AND A.sora_num = $suraNum ) AS filtered_ayat JOIN tdbr_tadabor AS B ON filtered_ayat.tdbr_id = B.id JOIN ( SELECT item_id, COUNT(*) AS item_count FROM shares WHERE share_table="tadabor" GROUP BY item_id ) AS S ON S.item_id = B.id WHERE B.status=1 ORDER BY S.item_count DESC LIMIT 1;',
    ));

    watch.stop();
    print("getMostSharedTadabor took ${watch.elapsedMilliseconds}ms");

    if (data?.first != null) {
      TdbrTadabor tdbTadabor = TdbrTadabor.fromJson(data!.first);
      tdbTadabor.verseNumber = verseNum;
      tdbTadabor.suraNumber = suraNum;

      return tdbTadabor;
    }
    return null;
  }

  static Future<List<TdbrEloquence>> getTdbEloquence(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_eloquence");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_eloquence where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrEloquence tdbrEloquence = TdbrEloquence.fromJson(e);
          tdbrEloquence.verseNumber = verseNum;
          tdbrEloquence.suraNumber = suraNum;
          return tdbrEloquence;
        }).toList() ??
        [];
  }

  static Future<List<TdbrConsider>> getTdbConsider(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_consider");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_consider where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrConsider tdbrConsider = TdbrConsider.fromJson(e);
          tdbrConsider.verseNumber = verseNum;
          tdbrConsider.suraNumber = suraNum;
          return tdbrConsider;
        }).toList() ??
        [];
  }

  static Future<List<TdbrRule>> getTdbRule(int suraNum, int verseNum) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_rules");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_rules where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrRule tdbrConsider = TdbrRule.fromJson(e);
          tdbrConsider.verseNumber = verseNum;
          tdbrConsider.suraNumber = suraNum;
          return tdbrConsider;
        }).toList() ??
        [];
  }

  static Future<List<TdbrSuggest>> getTdbSuggest(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_suggest");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_suggest where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrSuggest tdbrSuggest = TdbrSuggest.fromJson(e);
          tdbrSuggest.verseNumber = verseNum;
          tdbrSuggest.suraNumber = suraNum;
          return tdbrSuggest;
        }).toList() ??
        [];
  }

  static Future<List<TdbrPray>> getTdbPray(int suraNum, int verseNum) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_pray");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_rules where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrPray tdbrpray = TdbrPray.fromJson(e);
          tdbrpray.verseNumber = verseNum;
          tdbrpray.suraNumber = suraNum;
          return tdbrpray;
        }).toList() ??
        [];
  }

  static Future<List<TdbrMedia>> getTdbMedia(int suraNum, int verseNum) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_media");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_media where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrMedia tdbrMedia = TdbrMedia.fromJson(e);
          tdbrMedia.verseNumber = verseNum;
          tdbrMedia.suraNumber = suraNum;
          return tdbrMedia;
        }).toList() ??
        [];
  }

  static Future<List<TdbrComparable>> getTdbComparable(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_comparable");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_comparable where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrComparable tdbrComparable = TdbrComparable.fromJson(e);
          tdbrComparable.verseNumber = verseNum;
          tdbrComparable.suraNumber = suraNum;
          return tdbrComparable;
        }).toList() ??
        [];
  }

  static Future<List<TdbrQuestion>> getTdbQuestions(
    int suraNum,
    int verseNum,
  ) async {
    List<int> tdbrIds = await getTdbAyat(suraNum, verseNum, "tdbr_questions");

    return (await (await briteDB)?.rawQuery(
          "select * from tdbr_questions where id in (${tdbrIds.join(',')}) and status = 1 ",
        ))?.map((e) {
          TdbrQuestion tdbrQuestion = TdbrQuestion.fromJson(e);
          tdbrQuestion.verseNumber = verseNum;
          tdbrQuestion.suraNumber = suraNum;
          return tdbrQuestion;
        }).toList() ??
        [];
  }

  static Future<List<TdbrLink>> getTdbrsLinks(
    List<int> tdbrIds,
    String tdbrType,
  ) async {
    // stopwatch

    Stopwatch stopwatch = Stopwatch()..start();

    var links =
        (await (await briteDB)?.rawQuery(
          "select * from tdbr_links where tdbr_id in (${tdbrIds.join(',')}) and tdbr_type=?",
          [tdbrType],
        ))?.map((e) => TdbrLink.fromJson(e)).toList() ??
        [];

    stopwatch.stop();
    // print("getTdbrLinks: $tdbrId: $tdbrType ${stopwatch.elapsedMilliseconds}");
    return links;
  }

  static Future<List<Like>> getLikes(List<int> tdbrIds, String tdbrType) async {
    // stopwatch

    Stopwatch stopwatch = Stopwatch()..start();

    var likes =
        (await (await briteDB)?.rawQuery(
          "select * from likes where item_id in (${tdbrIds.join(',')}) and like_table=?",
          [tdbrType],
        ))?.map((e) => Like.fromJson(e)).toList() ??
        [];

    stopwatch.stop();
    // print("getTdbrLinks: $tdbrId: $tdbrType ${stopwatch.elapsedMilliseconds}");
    return likes;
  }

  static Future<List<Share>> getShares(
    List<int> tdbrIds,
    String tdbrType,
  ) async {
    // stopwatch

    Stopwatch stopwatch = Stopwatch()..start();

    var shares =
        (await (await briteDB)?.rawQuery(
          "select * from shares where item_id in (${tdbrIds.join(',')}) and share_table=?",
          [tdbrType],
        ))?.map((e) => Share.fromJson(e)).toList() ??
        [];

    stopwatch.stop();
    // print("getTdbrLinks: $tdbrId: $tdbrType ${stopwatch.elapsedMilliseconds}");
    return shares;
  }

  static Future<List<Share>> getTdbrSource(
    List<int> tdbrIds,
    String tdbrType,
  ) async {
    // stopwatch

    Stopwatch stopwatch = Stopwatch()..start();

    var shares =
        (await (await briteDB)?.rawQuery(
          "select * from shares where item_id in (${tdbrIds.join(',')}) and share_table=?",
          [tdbrType],
        ))?.map((e) => Share.fromJson(e)).toList() ??
        [];

    stopwatch.stop();
    // print("getTdbrLinks: $tdbrId: $tdbrType ${stopwatch.elapsedMilliseconds}");
    return shares;
  }

  static Future<List<TdbrSource>> getTdbrsSources(
    List<int> tdbrSourceIds,
  ) async {
    var sources =
        (await (await briteDB)?.rawQuery(
          "select * from tdbr_sources where id in (${tdbrSourceIds.join(',')}) and status=1",
        ))?.map((e) => TdbrSource.fromJson(e)).toList() ??
        [];
    return sources;
  }

  // static Future<List<Map<String, dynamic>>> getSourcesUiLayer() async {
  //   var sources = ((await briteDB)?.rawQuery(
  //     "select id,name from tdbr_sources where status=1",
  //   )) as Future<List<Map<String, dynamic>>>;

  //   return sources;
  // }
  static Future<List<Map<String, Object?>>?> getSources() async {
    var sources = await ((await briteDB)?.rawQuery(
      "select id,name from tdbr_sources where status=1 order by name",
    ));

    return sources;
  }

  // get last tadabor ids
  static Future<SectionsWithNewDataVm> getLastIds() async {
    SectionsWithNewDataVm sectionsWithNewData = SectionsWithNewDataVm();
    // sync ,likes,shares,tdbr_ayat,tdbr_comparable,tdbr_consider, tdbr_eloquence,tdbr_links,tdbr_media,tdbr_pray,tdbr_questions,tdbr_rules,tdbr_sources,tdbr_suggest,tdbr_tadabor,tdbr_tadabor_cats,users
    sectionsWithNewData.sync = await getLastId('sync');
    sectionsWithNewData.likes = await getLastId('likes');
    sectionsWithNewData.shares = await getLastId('shares');
    sectionsWithNewData.tdbrVerses = await getLastId('tdbr_ayat');
    sectionsWithNewData.tdbrComparables = await getLastId('tdbr_comparable');
    sectionsWithNewData.tdbrConsiders = await getLastId('tdbr_consider');
    sectionsWithNewData.tdbrEloquences = await getLastId('tdbr_eloquence');
    sectionsWithNewData.tdbrLinks = await getLastId('tdbr_links');
    sectionsWithNewData.tdbrMedia = await getLastId('tdbr_media');
    sectionsWithNewData.tdbrPrays = await getLastId('tdbr_pray');
    sectionsWithNewData.tdbrQuestions = await getLastId('tdbr_questions');
    sectionsWithNewData.tdbrRules = await getLastId('tdbr_rules');
    sectionsWithNewData.tdbrSources = await getLastId('tdbr_sources');
    sectionsWithNewData.tdbrSuggests = await getLastId('tdbr_suggest');
    sectionsWithNewData.tdbrTadabors = await getLastId('tdbr_tadabor');
    sectionsWithNewData.tdbrTadaborCategories = await getLastId(
      'tdbr_tadabor_cats',
    );
    sectionsWithNewData.users = await getLastId('users');

    return sectionsWithNewData;
  }

  static Future<int> getLastId(String table) async {
    List<Map<String, Object?>>? lastId = await ((await briteDB)?.rawQuery(
      "select max(id) as max_id from $table",
    ));
    return lastId?.first["max_id"] as int? ?? 0;
  }

  // insert into table
  static Future<void> insertIntoTable(
    String table,
    List<Map<String, dynamic>> data,
  ) async {
    await (await briteDB)?.transaction((txn) async {
      var batch = txn.batch();
      for (var element in data) {
        batch.insert(
          table,
          element,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      await batch.commit(noResult: true);
    });
  }

  // update table
  static Future<void> updateTable(
    String table,
    List<Map<String, dynamic>> data,
  ) async {
    await (await briteDB)?.transaction((txn) async {
      var batch = txn.batch();
      for (var element in data) {
        batch.update(
          table,
          element,
          where: ' id = ?',
          whereArgs: [element['id']],
        );
      }
      await batch.commit(noResult: true);
    });
  }

  // update tdbr status
  static Future<void> updateTdbrStatus(String table, int id, int status) async {
    await (await briteDB)?.update(
      table,
      {'status': status},
      where: ' id = ?',
      whereArgs: [id],
    );
  }

  // delete tdbr
  static Future<void> deleteTdbr(String table, int id) async {
    await (await briteDB)?.delete(table, where: 'id = ?', whereArgs: [id]);
  }

  // getTdbrByIds
  static Future<List<Map<String, dynamic>>> getTdbrByIds(
    List<int> ids,
    TdbrType tdbrType,
  ) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    var tdbrTableType = TadarsConstants.tdbrMapping[tdbrType];
    if (tableName == null) return [];
    var tdbrs =
        (await (await briteDB)?.rawQuery(
          "select $tableName.* , tdbr_sources.name as source_name , tdbr_sources.details as source_details ,(SELECT count(shares.id)  FROM shares WHERE shares.item_id = $tableName.id AND shares.share_table = '$tdbrTableType'  ) AS shares from ($tableName INNER JOIN  tdbr_sources ON $tableName.source = tdbr_sources.id)   where  $tableName.id in (${ids.join(',')}) and  $tableName.status=1  ORDER BY id DESC",
        ))?.toList() ??
        [];
    var tdbrLinks = await getTdbrLinksByIds(ids, tdbrType);
    print(tdbrLinks.length);
    // loop with for index
    List<Map<String, dynamic>> newTdbrs = [];
    for (var i = 0; i < tdbrs.length; i++) {
      var tdbr = Map.of(tdbrs[i]);
      var tdbrId = tdbr['id'];
      var tdbrLinksForTdbr =
          tdbrLinks.where((element) => element['tdbr_id'] == tdbrId).toList();
      tdbr['links'] = tdbrLinksForTdbr;
      newTdbrs.add(tdbr);
    }
    return newTdbrs;
  }

  static Future<Map<String, dynamic>> getTdbrById(
    int id,
    TdbrType tdbrType,
  ) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    var tdbrTableType = TadarsConstants.tdbrMapping[tdbrType];
    if (tableName == null) return {};
    var tdbrs =
        (await (await briteDB)?.rawQuery(
          "select $tableName.* , tdbr_sources.name as source_name , tdbr_sources.details as source_details ,(SELECT count(shares.id)  FROM shares WHERE shares.item_id = $tableName.id AND shares.share_table = '$tdbrTableType'  ) AS shares from ($tableName INNER JOIN  tdbr_sources ON $tableName.source = tdbr_sources.id)   where  $tableName.id = $id and  $tableName.status=1  ORDER BY id DESC",
        ))?.toList() ??
        [];
    var tdbrLinks = await getTdbrLinksByIds([id], tdbrType);
    var tdbrVerses = await getTdbrVersesByIds([id], tdbrType);
    // loop with for index
    List<Map<String, dynamic>> newTdbrs = [];
    for (var i = 0; i < tdbrs.length; i++) {
      var tdbr = Map.of(tdbrs[i]);
      var tdbrId = tdbr['id'];
      var tdbrLinksForTdbr =
          tdbrLinks.where((element) => element['tdbr_id'] == tdbrId).toList();
      var tdbrVersesForTdbr =
          tdbrVerses.where((element) => element['tdbr_id'] == tdbrId).toList();
      tdbr['links'] = tdbrLinksForTdbr;
      tdbr['verses'] = tdbrVersesForTdbr;
      print(tdbr);
      newTdbrs.add(tdbr);
    }
    return newTdbrs.first;
  }

  // get tdbr links by id
  static Future<List<Map<String, dynamic>>> getTdbrLinksByIds(
    List<int> ids,
    TdbrType tdbrType,
  ) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    if (tableName == null) return [];
    var links =
        (await (await briteDB)?.rawQuery(
          "select * from tdbr_links where tdbr_id in (${ids.join(',')}) and tdbr_type='$tableName'",
        ))?.toList() ??
        [];

    return links;
  }

  // get tdbr verses
  static Future<List<Map<String, dynamic>>> getTdbrVerses(
    int id,
    TdbrType tdbrType,
  ) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    if (tableName == null) return [];
    var verses =
        (await (await briteDB)?.rawQuery(
          "select ayah_num, sora_num from tdbr_ayat where tdbr_id = $id AND tdbr_type='$tableName'",
        ))?.toList() ??
        [];
    return verses;
  }

  static Future<List<Map<String, dynamic>>> getTdbrVersesByIds(
    List<int> ids,
    TdbrType tdbrType,
  ) async {
    var stopwatch = Stopwatch();
    stopwatch.start();
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    if (tableName == null) return [];
    var verses =
        (await (await briteDB)?.rawQuery(
          "select ayah_num, sora_num,tdbr_id from tdbr_ayat where tdbr_id in (${ids.join(',')}) AND tdbr_type='$tableName'",
        ))?.toList() ??
        [];
    print('getTdbrVersesByIds take: ${stopwatch.elapsed.inMilliseconds}');
    return verses;
  }

  static Future<List<int>> getVerseTdbrIds(
    int suraNumber,
    int verseNumber,
    TdbrType tdbrType,
  ) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    return (await (await briteDB)?.rawQuery(
          "select tdbr_id from tdbr_ayat where sora_num = ? and ayah_num = ? and tdbr_type = ?",
          [suraNumber, verseNumber, tableName],
        ))?.map<int>((e) => e['tdbr_id'] as int? ?? 0).toList() ??
        [];
  }

  // get verse tdbrs
  static Future<List<Map<String, dynamic>>> getVerseTdbrs(
    int suraNumber,
    int verseNumber,
    TdbrType tdbrType,
  ) async {
    var tdbrIds = await getVerseTdbrIds(suraNumber, verseNumber, tdbrType);
    if (tdbrIds.isEmpty) return [];
    var tdbrs = await getTdbrByIds(tdbrIds, tdbrType);
    return tdbrs;
  }

  // get all tdbrs with pagination
  static Future<Map<String, dynamic>> getAllTdbrs(
    TdbrType tdbrType,
    int page, [
    int limit = 50,
  ]) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    var tdbrTableType = TadarsConstants.tdbrMapping[tdbrType];
    if (tableName == null) return {};
    var tdbrs =
        (await (await briteDB)?.rawQuery(
          "select $tableName.* , tdbr_sources.name as source_name , tdbr_sources.details as source_details ,(SELECT count(shares.id)  FROM shares WHERE shares.item_id = $tableName.id AND shares.share_table = '$tdbrTableType'  ) AS shares from ($tableName INNER JOIN  tdbr_sources ON $tableName.source = tdbr_sources.id)   where  $tableName.status=1  ORDER BY id DESC LIMIT $limit OFFSET ${(page - 1) * limit}",
        ))?.toList() ??
        [];
    var tdbrsIds = tdbrs.map<int>((e) => e['id'] as int? ?? 0).toList();
    var tdbrLinks = await getTdbrLinksByIds(tdbrsIds, tdbrType);
    print(tdbrLinks.length);
    var tdbrVerses = await getTdbrVersesByIds(tdbrsIds, tdbrType);
    print(tdbrVerses.length);
    // loop with for index
    List<Map<String, dynamic>> newTdbrs = [];
    for (var i = 0; i < tdbrs.length; i++) {
      var tdbr = Map.of(tdbrs[i]);
      var tdbrId = tdbr['id'];
      var tdbrLinksForTdbr =
          tdbrLinks.where((element) => element['tdbr_id'] == tdbrId).toList();
      var tdbrVersesForTdbr =
          tdbrVerses.where((element) => element['tdbr_id'] == tdbrId).toList();
      tdbr['links'] = tdbrLinksForTdbr;
      tdbr['verses'] = tdbrVersesForTdbr;
      print(tdbr);
      newTdbrs.add(tdbr);
    }
    var count =
        (await (await briteDB)?.rawQuery(
          "select count(*) as count from $tableName where status=1",
        ))?.first['count'] ??
        0;
    return {
      'tdbrs': newTdbrs,
      'count': count,
      'pages_count': (int.parse(count.toString()) / limit).floor(),
    };
  }

  // get source tdbrs with pagination
  static Future<Map<String, dynamic>> getSourceTdbrs(
    int sourceId,
    TdbrType tdbrType,
    int page, [
    int limit = 50,
  ]) async {
    var tableName = TadarsConstants.tdbrMappingForDb[tdbrType];
    var tdbrTableType = TadarsConstants.tdbrMapping[tdbrType];
    if (tableName == null) return {};
    var tdbrs =
        (await (await briteDB)?.rawQuery(
          "select $tableName.* , tdbr_sources.name as source_name , tdbr_sources.details as source_details ,(SELECT count(shares.id)  FROM shares WHERE shares.item_id = $tableName.id AND shares.share_table = '$tdbrTableType'  ) AS shares from ($tableName INNER JOIN  tdbr_sources ON $tableName.source = tdbr_sources.id)   where  $tableName.status=1 AND $tableName.source=$sourceId ORDER BY id DESC LIMIT $limit OFFSET ${(page - 1) * limit}",
        ))?.toList() ??
        [];
    // var tdbrs = (await (await briteDB)?.rawQuery(
    //       "select  $tableName.* , tdbr_sources.name as source_name , tdbr_sources.details as source_details ,(SELECT count(shares.id)  FROM shares WHERE shares.item_id = $tableName.id AND shares.share_table = '$tdbrTableType'  ) AS shares from ($tableName  JOIN  tdbr_sources ON $tableName.source = tdbr_sources.id)   where  $tableName.status=1 AND $tableName.source=$sourceId ORDER BY id DESC ",
    //     ))
    //         ?.toList() ??
    //     [];
    print(tdbrs.length);
    var tdbrsIds = tdbrs.map<int>((e) => e['id'] as int? ?? 0).toList();
    var tdbrLinks = await getTdbrLinksByIds(tdbrsIds, tdbrType);
    var tdbrVerses = await getTdbrVersesByIds(tdbrsIds, tdbrType);
    print(tdbrVerses.length);
    // loop with for index
    List<Map<String, dynamic>> newTdbrs = [];
    for (var i = 0; i < tdbrs.length; i++) {
      var tdbr = Map.of(tdbrs[i]);
      var tdbrId = tdbr['id'];
      var tdbrLinksForTdbr =
          tdbrLinks.where((element) => element['tdbr_id'] == tdbrId).toList();
      var tdbrVersesForTdbr =
          tdbrVerses.where((element) => element['tdbr_id'] == tdbrId).toList();
      tdbr['links'] = tdbrLinksForTdbr;
      tdbr['verses'] = tdbrVersesForTdbr;
      print(tdbr);
      newTdbrs.add(tdbr);
    }

    var count =
        (await (await briteDB)?.rawQuery(
          "select count(*) as count from $tableName where status=1 AND source=$sourceId",
        ))?.first['count'] ??
        0;
    print('len:  $count');
    return {
      'tdbrs': newTdbrs,
      'count': count,
      'pages_count': (int.parse(count.toString()) / limit).ceil(),
    };
  }

  static Future<Map<TdbrType, int>> getSource(int sourceId) async {
    var stopwatch = Stopwatch()..start();
    final Map<TdbrType, int> counts = {};
    for (var key in TadarsConstants.tdbrMappingForDb.keys) {
      String tableName = TadarsConstants.tdbrMappingForDb[key] ?? "";
      var count =
          (await (await briteDB)?.rawQuery(
            "select count(*) as count from $tableName where status=1 AND source=$sourceId",
          ))?.first['count'] ??
          0;
      counts[key] = int.parse(count.toString());
    }
    print("source tdbr take :  ${stopwatch.elapsedMilliseconds}");
    print(counts);
    return counts;
  }

  static Future<Map<TdbrType, int>> getAllTadbrsCounts() async {
    var stopwatch = Stopwatch()..start();
    final Map<TdbrType, int> counts = {};
    for (var key in TadarsConstants.tdbrMappingForDb.keys) {
      String tableName = TadarsConstants.tdbrMappingForDb[key] ?? "";
      var count =
          (await (await briteDB)?.rawQuery(
            "select count(*) as count from $tableName where status=1",
          ))?.first['count'] ??
          0;
      counts[key] = int.parse(count.toString());
    }
    print("source tdbr take :  ${stopwatch.elapsedMilliseconds}");
    print(counts);
    return counts;
  }

  // get page tdbrs count
  static Future<Map> getAllTdbrsCount() async {
    // stop watch
    var stopwatch = Stopwatch()..start();
    var counts =
        (await (await briteDB)?.rawQuery(
          "select sora_num,ayah_num, count(*) as count from tdbr_ayat where status = 0  group by  sora_num,ayah_num ",
        ))?.toList() ??
        [];
    var temp = {
      for (var e in counts)
        "${e['sora_num'].toString().padLeft(3, '0')}_${e['ayah_num'].toString().padLeft(3, '0')}":
            e['count'],
    };
    stopwatch.stop();
    print('get page tdbrs count time ${stopwatch.elapsedMilliseconds}');

    print(temp);

    return temp;
  }

  static Future<List<Map<String, dynamic>>> getSourcesByIds(
    List<int> ids,
  ) async {
    var sources =
        (await (await briteDB)?.rawQuery(
          "select *  FROM tdbr_sources  where  id in (${ids.join(',')}) and  status=1 ",
        ))?.toList() ??
        [];
    return sources;
  }

  static Future<List<Map<String, dynamic>>> getBookmarkedSources() async {
    return await getSourcesByIds(BoxesHelper.getSourceBookmarksIds());
  }

  static Future<Map<TdbrType, Map<String, dynamic>>> searchInTdbr(
    String searchKey,
  ) async {
    var stopwatch = Stopwatch()..start();
    final Map<TdbrType, Map<String, dynamic>> results = {};
    for (var key in TadarsConstants.tdbrMappingForDb.keys) {
      String tableName = TadarsConstants.tdbrMappingForDb[key] ?? "";
      String searchColumnName = "fulltext";
      if (key == TdbrType.media) {
        searchColumnName = "details LIKE '%$searchKey%' AND title";
      }
      var count =
          (await (await briteDB)?.rawQuery(
            "select count(*) as count from $tableName where status=1 AND $searchColumnName LIKE '%$searchKey%'",
          ))?.first['count'] ??
          0;
      var result = (await (await briteDB)?.rawQuery(
        "select *  from $tableName where status=1 AND $searchColumnName LIKE '%$searchKey%'",
      ));
      results[key] = {"count": int.parse(count.toString()), "result": result};
    }
    print("source tdbr take :  ${stopwatch.elapsedMilliseconds}");
    print(results);
    return results;
  }

  // get translation books
  // static Future<List<TranslationBook>> getTranslationBooks() async {
  //   var books = (await (await briteDB)?.query(
  //         "translation_books",
  //         orderBy: "sort ASC",
  //       ))
  //           ?.toList() ??
  //       [];
  //   print(books);
  //   return books.map((e) => TranslationBook.fromJson(e)).toList();
  // }
}
