// import 'dart:io';

// import 'package:audio_service/audio_service.dart';
// import 'package:audio_session/audio_session.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/foundation.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:just_audio/just_audio.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:tadars/helpers/boxes_helper.dart';
// import 'package:tadars/helpers/reciter_sql_helper.dart';
// import 'package:tadars/models/hive/quran_sura.dart';
// import 'package:tadars/models/hive/quran_timing.dart';
// import 'package:tadars/services/network_service.dart';
// import 'package:tadars/utils/constants/boxes.dart';

// class AudioPlayerHandler extends BaseAudioHandler
//     with <PERSON><PERSON><PERSON><PERSON><PERSON>, SeekHandler {
//   AudioPlayerHandler(int? _loopCount, int? _reciterId) {
//     try {
//       init().then((_) {
//         loopCount = _loopCount ?? 1;
//         reciterId = _reciterId ?? 1;
//         AudioSession.instance.then((session) {
//           session.configure(const AudioSessionConfiguration.music());
//         });
//         initReciter().then((value) {
//           _player.playbackEventStream.map(_transformEvent).pipe(playbackState);
//           _player.positionStream.listen(handlePositionStream);
//           _player.currentIndexStream.distinct().listen(
//             (index) async {
//               if (index != null) {
//                 handleCurrentIndexStream(index);
//               }
//             },
//           );
//         });
//       });
//     } catch (e) {
//       if (kDebugMode) {
//         print(e);
//       }
//     }
//   }
//   final _player = AudioPlayer();
//   int reciterId = 0;
//   int suraNumber = 0;
//   int _verseNumber = 1;
//   set verseNumber(int value) {
//     if (value == 1) {
//       firstVerse = true;
//     } else {
//       firstVerse = false;
//     }
//     _verseNumber = value;
//   }

//   Duration duration = Duration.zero;
//   QuranTiming? timing;
//   List<MediaItem> q = [];
//   bool onlyVerse = false;
//   bool firstVerse = false;
//   bool changeVerse = true;
//   int loopCount = 1;
//   int currentLoop = 1;
//   int get verseNumber => _verseNumber;

//   @override
//   Future<void> play() async {
//     try {
//       await _player.play();
//     } catch (e) {
//       if (kDebugMode) {
//         print("play error: $e");
//       }
//     }
//   }

//   @override
//   Future<void> pause() async => await _player.pause();
//   @override
//   Future<void> stop() async {
//     try {
//       await _player.stop();
//     } catch (e) {
//       if (kDebugMode) {
//         print("error: $e");
//       }
//     }
//   }

//   @override
//   Future<void> seek(Duration position) async {
//     try {
//       print('sura number: $suraNumber');
//       await _player.seek(position, index: suraNumber - 1);
//       print('after seek: $suraNumber');
//     } catch (e) {
//       if (kDebugMode) {
//         print("seek error: $e");
//       }
//       customEvent.add(
//         {
//           "type": "onSeekErrorEvent",
//           "args": {
//             "error": e,
//           }
//         },
//       );
//     }
//   }

//   @override
//   Future<void> skipToNext() async {
//     await _player.seekToNext();
//   }

//   @override
//   Future<void> skipToPrevious() async {
//     await _player.seekToPrevious();
//   }

//   @override
//   Future<void> fastForward() async {
//     goToNextVerse();
//   }

//   @override
//   Future<void> rewind() async {
//     goToPreviousVerse();
//   }

//   @override
//   Future<dynamic> customAction(String name, [Map<String, dynamic>? extras]) {
//     switch (name) {
//       case "playVerse":
//         onlyVerse = true;
//         print('playVerse');
//         print(extras);
//         playVerse(extras!["sura_number"], extras["verse_number"]);
//         break;
//       case "playFromVerse":
//         onlyVerse = false;
//         playVerse(extras!["sura_number"], extras["verse_number"]);
//         break;
//       case "download":
//         downloadFile(suraNumber);
//         break;
//       case "setLoopCount":
//         // print("setLoopCount");
//         loopCount = extras!["count"];
//         break;
//       case "setReciterId":
//         if (extras?["id"] != reciterId) {
//           setReciter(extras?["id"]);
//         }
//         break;
//       default:
//     }
//     return super.customAction(name);
//   }

//   Future<void> goToNextVerse() async {
//     var mediaItem = q[suraNumber - 1];
//     if (mediaItem.extras != null) {
//       if (verseNumber >= mediaItem.extras!["last_verse"]) {
//         goToNextSura();
//       } else {
//         verseNumber++;
//         var temp = await getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem2(suraNumber, verseNumber);
//           await seek(Duration(milliseconds: (timing?.startAt.toInt() ?? 0)));
//         }
//       }
//     }
//   }

//   Future<void> goToNextSura() async {
//     // print("goToNextSura");
//     await skipToNext();
//   }

//   Future<void> goToPreviousSura() async {
//     // print("goToPreviosSura");
//     await skipToPrevious();
//   }

//   Future<void> goToPreviousVerse() async {
//     var mediaItem = q[suraNumber - 1];
//     if (mediaItem.extras != null) {
//       if (verseNumber <= 1) {
//         changeVerse = false;
//         await goToPreviousSura();
//         var mi = q[suraNumber - 1];
//         verseNumber = mi.extras!["last_verse"];
//         var temp = await getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem2(suraNumber, verseNumber);
//           await seek(Duration(milliseconds: (timing?.startAt.toInt() ?? 0)));
//         }
//       } else {
//         verseNumber--;
//         var temp = await getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem2(suraNumber, verseNumber);
//           await seek(Duration(milliseconds: (timing?.startAt.toInt() ?? 0)));
//         }
//       }
//     }
//   }

//   Future<void> updateMediaItem2(int suraNumber, int verseNumber) async {
//     var mi = q[suraNumber - 1];
//     mediaItem.add(
//       MediaItem(
//         id: mi.id,
//         artist: mi.artist,
//         album: mi.album,
//         displayTitle: mi.album,
//         displaySubtitle: "الآية ($verseNumber)",
//         displayDescription: mi.artist,
//         title: mi.title,
//         duration: mi.duration,
//         extras: mi.extras,
//       ),
//     );
//     customEvent.add({
//       "type": "versePosition",
//       "args": {
//         "sura_number": suraNumber,
//         "verse_number": verseNumber,
//       }
//     });
//   }

//   Future<void> downloadFile(int suraNumber) async {
//     try {} catch (e) {}
//     String url =
//         "${(await ReciterSqlHelper.getReciterById(reciterId))!.filesUrl}${getFilesUrlSuffix(reciterId, suraNumber)}${suraNumber.toString().padLeft(3, "0")}.mp3";
//     var downloadDirectory = await getApplicationDocumentsDirectory();
//     var savePath =
//         "${downloadDirectory.path}/$reciterId/${suraNumber.toString().padLeft(3, "0")}.mp3";
//     NetworkServices.instance.checkConnectivity(() {
//       Dio().download(url, savePath, onReceiveProgress: (progress, total) {
//         customEvent.add({
//           "type": "downloadEvent",
//           "args": {
//             "progress": progress,
//             "total": total,
//             "id": url,
//           }
//         });
//       }, deleteOnError: true).catchError((error) {
//         customEvent.add({
//           "type": "downloadErrorEvent",
//           "args": {
//             "error": error,
//             "id": url,
//           }
//         });
//       }).then((value) async {
//         await BoxesHelper.addFileToDownloads(url, savePath);
//         await setAudioSource(suraNumber);
//         customEvent.add({
//           "type": "downloadCompletedEvent",
//           "args": {
//             "id": url,
//           }
//         });
//       });
//     }, () {
//       customEvent.add({
//         "type": "connectionErrorEvent",
//       });
//     });
//   }

//   void handleLoop() {
//     // print("loopCount $loopCount");
//     // print("currentLoop $currentLoop");
//   }

//   String getFilesUrlSuffix(int reciterId, int suraNumber) {
//     var fileUrl = "";
//     if (reciterId == 3) {
//       switch (suraNumber) {
//         case 2:
//         case 27:
//         case 28:
//         case 58:
//           fileUrl = "replaced/";
//           break;
//         default:
//       }
//     }
//     return fileUrl;
//   }

//   Future<void> setAudioSource(int suraNumber) async {
//     try {
//       await _player.setAudioSource(
//         ConcatenatingAudioSource(
//           children: q.map((item) {
//             var file = BoxesHelper.getDownloadedFile(item.id);
//             if (file != null) {
//               // print("file Path ${file.filePath}");

//               return AudioSource.uri(File(file.filePath).uri);
//             }
//             // print("file source ${item.id}");
//             return AudioSource.uri(Uri.parse(item.id));
//           }).toList(),
//           useLazyPreparation: false,
//         ),
//         initialIndex: suraNumber - 1,
//         initialPosition: Duration(seconds: (timing?.startAt ?? 0).toInt()),
//         preload: false,
//       );
//     } catch (e) {
//       // print("Error: $e");
//       customEvent.add(
//         {
//           "type": "setAudioSourceErrorEvent",
//           "args": {
//             "error": e,
//           }
//         },
//       );
//       stop();
//     }
//   }

//   Future<void> handleCurrentIndexStream(int index) async {
//     suraNumber = index + 1;
//     print("ChangeVerse $changeVerse");
//     if (changeVerse) {
//       verseNumber = 1;
//     } else {
//       changeVerse = true;
//     }
//     print("_player.currentIndexStream index = $index");
//     print("_player.currentIndexStream verse = $verseNumber");
//     print("_player.currentIndexStream sura = $suraNumber");
//     var mi = q[index];
//     print("aymen ${mi.id}");

//     mediaItem.add(
//       MediaItem(
//         id: mi.id,
//         artist: mi.artist,
//         album: mi.album,
//         displayTitle: mi.album,
//         displaySubtitle: "الآية ($verseNumber)",
//         displayDescription: mi.artist,
//         title: mi.title,
//         duration: mi.duration,
//         extras: mi.extras,
//       ),
//     );
//     print("aymen ${mi.duration}");
//   }

//   Future<void> handlePositionStream(Duration position) async {
//     print(position);
//     if (timing != null) {
//       var end = (timing?.endAt ?? 0);
//       // print(position.inMilliseconds.toString() + " " + end.toString());
//       if (firstVerse) {
//         var temp = await getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem2(suraNumber, verseNumber);
//           firstVerse = false;
//         }
//       } else if (position.inMilliseconds >= end) {
//         handleLoop();
//         if (loopCount > 1 && currentLoop <= loopCount - 1) {
//           await seek(Duration(milliseconds: (timing?.startAt.toInt() ?? 0)));
//           if (loopCount != 5) {
//             currentLoop++;
//           }
//         } else if (onlyVerse) {
//           await seek(Duration(milliseconds: (timing?.startAt.toInt() ?? 0)));
//           pause();
//         } else {
//           verseNumber++;
//           var temp = await getVerseTiming(suraNumber, verseNumber);
//           print("getVerseTiming temp $temp");
//           if (temp != null) {
//             currentLoop = 1;
//             timing = temp;
//             updateMediaItem2(suraNumber, verseNumber);
//           } else {
//             changeVerse = true;
//           }
//         }
//       }
//     }
//   }

//   Future<void> playVerse(int suraNo, int verseNo) async {
//     changeVerse = false;
//     verseNumber = verseNo;
//     if (suraNumber != suraNo) {
//       suraNumber = suraNo;
//       mediaItem.add(q[suraNo - 1]);
//       await seek(
//         Duration.zero,
//       );
//     }
//     timing = await getVerseTiming(suraNo, verseNo);
//     print("time start at ");
//     print(timing?.startAt);
//     int startAt = ((timing?.startAt ?? 0)).toInt();
//     print(startAt);
//     await seek(
//       Duration(milliseconds: startAt),
//     );
//     await play();
    
//   }

//   Future<void> initReciter() async {
//     var suar = Hive.box<QuranSura>(Boxes.quranSuar);
//     var reciters = await ReciterSqlHelper.getAllReciters();
//     var reciter = reciters.where(($re) => $re.id == reciterId).first;

//     //TODO Delete this after converted to SQL
//     await Hive.openBox<QuranTiming>(Boxes.reciterTimings(reciterId));

//     //get soar durations
//     var durations = await ReciterSqlHelper.getSoarDuration(reciterId);
//     q = suar.values.map(
//       (e) {
//         var id =
//             '${reciter.filesUrl}${getFilesUrlSuffix(reciterId, e.id)}${e.id.toString().padLeft(3, "0")}.mp3';

//         var duration = durations?.isEmpty == true
//             ? 0
//             : ((int.tryParse(durations
//                         ?.firstWhere((element) =>
//                             element["sura_number"] == e.id)["duration"]
//                         ?.toString() ??
//                     "") ??
//                 0));

//         return MediaItem(
//           album: e.name,
//           artist: reciter.name,
//           id: id,
//           title: e.name,
//           extras: {
//             "sura_number": e.id,
//             "reciter_id": reciter.id,
//             "last_verse": e.versesCount,
//             "duration": duration.toInt()
//           },
//         );
//       },
//     ).toList();
//     queue.add(q);
//     try {
//       await _player.setAudioSource(
//         ConcatenatingAudioSource(
//           children: q.map((item) {
//             var file = BoxesHelper.getDownloadedFile(item.id);
//             if (file != null) {
//               print("file Path ${file.filePath}");

//               return AudioSource.uri(File(file.filePath).uri);
//             }
//             print("file source ${item.id}");
//             return AudioSource.uri(Uri.parse(item.id));
//           }).toList(),
//           useLazyPreparation: false,
//         ),
//         preload: false,
//       );
//     } catch (e) {
//       print("Error: $e");
//       customEvent.add(
//         {
//           "type": "setAudioSourceErrorEvent",
//           "args": {
//             "error": e,
//           }
//         },
//       );
//       stop();
//     }
//   }

//   Future<void> setReciter(int id) async {
//     try {
//       reciterId = id;
//       var tempSuraNumber = suraNumber;
//       var tempVerseNumber = verseNumber;
//       await stop();
//       await initReciter();
//       suraNumber = tempSuraNumber;
//       verseNumber = tempVerseNumber;
//       timing = await getVerseTiming(suraNumber, verseNumber);
//       print(timing?.startAt ?? "null");
//       int startAt = ((timing?.startAt ?? 0)).toInt();
//       await seek(
//         Duration(
//           milliseconds: startAt,
//         ),
//       );
//       await play();
//     } catch (e) {}
//   }

//   PlaybackState   _transformEvent(PlaybackEvent event) {
//     return PlaybackState(
//       controls: [
//         MediaControl.rewind,
//         if (_player.playing) MediaControl.pause else MediaControl.play,
//         MediaControl.stop,
//         MediaControl.fastForward,
//       ],
//       systemActions: const {
//         MediaAction.seek,
//         MediaAction.seekForward,
//         MediaAction.seekBackward,
//       },
//       androidCompactActionIndices: const [0, 1, 3],
//       processingState: const {
//         ProcessingState.idle: AudioProcessingState.idle,
//         ProcessingState.loading: AudioProcessingState.loading,
//         ProcessingState.buffering: AudioProcessingState.buffering,
//         ProcessingState.ready: AudioProcessingState.ready,
//         ProcessingState.completed: AudioProcessingState.completed,
//       }[_player.processingState]!,
//       playing: _player.playing,
//       updatePosition: _player.position,
//       bufferedPosition: _player.bufferedPosition,
//       speed: _player.speed,
//       queueIndex: event.currentIndex,
//     );
//   }

//   Future<void> init() async {
//     // var documentDirectory = await getApplicationDocumentsDirectory();
//     // await Hive.initFlutter(documentDirectory.path);
//     // Hive.registerAdapter(QuranSuraAdapter());
//     // Hive.registerAdapter(QuranTimingAdapter());
//     // Hive.registerAdapter(QuranReciterAdapter());
//     // Hive.registerAdapter(DownloadedFileAdapter());
//     // // await Hive.openBox<QuranSura>(Boxes.quranSuar);
//     // await Hive.openBox<QuranReciter>(Boxes.quranReciters);
//     // await Hive.openBox(Boxes.settings);
//     // await Hive.openBox<DownloadedFile>(Boxes.downloads);
//     // Get.lazyPut(() => NetworkService());
//   }

//   Future<QuranTiming?> getVerseTiming(int suraNo, int verseNo) async {
//     return await ReciterSqlHelper.getVerseTiming(
//         suraNo: suraNo, verseNo: verseNo, reciterId: reciterId);

//     // Hive.box<QuranTiming>(Boxes.reciterTimings(reciterId)).get(
//     //     "${suraNo.toString().padLeft(3, "0")}_${verseNo.toString().padLeft(3, "0")}");
//   }
// }
