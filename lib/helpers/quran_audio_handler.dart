import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

class QuranAudioHandler extends BaseAudioHandler
    with <PERSON><PERSON><PERSON><PERSON><PERSON>, SeekHandler {
  QuranAudioHandler({required this.loadPlayList, required this.reciterId}) {
    AudioSession.instance.then((session) {
      session.configure(const AudioSessionConfiguration.music());
    });
    _loadEmptyPlaylist();

    _player.playbackEventStream.listen(playbackEventStream);
    _player.currentIndexStream.distinct().listen(
      (index) async {
        if (index != null) {
          handleCurrentIndexStream(index);
        }
      },
    );
  }

  final _player = AudioPlayer();
  final _playlist = ConcatenatingAudioSource(children: []);

  final Function(int) loadPlayList;

  int _suraNumber = 0;
  int reciterId;
  int? _stopIndex;

  void _loadEmptyPlaylist() {
    try {
      _player.setAudioSource(_playlist);
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  @override
  Future<void> addQueueItems(List<MediaItem> mediaItems) async {
    // manage Just Audio
    final audioSource = mediaItems.map(_createAudioSource);
    _playlist.addAll(audioSource.toList());

    // notify system
    final newQueue = queue.value..addAll(mediaItems);
    queue.add(newQueue);
  }

  @override
  Future<void> addQueueItem(MediaItem mediaItem) async {
    // manage Just Audio
    final audioSource = _createAudioSource(mediaItem);
    _playlist.add(audioSource);

    // notify system
    final newQueue = queue.value..add(mediaItem);
    queue.add(newQueue);
  }

  Future<void> removeQueueItems() async {
    // manage Just Audio
    await _playlist.clear();

    // notify system
    queue.value.clear();
  }

  ClippingAudioSource _createAudioSource(MediaItem mediaItem) {
    if (mediaItem.extras?['source'] == 'file') {
      return ClippingAudioSource(
        child: AudioSource.file(
          mediaItem.extras!['url'] as String,
          tag: mediaItem,
        ),
        tag: mediaItem,
        start: mediaItem.extras?['start'],
        end: mediaItem.extras?['end'],
      );
    }
    return ClippingAudioSource(
      child: AudioSource.uri(
        Uri.parse(mediaItem.extras!['url'] as String),
        tag: mediaItem,
      ),
      tag: mediaItem,
      start: mediaItem.extras?['start'],
      end: mediaItem.extras?['end'],
    );
  }

  @override
  Future<void> removeQueueItemAt(int index) async {
    // manage Just Audio
    _playlist.removeAt(index);

    // notify system
    final newQueue = queue.value..removeAt(index);
    queue.add(newQueue);
  }

  @override
  Future<void> play() async {
    try {
      await _player.play();
    } catch (e) {
      if (kDebugMode) {
        print("play error: $e");
      }
    }
  }

  @override
  Future<void> pause() async => await _player.pause();
  @override
  Future<void> stop() async {
    try {
      await _player.stop();
    } catch (e) {
      if (kDebugMode) {
        print("error: $e");
      }
    }
  }

  @override
  Future<void> seek(Duration position, {int? index}) async {
    try {
      await _player.seek(position, index: index);
    } catch (e) {
      if (kDebugMode) {
        print("seek error: $e");
      }
      customEvent.add(
        {
          "type": "onSeekErrorEvent",
          "args": {
            "error": e,
          }
        },
      );
    }
  }

  @override
  Future<void> skipToNext() async {
    await _player.seekToNext();
  }

  @override
  Future<void> skipToPrevious() async {
    await _player.seekToPrevious();
  }

  @override
  Future<void> fastForward() async {
    goToNextVerse();
  }

  @override
  Future<void> rewind() async {
    goToPreviousVerse();
  }

  Future<void> goToNextVerse() async {
    await skipToNext();
  }

  Future<void> goToPreviousVerse() async {
    await skipToPrevious();
  }

  @override
  Future<void> skipToQueueItem(int index) async {
    await seek(Duration.zero, index: index);
  }

  @override
  Future<void> setSpeed(double speed) async {
    _player.setSpeed(speed);
  }

  Future<void> playVerse({
    required int suraNumber,
    required int verseNumber,
    int? toVerse,
    double? speed,
  }) async {
    if (_suraNumber != suraNumber) {
      await loadPlayList(suraNumber);
      _suraNumber = suraNumber;
    }

    _stopIndex = toVerse;

    if (speed != null) {
      setSpeed(speed);
    }

    var verseIndex = queue.value.indexWhere(
        (mediaItem) => mediaItem.extras?['verse_number'] == verseNumber);

    await skipToQueueItem(verseIndex);
    await play();
  }

  Future<void> handleCurrentIndexStream(int index) async {
    if (kDebugMode) {
      print("index $index");
    }
    if (_stopIndex != null && index >= _stopIndex!) {
      await stop();
      _stopIndex = null;
    }

    if (index > 0) {
      var verseNumber = queue.value[index].extras?['verse_number'];

      customEvent.add({
        "type": "verseChange",
        "args": {
          "sura_number": _suraNumber,
          "verse_number":
            verseNumber,
        }
      });
    }
  }

  playbackEventStream(PlaybackEvent event) {
    playbackState.add(PlaybackState(
      controls: [
        MediaControl.rewind,
        if (_player.playing) MediaControl.pause else MediaControl.play,
        MediaControl.stop,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 3],
      processingState: const {
        ProcessingState.idle: AudioProcessingState.idle,
        ProcessingState.loading: AudioProcessingState.loading,
        ProcessingState.buffering: AudioProcessingState.buffering,
        ProcessingState.ready: AudioProcessingState.ready,
        ProcessingState.completed: AudioProcessingState.completed,
      }[_player.processingState]!,
      playing: _player.playing,
      updatePosition: _player.position,
      bufferedPosition: _player.bufferedPosition,
      speed: _player.speed,
      queueIndex: event.currentIndex,
    ));
  }

//change reciter ID
  changeReciterId(int id) {
    reciterId = id;
    if (_suraNumber != 0) {
      loadPlayList(_suraNumber);
    }
  }
}
