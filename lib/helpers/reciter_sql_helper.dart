import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqlbrite/sqlbrite.dart';
import 'package:path/path.dart';
import 'package:tadars/models/hive/quran_reciter.dart';
import 'package:tadars/models/hive/quran_timing.dart';

class ReciterSqlHelper {
  static final ReciterSqlHelper reciterSqlHelper = ReciterSqlHelper._internal();

  ReciterSqlHelper._internal();

  factory ReciterSqlHelper() {
    return reciterSqlHelper;
  }

  static BriteDatabase? _db;

  static Future<BriteDatabase?> get db async {
    if (_db == null) {
      late Database db;
      var databasesPath = await getDatabasesPath();
      var path = join(databasesPath, "reciters.sqlite");
      db = await openDatabase(
        path,
      );

      _db = BriteDatabase(db);
    }
    return _db;
  }

  // insert into table
  static Future<void> insertIntoTable(
      String table, List<Map<String, dynamic>> data) async {
    await (await db)?.transaction((txn) async {
      var batch = txn.batch();
      for (var element in data) {
        batch.insert(table, element,
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
      await batch.commit(noResult: false);
    });
  }

  // update table
  static Future<void> updateTable(
      String table, List<Map<String, dynamic>> data) async {
    await (await db)?.transaction((txn) async {
      var batch = txn.batch();
      for (var element in data) {
        batch.update(table, element,
            where: ' id = ?', whereArgs: [element['id']]);
      }
      await batch.commit(noResult: true);
    });
  }

  // update table with specfic where
  static Future<void> updateTableWhere(String table, Map<String, dynamic> data,
      String where, List<Object?> whereArgs) async {
    await (await db)?.transaction((txn) async {
      var batch = txn.batch();

      batch.update(table, data, where: where, whereArgs: whereArgs);
      await batch.commit(noResult: true);
    });
  }

  // update or Create table
  // static Future<void> updateOrCreateTable(
  //     String table, List<Map<String, dynamic>> data) async {
  //   await (await db)?.transaction((txn) async {
  //     var batch = txn.batch();
  //     //update exists ids
  //     for (var element in data) {
  //       batch.update(table, element,
  //           where: ' id = ?', whereArgs: [element['id']]);
  //     }
  //     //insert the ids not exists
  //     var ids = data.map((e) => e["id"]);
  //     var result = await (await db)
  //         ?.query(table, where: "id NOT IN (?)", whereArgs: [ids]);

  //     await batch.commit(noResult: true);
  //   });
  // }

  // Reciters
  static Future<QuranReciter?> getReciterById(int id) async {
    var data =
        await (await db)!.query("reciters", where: "id= ?", whereArgs: [id]);

    return QuranReciter.fromJson(data.first);
  }

  static Future<bool> isReciterExist(int id) async {
    var data =
        await (await db)!.query("reciters", where: "id= ?", whereArgs: [id]);

    return data.isNotEmpty;
  }

  static Future<List<QuranReciter>> getAllReciters() async {
    var data = await (await db)!.query("reciters");

    var reciters = List.generate(
      data.length,
      (index) => QuranReciter.fromJson(data[index]),
    );
    return reciters;
  }

//get reciter verse timings
  static Future<QuranTiming?> getVerseTiming(
      {required int suraNo, required int verseNo, required reciterId}) async {
    debugPrint('resiter id $reciterId');
    var data = await (await db)!.query("timings",
        where: "reciter_id= ? and sura_number = ? and verse_number = ?",
        whereArgs: [reciterId, suraNo, verseNo]);

    print("data ${data.first}");
    return QuranTiming.fromJson(data.first);
  }

//get reciter sora timings
  static Future<List<QuranTiming>?> getSoraTimings(
      {required int suraNo, required, required reciterId}) async {
    debugPrint('resiter id $reciterId');
    var data = await (await db)!.query("timings",
        where: "reciter_id= ? and sura_number = ?",
        whereArgs: [reciterId, suraNo]);

    return data.map((e) => QuranTiming.fromJson(e)).toList();
  }

  //get soar  duration

  static Future<List<Map<String, Object?>>?> getSoarDuration(
      int reciterId) async {
    var data = await (await db)?.query("timings",
        columns: ["sura_number", "max(end_at) duration"],
        groupBy: "sura_number",
        where: "reciter_id = ?",
        whereArgs: [reciterId]);
    return data;
  }
}
