import 'package:shared_preferences/shared_preferences.dart';
import 'package:tadars/utils/constants/shared_prefrence.dart';

class SharedPrefrenceHelper {
  static SharedPreferencesAsync prefs = SharedPreferencesAsync();

  static setVerseNotficationEnable(bool value) {
    prefs.setBool(SharedPrefrenceConstans.verseNotficationEnable, value);
  }

  static Future<bool> getVerseNotficationEnable() async {
    return (await prefs.getBool(
          SharedPrefrenceConstans.verseNotficationEnable,
        )) ??
        false;
  }

  static setVerseNotficationDuration(int value) async{
   await prefs.setInt(SharedPrefrenceConstans.verseNotficationDuration, value);
  }

  static Future<int> getVerseNotficationDuration() async {
    return (await prefs.getInt(
          SharedPrefrenceConstans.verseNotficationDuration,
        )) ??
        2;
  }



  static Future<int> getLastVerseId() async {
    // Obtain shared preferences.
    return await prefs.getInt(SharedPrefrenceConstans.lastVerseId) ?? 0;
  }

  static Future<void> setLastVerseId(int? id) async {
    // Obtain shared preferences.
    prefs.setInt(SharedPrefrenceConstans.lastVerseId, id ?? 0);
  }

}
