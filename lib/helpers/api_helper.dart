import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart';
import 'package:tadars/models/add_tdbr_response.dart';
import 'package:tadars/models/hive/like.dart';
import 'package:tadars/models/hive/quran_book.dart';
import 'package:tadars/models/hive/quran_tafseer.dart';
import 'package:tadars/models/hive/share.dart';
import 'package:tadars/models/hive/tafseer.dart';
import 'package:tadars/models/hive/tdbr_comparable.dart';
import 'package:tadars/models/hive/tdbr_consider.dart';
import 'package:tadars/models/hive/tdbr_eloquence.dart';
import 'package:tadars/models/hive/tdbr_link.dart';
import 'package:tadars/models/hive/tdbr_media.dart';
import 'package:tadars/models/hive/tdbr_pray.dart';
import 'package:tadars/models/hive/tdbr_question.dart';
import 'package:tadars/models/hive/tdbr_rule.dart';
import 'package:tadars/models/hive/tdbr_source.dart';
import 'package:tadars/models/hive/tdbr_suggest.dart';
import 'package:tadars/models/hive/tdbr_tadabor_category.dart';
import 'package:tadars/models/hive/tdbr_verse.dart';
import 'package:tadars/models/hive/user.dart';
import 'package:tadars/models/new_version_response.dart';
import 'package:tadars/models/sync.dart';
import 'package:tadars/models/translated_tdbr.dart';
import 'package:tadars/models/user_credential.dart';
import 'package:tadars/models/view_models/sections_with_new_data_vm.dart';

import '../models/denouncement_response.dart';
import '../models/hive/tdbr_tadabor.dart';
import '../models/share_item_response.dart';

part 'api_helper.g.dart';

@RestApi(baseUrl: 'https://tadars.com/app/api?ac=')
abstract class ApiHelper {
  // postman collecion url https://www.getpostman.com/collections/56db456439f251ba8ed6
  factory ApiHelper(Dio dio, {String baseUrl}) = _ApiHelper;
  // get tafseer by page number
  @GET("tafser&json=1")
  Future<List<Tafseer?>> getPageTafseer(
    @Query("page") int page,
    @Query("name") String tafseerName,
  );
  //get sections with new data
  @GET("sectionsWithNewData")
  Future<SectionsWithNewDataVm> getSectionsWithNewData(
    @Queries() SectionsWithNewDataVm sectionsWithNewDataVm, [
    @Query("updated") int? lastSyncId,
  ]);
  // get tdbr verses
  @GET("items&section=tdbr_ayat")
  Future<List<TdbrVerse>> getTdbrVerses({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });
  // get tdbr tadabors
  @GET("items&section=tdbr_tadabor")
  Future<List<TdbrTadabor>> getTdbrTadabors({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr consider
  @GET("items&section=tdbr_consider")
  Future<List<TdbrConsider>> getTdbrConsiders({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr links
  @GET("items&section=tdbr_links")
  Future<List<TdbrLink>> getTdbrLinks({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr media
  @GET("items&section=tdbr_media")
  Future<List<TdbrMedia>> getTdbrMedias({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr pray
  @GET("items&section=tdbr_pray")
  Future<List<TdbrPray>> getTdbrPrays({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr questions
  @GET("items&section=tdbr_questions")
  Future<List<TdbrQuestion>> getTdbrQuestions({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr rules
  @GET("items&section=tdbr_rules")
  Future<List<TdbrRule>> getTdbrRules({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr rules
  @GET("items&section=tdbr_sources")
  Future<List<TdbrSource>> getTdbrSources({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });
  // get tdbr rules
  @GET("items&section=tdbr_suggest")
  Future<List<TdbrSuggest>> getTdbrSuggeste({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr rules
  @GET("items&section=tdbr_comparable")
  Future<List<TdbrComparable>> getTdbrComparable({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr eloquence
  @GET("items&section=tdbr_eloquence")
  Future<List<TdbrEloquence>> getTdbrEloquence({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tdbr tadabor  cats
  @GET("items&section=tdbr_tadabor_cats")
  Future<List<TdbrTadaborCategory>> getTdbrTadaborCats({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get comments
  // @GET("items&section=comments")
  // Future<List<Comment> getComments({
  //   @Query("id") required int id,
  //   @Query("page") int page = 1,
  //   @Query("perPage") int perPage = 500,
  //   @Query("updated") int? lastSyncId,
  // });
  // get likes
  @GET("items&section=likes")
  Future<List<Like>> getLikes({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get users
  @GET("items&section=users")
  Future<List<User>> getUsers({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get sync
  @GET("items&section=sync")
  Future<List<Sync>> getSync({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get tafser
  @GET("items&section=tafser")
  Future<List<QuranTafseer>> getTafser({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });

  // get shares
  @GET("items&section=shares")
  Future<List<Share>> getShares({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });
  //  // get tags
  // @GET("items&section=tags")
  // Future<List<Tag>> getTags({
  //   @Query("id") required int id,
  //   @Query("page") int page = 1,
  //   @Query("perPage") int perPage = 500,
  //   @Query("updated") int? lastSyncId,
  // });

  // get quran_books
  @GET("items&section=quran_books")
  Future<List<QuranBook>> getQuranBooks({
    @Query("id") required int id,
    @Query("page") int page = 1,
    @Query("perPage") int perPage = 500,
    @Query("updated") int? lastSyncId,
  });
  @POST("login")
  Future<UserCredential> login(@Body() Map<String, dynamic> data);

  @POST("addTadars")
  Future<AddTdbrResponse> addTdbr(@Body() Map<String, dynamic> data);

  @POST("denouncementItem")
  Future<DenouncementResponse> denouncementItem(
    @Body() Map<String, dynamic> data,
  );
  @POST("shareItem")
  Future<ShareItemResponse> shareItem(@Body() Map<String, dynamic> data);
  @POST("newApp&ver={version}&os={os}")
  Future<NewVersionResponse> checkForNewVersion(
    @Path("version") String version,
    @Path("os") String os,
  );

  @GET("translate/{lang}/{tdbr_type}/{tdbr_id}")
  Future<TranslatedTdbr> translateTdbr(
    @Path("lang") String language,
    @Path("tdbr_id") int tdbrId,
    @Path("tdbr_type") String type
  );
}
