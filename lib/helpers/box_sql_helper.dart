import 'package:flutter/foundation.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/hive/tdbr_comparable.dart';
import 'package:tadars/models/hive/tdbr_consider.dart';
import 'package:tadars/models/hive/tdbr_eloquence.dart';
import 'package:tadars/models/hive/tdbr_media.dart';
import 'package:tadars/models/hive/tdbr_pray.dart';
import 'package:tadars/models/hive/tdbr_question.dart';
import 'package:tadars/models/hive/tdbr_rule.dart';
import 'package:tadars/models/hive/tdbr_suggest.dart';
import 'package:tadars/models/hive/tdbr_tadabor.dart';
import 'package:tadars/models/tdbr.dart';
import 'package:tadars/models/verse_sura_args.dart';

class BoxSqlHelper {
  //tadabor
  static Future<List<Tdbr<TdbrTadabor>>> getPageTdbrTadabors(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrTadabors(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrTadabor>>> getVersesTdbrTadabors(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrTadabor> tdbTadbor = [];

    for (var element in verses) {
      tdbTadbor.addAll(await SqlHelper.getTdbTadabor(
          element.suraNumber, element.verseNumber));
    }
    // var box = Hive.box<TdbrTadabor>(Boxes.tdbrTadabors);
    // print('tdbr lenght ${box.length}');
    List<Tdbr<TdbrTadabor>> tdbr = [];
    // var tdbrs = box.values
    //     .where((e) =>
    //         verses.any((v) =>
    //             v.suraNumber == e.suraNumber &&
    //             v.verseNumber == e.verseNumber) &&
    //         e.status == 1)
    //     .toList();
    // print('tdbr lenght ${tdbrs.length}');
    // print('get tdbr took ${stopwatch.elapsedMilliseconds}');

    // tdbr ids
    var tdbrIds = tdbTadbor.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_tadabor");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tadabor");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tadabor");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbTadbor.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    // for (var t in tdbrs) {
    //   var links = tdbrlinks.where((e) => e.tdbrId == t.id).toList();
    //   int likes = tdbrKikes.where((e) => e.itemId == t.id).toList().length;
    //   int shares = tdbrShares.where((e) => e.itemId == t.id).toList().length;
    //   var sourceName = "";
    //   try {
    //     sourceName = tdbrSources.firstWhere((e) => e.id == t.sourceId).name;
    //   } catch (e) {
    //     sourceName = "";
    //   }
    for (var element in tdbTadbor) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      // print('link took ${stopwatch.elapsedMilliseconds}');
      tdbr.add(
        Tdbr<TdbrTadabor>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();
    // print(
    //     "${stopwatch.elapsedMilliseconds}");

    return tdbr.reversed.toList();
  }

  //tdbr_eloquence
  static Future<List<Tdbr<TdbrEloquence>>> getPageTdbrEloquences(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrEloquences(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrEloquence>>> getVersesTdbrEloquences(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrEloquence> tdbrEloquence = [];

    for (var element in verses) {
      tdbrEloquence.addAll(await SqlHelper.getTdbEloquence(
          element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrEloquence>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrEloquence.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_eloquence");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_eloquence");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_eloquence");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrEloquence.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrEloquence) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrEloquence>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_consider
  static Future<List<Tdbr<TdbrConsider>>> getPageTdbrConsiders(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrConsiders(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrConsider>>> getVersesTdbrConsiders(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrConsider> tdbrConsider = [];

    for (var element in verses) {
      tdbrConsider.addAll(await SqlHelper.getTdbConsider(
          element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrConsider>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrConsider.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_Consider");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_Consider");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_Consider");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrConsider.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrConsider) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrConsider>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_rules
  static Future<List<Tdbr<TdbrRule>>> getPageTdbrRules(int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrRules(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrRule>>> getVersesTdbrRules(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrRule> tdbrRules = [];

    for (var element in verses) {
      tdbrRules.addAll(
          await SqlHelper.getTdbRule(element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrRule>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrRules.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_rules");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_rules");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_rules");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrRules.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrRules) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrRule>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_suggest
  static Future<List<Tdbr<TdbrSuggest>>> getPageTdbrSuggests(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrSuggest(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrSuggest>>> getVersesTdbrSuggest(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrSuggest> tdbrSuggest = [];

    for (var element in verses) {
      tdbrSuggest.addAll(await SqlHelper.getTdbSuggest(
          element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrSuggest>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrSuggest.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_suggest");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_suggest");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_suggest");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrSuggest.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrSuggest) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrSuggest>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_pray
  static Future<List<Tdbr<TdbrPray>>> getPageTdbrPray(int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrPray(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrPray>>> getVersesTdbrPray(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrPray> tdbrPray = [];

    for (var element in verses) {
      tdbrPray.addAll(
          await SqlHelper.getTdbPray(element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrPray>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrPray.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_pray");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_pray");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_pray");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrPray.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrPray) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrPray>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_rules
  static Future<List<Tdbr<TdbrMedia>>> getPageTdbrMedia(int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrMedia(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrMedia>>> getVersesTdbrMedia(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrMedia> tdbrMedia = [];

    for (var element in verses) {
      tdbrMedia.addAll(
          await SqlHelper.getTdbMedia(element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrMedia>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrMedia.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_media");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_media");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_media");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrMedia.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrMedia) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrMedia>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_comparable
  static Future<List<Tdbr<TdbrComparable>>> getPageTdbrComparable(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrCompareable(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrComparable>>> getVersesTdbrCompareable(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrComparable> tdbrComparable = [];

    for (var element in verses) {
      tdbrComparable.addAll(await SqlHelper.getTdbComparable(
          element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrComparable>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrComparable.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_compareable");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_compareable");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_compareable");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrComparable.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrComparable) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrComparable>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }

  //tdbr_question
  static Future<List<Tdbr<TdbrQuestion>>> getPageTdbrQuestion(
      int pageNumber) async {
    final stopwatch = Stopwatch()..start();
    var page = BoxesHelper.getPageById(pageNumber);
    if (page != null) {
      List<VerseSuraArgs> pageVerse = [];
      var inx = 0;
      for (var e in page.suarVersesCount.entries) {
        for (int i = 0; i < e.value; i++) {
          if (inx == 0) {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key),
                verseNumber: page.firstVerseNumber + i));
          } else {
            pageVerse.add(VerseSuraArgs(
                suraNumber: int.parse(e.key), verseNumber: i + 1));
          }
        }
        inx++;
      }
      if (kDebugMode) {
        print('page verses took ${stopwatch.elapsedMilliseconds}');
      }
      // pageVerse.forEach((element) {
      //   print("suraaaaaaaaaa number");
      //   print(element.suraNumber);
      //   print("veeeeeeeeeers number");
      //   print(element.verseNumber);
      // });
      stopwatch.start();
      // print("----------");
      // print(pageVerse.length);
      var tdbrs = await getVersesTdbrQuestion(pageVerse);
      stopwatch.stop();
      if (kDebugMode) {
        print("elipsed time:  ${stopwatch.elapsedMilliseconds}");
      }
      return tdbrs;
    }
    return [];
  }

  static Future<List<Tdbr<TdbrQuestion>>> getVersesTdbrQuestion(
      List<VerseSuraArgs> verses) async {
    final stopwatch = Stopwatch()..start();
    List<TdbrQuestion> tdbrQuestion = [];

    for (var element in verses) {
      tdbrQuestion.addAll(await SqlHelper.getTdbQuestions(
          element.suraNumber, element.verseNumber));
    }

    List<Tdbr<TdbrQuestion>> tdbr = [];

    // tdbr ids
    var tdbrIds = tdbrQuestion.map((e) => e.id).toList();
    stopwatch.reset();

    var tdbrlinks = await SqlHelper.getTdbrsLinks(tdbrIds, "tdbr_question");
    print('get tdbr links took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrKikes = await SqlHelper.getLikes(tdbrIds, "tdbr_question");
    print('get tdbr likes took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrShares = await SqlHelper.getShares(tdbrIds, "tdbr_question");
    print('get tdbr shares took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();
    var tdbrSources = await SqlHelper.getTdbrsSources(
        tdbrQuestion.map<int>((e) => e.sourceId).toList());
    print('get tdbr sources took ${stopwatch.elapsedMilliseconds}');
    stopwatch.reset();

    for (var element in tdbrQuestion) {
      // stopwatch.reset();
      var links = (tdbrlinks).where((e) => e.tdbrId == element.id).toList();
      int likes =
          tdbrKikes.where((e) => e.itemId == element.id).toList().length;
      int shares =
          tdbrShares.where((e) => e.itemId == element.id).toList().length;

      var sourceName = "";
      try {
        sourceName =
            tdbrSources.firstWhere((e) => e.id == element.sourceId).name;
      } catch (e) {
        sourceName = "";
      }
      tdbr.add(
        Tdbr<TdbrQuestion>(
            tdbr: element,
            links: links,
            shares: shares,
            likes: likes,
            sourceName: sourceName),
      );
    }

    print('get tdbr loop took ${stopwatch.elapsedMilliseconds}');
    stopwatch.stop();

    return tdbr.reversed.toList();
  }
}
