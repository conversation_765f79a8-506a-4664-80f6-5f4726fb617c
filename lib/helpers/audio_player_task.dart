// import 'dart:async';
// import 'dart:io';

// import 'package:audio_service/audio_service.dart';
// import 'package:audio_session/audio_session.dart';
// import 'package:dio/dio.dart';
// import 'package:get/get.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:just_audio/just_audio.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:tadars/helpers/boxes_helper.dart';
// import 'package:tadars/models/hive/downloaded_file.dart';
// import 'package:tadars/models/hive/quran_reciter.dart';
// import 'package:tadars/models/hive/quran_sura.dart';
// import 'package:tadars/models/hive/quran_timing.dart';
// import 'package:tadars/services/network_service.dart';
// import 'package:tadars/utils/constants/boxes.dart';

// class AudioPlayerTask extends AudioEffect {
//   final _player = AudioPlayer();
//   int reciterId = 0;
//   int suraNumber = 0;
//   int _verseNumber = 1;
//   set verseNumber(int value) {
//     if (value == 1)
//       firstVerse = true;
//     else
//       firstVerse = false;
//     _verseNumber = value;
//   }

//   int get verseNumber => _verseNumber;

//   Duration duration = Duration.zero;
//   QuranTiming? timing;
//   List<MediaItem> q = [];
//   bool onlyVerse = false;
//   bool firstVerse = false;
//   bool changeVerse = true;
//   int loopCount = 1;
//   int currentLoop = 1;
//   // final _completer = Completer();
//   Future<void> init() async {
//     var documentDirectory = await getApplicationDocumentsDirectory();
//     await Hive.initFlutter(documentDirectory.path);
//     Hive.registerAdapter(QuranSuraAdapter());
//     Hive.registerAdapter(QuranTimingAdapter());
//     Hive.registerAdapter(QuranReciterAdapter());
//     Hive.registerAdapter(DownloadedFileAdapter());
//     await Hive.openBox<QuranSura>(Boxes.quranSuar);
//     await Hive.openBox<QuranReciter>(Boxes.quranReciters);
//     await Hive.openBox(Boxes.settings);
//     await Hive.openBox<DownloadedFile>(Boxes.downloads);
//     Get.lazyPut(() => NetworkService());
//   }

//   QuranTiming? getVerseTiming(int suraNumber, int verseNumber) {
//     return Hive.box<QuranTiming>(Boxes.reciterTimings(reciterId)).get(
//         "${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}");
//   }

//   Future<void> goToNextSura() async {
//     print("goToNextSura");
//     await onSkipToNext();
//   }

//   Future<void> goToPreviousSura() async {
//     print("goToPreviosSura");
//     await onSkipToPrevious();
//   }

//   Future<void> goToNextVerse() async {
//     var mediaItem = q[suraNumber - 1];
//     if (mediaItem.extras != null) {
//       if (verseNumber >= mediaItem.extras!["last_verse"]) {
//         goToNextSura();
//       } else {
//         verseNumber++;
//         var temp = getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem(suraNumber, verseNumber);
//           await onSeekTo(
//               Duration(milliseconds: (timing?.startAt.toInt() ?? 0) * 1000));
//         }
//       }
//     }
//   }

//   Future<void> goToPreviousVerse() async {
//     var mediaItem = q[suraNumber - 1];
//     if (mediaItem.extras != null) {
//       if (verseNumber <= 1) {
//         changeVerse = false;
//         await goToPreviousSura();
//         var mi = q[suraNumber - 1];
//         verseNumber = mi.extras!["last_verse"];
//         var temp = getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem(suraNumber, verseNumber);
//           await onSeekTo(
//               Duration(milliseconds: (timing?.startAt.toInt() ?? 0) * 1000));
//         }
//       } else {
//         verseNumber--;
//         var temp = getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem(suraNumber, verseNumber);
//           await onSeekTo(
//               Duration(milliseconds: (timing?.startAt.toInt() ?? 0) * 1000));
//         }
//       }
//     }
//   }

//   Future<void> updateMediaItem(int _suraNumber, int _verseNumber) async {
//     var mediaItem = q[_suraNumber - 1];
//     await AudioServiceBackground.setMediaItem(
//       MediaItem(
//         id: mediaItem.id,
//         artist: mediaItem.artist,
//         album: mediaItem.album,
//         displayTitle: mediaItem.album,
//         displaySubtitle: "الآية" + " (" + _verseNumber.toString() + ")",
//         displayDescription: mediaItem.artist,
//         title: mediaItem.title,
//         duration: mediaItem.duration,
//         extras: mediaItem.extras,
//       ),
//     );
//     AudioServiceBackground.sendCustomEvent({
//       "type": "versePosition",
//       "args": {
//         "sura_number": _suraNumber,
//         "verse_number": _verseNumber,
//       }
//     });
//   }

//   Future<void> downloadFile(int _suraNumber) async {
//     try {} catch (e) {}
//     String url =
//         Hive.box<QuranReciter>(Boxes.quranReciters).get(reciterId)!.filesUrl +
//             getFilesUrlSuffix(reciterId, _suraNumber) +
//             _suraNumber.toString().padLeft(3, "0") +
//             ".mp3";
//     var downloadDirectory = await getApplicationDocumentsDirectory();
//     var savePath = downloadDirectory.path +
//         "/" +
//         reciterId.toString() +
//         "/" +
//         _suraNumber.toString().padLeft(3, "0") +
//         ".mp3";
//     NetworkService.to.checkConnection().then((value) {
//       if (value) {
//         Dio().download(url, savePath, onReceiveProgress: (progress, total) {
//           AudioServiceBackground.sendCustomEvent({
//             "type": "downloadEvent",
//             "args": {
//               "progress": progress,
//               "total": total,
//               "id": url,
//             }
//           });
//         }, deleteOnError: true).catchError((error) {
//           AudioServiceBackground.sendCustomEvent({
//             "type": "downloadErrorEvent",
//             "args": {
//               "error": error,
//               "id": url,
//             }
//           });
//         }).then((value) async {
//           await BoxesHelper.addFileToDownloads(url, savePath);
//           await setAudioSource(_suraNumber);
//           AudioServiceBackground.sendCustomEvent({
//             "type": "downloadCompletedEvent",
//             "args": {
//               "id": url,
//             }
//           });
//         });
//       } else {
//         AudioServiceBackground.sendCustomEvent({
//           "type": "connectionErrorEvent",
//         });
//       }
//     });
//   }

//   void handleLoop() {
//     print("loopCount $loopCount");
//     print("currentLoop $currentLoop");
//   }

//   String getFilesUrlSuffix(int _reciterId, int _suraNumber) {
//     var fileUrl = "";
//     if (_reciterId == 3) {
//       switch (_suraNumber) {
//         case 2:
//         case 27:
//         case 28:
//         case 58:
//           fileUrl = "replaced/";
//           break;
//         default:
//       }
//     }
//     return fileUrl;
//   }

//   Future<void> initReciter() async {
//     var suar = Hive.box<QuranSura>(Boxes.quranSuar);
//     var reciters = Hive.box<QuranReciter>(Boxes.quranReciters);
//     var reciter = reciters.get(reciterId);
//     await Hive.openBox<QuranTiming>(Boxes.reciterTimings(reciterId));
//     q = suar.values.map(
//       (e) {
//         var id =
//             '${reciter?.filesUrl}${getFilesUrlSuffix(reciterId, e.id)}${e.id.toString().padLeft(3, "0")}.mp3';
//         return MediaItem(
//           album: e.name,
//           artist: reciter?.name ?? "ا",
//           id: id,
//           // duration: Duration(
//           //     milliseconds:
//           //         ((getVerseTiming(e.id, e.versesCount)?.endAt ?? 0) * 1000)
//           //             .toInt()),
//           title: e.name,
//           extras: {
//             "sura_number": e.id,
//             "reciter_id": reciter?.id ?? 1,
//             "last_verse": e.versesCount,
//             "duration":
//                 ((getVerseTiming(e.id, e.versesCount)?.endAt ?? 0) * 1000)
//                     .toInt(),
//           },
//         );
//       },
//     ).toList();
//     await AudioServiceBackground.setQueue(q);
//     try {
//       await _player.setAudioSource(
//         ConcatenatingAudioSource(
//           children: q.map((item) {
//             var file = BoxesHelper.getDownloadedFile(item.id);
//             if (file != null) {
//               print("file Path ${file.filePath}");

//               return AudioSource.uri(File(file.filePath).uri);
//             }
//             print("file source ${item.id}");
//             return AudioSource.uri(Uri.parse(item.id));
//           }).toList(),
//           useLazyPreparation: false,
//         ),
//         preload: false,
//       );
//     } catch (e) {
//       print("Error: $e");
//       AudioServiceBackground.sendCustomEvent(
//         {
//           "type": "setAudioSourceErrorEvent",
//           "args": {
//             "error": e,
//           }
//         },
//       );
//       onStop();
//     }
//   }

//   Future<void> setAudioSource(int _suraNumber) async {
//     try {
//       await _player.setAudioSource(
//         ConcatenatingAudioSource(
//           children: q.map((item) {
//             var file = BoxesHelper.getDownloadedFile(item.id);
//             if (file != null) {
//               print("file Path ${file.filePath}");

//               return AudioSource.uri(File(file.filePath).uri);
//             }
//             print("file source ${item.id}");
//             return AudioSource.uri(Uri.parse(item.id));
//           }).toList(),
//           useLazyPreparation: false,
//         ),
//         initialIndex: suraNumber - 1,
//         initialPosition: Duration(seconds: (timing?.startAt ?? 0).toInt()),
//         preload: false,
//       );
//     } catch (e) {
//       print("Error: $e");
//       AudioServiceBackground.sendCustomEvent(
//         {
//           "type": "setAudioSourceErrorEvent",
//           "args": {
//             "error": e,
//           }
//         },
//       );
//       onStop();
//     }
//   }

//   Future<void> handleCurrentIndexStream(int index) async {
//     suraNumber = index + 1;
//     print("ChangeVerse $changeVerse");
//     if (changeVerse) {
//       verseNumber = 1;
//     } else {
//       changeVerse = true;
//     }
//     print("_player.currentIndexStream index = $index");
//     print("_player.currentIndexStream verse = $verseNumber");
//     print("_player.currentIndexStream sura = $suraNumber");
//     var mediaItem = q[index];
//     print("aymen ${mediaItem.id}");
//     await AudioServiceBackground.setMediaItem(
//       MediaItem(
//         id: mediaItem.id,
//         artist: mediaItem.artist,
//         album: mediaItem.album,
//         displayTitle: mediaItem.album,
//         displaySubtitle: "الآية" + " (" + verseNumber.toString() + ")",
//         displayDescription: mediaItem.artist,
//         title: mediaItem.title,
//         duration: mediaItem.duration,
//         extras: mediaItem.extras,
//       ),
//     );
//     print("aymen ${mediaItem.duration}");
//   }

//   Future<void> handlePositionStream(Duration position) async {
//     if (timing != null) {
//       var end = (timing?.endAt ?? 0) * 1000;
//       // print(position.inMilliseconds.toString() + " " + end.toString());
//       if (firstVerse) {
//         var temp = getVerseTiming(suraNumber, verseNumber);
//         if (temp != null) {
//           currentLoop = 1;
//           timing = temp;
//           updateMediaItem(suraNumber, verseNumber);
//           firstVerse = false;
//         }
//       } else if (position.inMilliseconds >= end) {
//         handleLoop();
//         if (loopCount > 1 && currentLoop <= loopCount - 1) {
//           await onSeekTo(
//               Duration(milliseconds: (timing?.startAt.toInt() ?? 0) * 1000));
//           if (loopCount != 5) {
//             currentLoop++;
//           }
//         } else if (onlyVerse) {
//           await onSeekTo(
//               Duration(milliseconds: (timing?.startAt.toInt() ?? 0) * 1000));
//           onPause();
//         } else {
//           verseNumber++;
//           var temp = getVerseTiming(suraNumber, verseNumber);
//           if (temp != null) {
//             currentLoop = 1;
//             timing = temp;
//             updateMediaItem(suraNumber, verseNumber);
//           }
//         }
//       }
//     }
//   }

//   Future<void> playVerse(int _suraNumber, int _verseNumber) async {
//     changeVerse = false;
//     verseNumber = _verseNumber;
//     if (suraNumber != _suraNumber) {
//       suraNumber = _suraNumber;
//       await AudioServiceBackground.setMediaItem(q[suraNumber - 1]);
//       await onSeekTo(
//         Duration.zero,
//       );
//     }
//     timing = getVerseTiming(_suraNumber, _verseNumber);
//     int startAt = ((timing?.startAt ?? 0) * 1000).toInt();
//     print(startAt);
//     await onSeekTo(
//       Duration(milliseconds: startAt),
//     );
//     await onPlay();
//   }

//   Future<void> onStart(Map<String, dynamic>? params) async {
//     await init();
//     if (params != null) {
//       loopCount = params["loop_count"] ?? 1;
//       reciterId = params["reciter_id"] ?? 1;
//     }
//     final session = await AudioSession.instance;
//     await session.configure(AudioSessionConfiguration.music());
//     await initReciter();
//     _player.currentIndexStream.distinct().listen(
//       (index) async {
//         if (index != null) {
//           handleCurrentIndexStream(index);
//         }
//       },
//     );
//     _player.processingStateStream.distinct().listen((state) async {
//       if (state == ProcessingState.completed) {
//         print("aymen completed");
//         await onSkipToNext();
//       }
//     });

//     _player.positionStream.distinct().listen(
//       (position) async {
//         handlePositionStream(position);
//       },
//     );
//     _player.playbackEventStream.distinct().listen((playBack) async {
//       print("aymen processing state ${playBack.processingState}");
//       await AudioServiceBackground.setState(
//         controls: [
//           MediaControl.rewind,
//           if (_player.playing) MediaControl.pause else MediaControl.play,
//           MediaControl.stop,
//           MediaControl.fastForward,
//         ],
//         systemActions: [
//           MediaAction.seekForward,
//           MediaAction.seekBackward,
//         ],
//         androidCompactActions: [0, 1, 3],
//         processingState: {
//           ProcessingState.idle: AudioProcessingState.idle,
//           ProcessingState.loading: AudioProcessingState.loading,
//           ProcessingState.buffering: AudioProcessingState.buffering,
//           ProcessingState.ready: AudioProcessingState.ready,
//           ProcessingState.completed: AudioProcessingState.completed,
//         }[playBack.processingState],
//         playing: _player.playing,
//         position: _player.position,
//         bufferedPosition: _player.bufferedPosition,
//         speed: _player.speed,
//       );
//     });
//     // _player.playerStateStream.listen(
//     //   (playerState) async {
//     //     print(playerState.processingState);
//     //     AudioServiceBackground.setState(
//     //       playing: playerState.playing,
//     //       position: _player.position,
//     //       bufferedPosition: _player.bufferedPosition,
//     //       speed: _player.speed,
//     //       processingState: {
//     //         ProcessingState.idle: AudioProcessingState.stopped,
//     //         ProcessingState.loading: AudioProcessingState.connecting,
//     //         ProcessingState.buffering: AudioProcessingState.buffering,
//     //         ProcessingState.ready: AudioProcessingState.ready,
//     //         ProcessingState.completed: AudioProcessingState.completed,
//     //       }[playerState.processingState],
//     //       controls: [
//     //         playerState.playing ? MediaControl.pause : MediaControl.play,
//     //         MediaControl.stop,
//     //       ],
//     //     );
//     //   },
//     // );
//     return super.onStart(params);
//   }

//   @override
//   Future onCustomAction(String name, arguments) async {
//     switch (name) {
//       case "playVerse":
//         onlyVerse = true;
//         playVerse(arguments["sura_number"], arguments["verse_number"]);
//         break;
//       case "playFromVerse":
//         onlyVerse = false;
//         playVerse(arguments["sura_number"], arguments["verse_number"]);
//         break;
//       case "download":
//         downloadFile(suraNumber);
//         break;
//       case "setLoopCount":
//         print("setLoopCount");
//         loopCount = arguments["count"];
//         break;
//       case "setReciterId":
//         print("setReciterId");
//         if (arguments["id"] != reciterId) {
//           setReciter(arguments["id"]);
//         }
//         break;
//       default:
//     }
//     return super.onCustomAction(name, arguments);
//   }

//   Future<void> setReciter(int id) async {
//     reciterId = id;
//     var tempSuraNumber = suraNumber;
//     var tempVerseNumber = verseNumber;
//     await initReciter();
//     suraNumber = tempSuraNumber;
//     verseNumber = tempVerseNumber;
//     timing = getVerseTiming(suraNumber, verseNumber);
//     print(timing?.startAt ?? "null");
//     int startAt = ((timing?.startAt ?? 0) * 1000).toInt();
//     await onSeekTo(
//       Duration(
//         milliseconds: startAt,
//       ),
//     );
//     await onPlay();
//   }

//   @override
//   Future<void> play() async {
//     await _player.play();
//   }

//   @override
//   Future<void> onPause() async {
//     await _player.pause();
//     return super.onPause();
//   }

//   @override
//   Future<void> onStop() {
//     _player.stop();
//     return super.onStop();
//   }

//   @override
//   Future<void> onSeekTo(Duration duration) async {
//     try {
//       await _player.seek(duration, index: suraNumber - 1);
//     } catch (e) {
//       print(e);
//       AudioServiceBackground.sendCustomEvent(
//         {
//           "type": "onSeekErrorEvent",
//           "args": {
//             "verse_number": e,
//           }
//         },
//       );
//     }
//   }

//   @override
//   Future<void> onSkipToNext() async {
//     await _player.seekToNext();
//     return super.onSkipToNext();
//   }

//   @override
//   Future<void> onSkipToPrevious() async {
//     await _player.seekToPrevious();
//     return super.onSkipToPrevious();
//   }

//   @override
//   Future<void> onFastForward() {
//     goToNextVerse();
//     return super.onFastForward();
//   }

//   @override
//   Future<void> onRewind() {
//     goToPreviousVerse();
//     return super.onRewind();
//   }
// }
