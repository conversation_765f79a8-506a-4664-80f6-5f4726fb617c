import 'dart:async';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tadars/utils/constants/boxes.dart';

import '../../../../helpers/boxes_helper.dart';
import '../../../../utils/constants/routes.dart';
import '../../models/bookmark.dart';
import '../../models/note.dart';
import '../enums/bookmark_type.dart';
import '../helpers/user_sync_api_helper.dart';
import '../helpers/user_sync_db_helper.dart';

class UserSyncService extends GetxService {
  static UserSyncService get instance => Get.find();
  UserSyncApiHelper get apiHelper {
    var dio = Dio();
    dio.interceptors.add(
      RetryInterceptor(
        dio: dio,
        logPrint: Get.log, // specify log function
        retries: 3, // retry count
        retryDelays: const [
          Duration(seconds: 1), // wait 1 sec before first retry
          Duration(seconds: 2), // wait 2 sec before second retry
          Duration(seconds: 3), // wait 3 sec before third retry
        ],
      ),
    );
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        enabled: kDebugMode,
      ),
    );
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-api-key': 'elA3H9qcnN39uULcjLXOUeWpIEW3QYaHMHHZvs8eTfVqx4Q6QF',
      'Authorization': 'Bearer ${Hive.box(Boxes.settings).get("user_token")}',

      "user-uid": Hive.box(Boxes.settings).get("uid"),
      "user-token": Hive.box(Boxes.settings).get("user_token"),
    };
    return UserSyncApiHelper(dio);
  }

  late SharedPreferencesWithCache pref;
  var bookmarks = RxMap<String, Bookmark>({});
  var notes = RxMap<String, Note>({});
  String prefsKeyDontShowLoginDialog = 'dont_show_login_dialog_again';
  Future<UserSyncService> init() async {
    await loadData();
    pref = await SharedPreferencesWithCache.create(
      cacheOptions: const SharedPreferencesWithCacheOptions(),
    );

    notes.listen((data) {
      Get.log("ayman notes changed");
      // sync();
    });

    bookmarks.listen((data) {
      Get.log("ayman bookmarks changed");
      // sync();
    });

    return this;
  }

  int getLastSyncId() {
    return pref.getInt('last_sync_id') ?? 0;
  }

  void setLastSyncId(int id) {
    pref.setInt('last_sync_id', id);
  }

  Future<void> loadData() async {
    bookmarks.value = await UserSyncDBHelper.getAllBookmarks();
    notes.value = await UserSyncDBHelper.getAllNotes();
  }

  Future<void> syncNewDataFromServer() async {
    var tables = ['notes', 'bookmarks'];
    var lastIds = await UserSyncDBHelper.getTablesLastIds(tables: tables);
    Get.log('lastIds: $lastIds');
    // check for new data
    try {
      var response = await apiHelper.checkNewRecords(
        queries: {
          'last_note_id': lastIds['notes'],
          'last_bookmark_id': lastIds['bookmarks'],
          'last_favorited_hdaiah_id': lastIds['favorited_hdaiat'],
          'last_sync_id': getLastSyncId(),
        },
      );
      Map<String, dynamic> tablesNewRecordsCounts = response.data;
      for (var table in tables) {
        if (tablesNewRecordsCounts.containsKey('${table}_count')) {
          var perPage = 15;
          switch (table) {
            case 'notes':
              perPage = 15;
            case 'bookmarks':
              perPage = 100;
            case 'favorited_hdaiat':
              perPage = 50;
            default:
              perPage = 15;
          }

          for (
            var i = 0;
            i < tablesNewRecordsCounts['${table}_count'] / perPage;
            i++
          ) {
            var response = await apiHelper.getNewRecords(
              queries: {
                'table_name': table,
                'last_id': lastIds[table],
                'page': i + 1,
                'per_page': perPage,
              },
            );
            var data = response.data['data'];
            if (data is List) {
              var records =
                  (response.data['data'] as List?)
                      ?.cast<Map<String, dynamic>>() ??
                  [];
              unawaited(
                UserSyncDBHelper.addToTable(table: table, records: records),
              );
            }
          }
        }
      }
      Get.log("tablesNewRecordsCounts $tablesNewRecordsCounts");
    } catch (e) {
      if (e is DioException) {
        Get.log(e.response?.data);
      } else {
        Get.log(e.toString());
      }
    }
  }

  Future<void> syncUpdatedDataFromServer() async {
    var tables = ['notes', 'bookmarks', 'favorited_hdaiat'];
    var lastSyncId = getLastSyncId();
    Get.log('lastSyncId: $lastSyncId');
    try {
      var response = await apiHelper.checkUpdatedRecords(
        lastSyncId: lastSyncId,
      );
      Map<String, dynamic> tablesUpdatedRecordsCounts = response.data;
      for (var table in tables) {
        if (tablesUpdatedRecordsCounts.containsKey('${table}_count')) {
          var perPage = 15;
          switch (table) {
            case 'notes':
              perPage = 15;
              break;
            case 'bookmarks':
              perPage = 100;
            case 'favorited_hdaiat':
              perPage = 50;
            default:
              perPage = 15;
          }

          for (
            var i = 0;
            i < tablesUpdatedRecordsCounts['${table}_count'] / perPage;
            i++
          ) {
            var response = await apiHelper.getUpdatedRecords(
              lastSyncId: lastSyncId,
              table: table,
            );
            if (kDebugMode) {
              Get.log(
                'Updated Data for $table page ${i + 1}: ${response.data}',
              );
            }
            // Assuming response.data.data is List<Map<String, dynamic>>
            var records =
                (response.data['data'] as List?)
                    ?.cast<Map<String, dynamic>>() ??
                [];
            if (kDebugMode) {
              Get.log('Updated record for $table page ${i + 1}: ${records}');
            }
            if (records.isNotEmpty) {
              await UserSyncDBHelper.updateRecordsFromServer(
                table: table,
                records: records,
              );
            }
          }
        }
      }
      Get.log('tablesUpdatedRecordsCounts: $tablesUpdatedRecordsCounts');
    } catch (e) {
      if (e is DioException) {
        Get.log(e.response?.data);
      } else {
        Get.log(e.toString());
      }
      // Get.log error trace
      if (kDebugMode) {
        Get.log(e.toString());
      }
    }
  }

  Future<void> syncDeletedDataFromServer() async {
    var lastSyncId = getLastSyncId();

    try {
      var response = await apiHelper.checkNewRecords(
        queries: {'last_sync_id': getLastSyncId()},
      );
      Map<String, dynamic> tablesNewRecordsCounts = response.data;
      var _lastSyncId = lastSyncId;
      for (var i = 0; i < tablesNewRecordsCounts['sync_count'] / 100; i++) {
        var response = await apiHelper.getNewRecords(
          queries: {
            'table_name': 'user_syncs',
            'last_id': lastSyncId,
            'page': i + 1,
            'per_page': 100,
          },
        );
        var data = response.data['data'] as List;
        if (data.isEmpty) {
          continue;
        }
        Get.log("syncDeletedDataFromServer data: $data");
        var recordsToDelete = <String, List<int>>{};
        for (var item in data) {
          var action = item['action'];
          var table = item['table_name'];
          if (action != 0) {
            continue;
          }
          if (!recordsToDelete.containsKey(table)) {
            recordsToDelete[table] = [];
          }
          recordsToDelete[table]?.add(item['id']);
        }
        Get.log("recordsToDelete: $recordsToDelete");
        for (var table in recordsToDelete.keys) {
          await UserSyncDBHelper.deleteRecordsFromServer(
            table: table,
            serverIds: recordsToDelete[table] ?? [],
          );
        }
        Get.log("recordsToDelete: $recordsToDelete");

        _lastSyncId = data.last['id'];
        Get.log("sync data: $_lastSyncId");
      }
      setLastSyncId(_lastSyncId);
    } catch (e) {
      if (e is DioException) {
        Get.log(e.response?.data);
      } else {
        Get.log(e.toString());
      }
    }
  }

  Future<void> sync({bool withoutDialog = true}) async {
    if (Hive.box(Boxes.settings).get("user_token") == null) {
      if (withoutDialog) return;
      unawaited(checkAndShowLoginDialog());
      return;
    }
    await syncNewDataFromServer();
    await syncUpdatedDataFromServer();
    await syncDeletedDataFromServer();
    await syncDataToServer();
    unawaited(loadData());
  }

  Future<void> syncDataToServer() async {
    var tables = ['notes', 'bookmarks'];
    try {
      for (var table in tables) {
        // TODO: Get local data to sync for each table
        var newRecords = await UserSyncDBHelper.getNewRecordsToSync(
          table: table,
        );
        var updatedRecords = await UserSyncDBHelper.getUpdatedRecordsToSync(
          table: table,
        );
        var deletedRecords = await UserSyncDBHelper.getDeletedRecordsToSync(
          table: table,
        );
        Get.log('$table newRecords: $newRecords');
        Get.log('$table updatedRecords: $updatedRecords');
        Get.log('$table deletedRecords: $deletedRecords');
        if (newRecords.isEmpty &&
            updatedRecords.isEmpty &&
            deletedRecords.isEmpty) {
          continue;
        }
        var response = await apiHelper.sync(
          body: {
            'table_name': table,
            'inserts': newRecords,
            'updates': updatedRecords,
            'deletes': deletedRecords,
          },
        );
        var mappings = <Map<String, dynamic>>[]; // Default to empty list
        if (response.data != null &&
            response.data.containsKey('inserted_mappings')) {
          var insertedMappingsData = response.data['inserted_mappings'];
          if (insertedMappingsData is List) {
            // Check if it's a List
            mappings =
                insertedMappingsData.cast<Map<String, dynamic>>().toList();
          } else {
            // Handle the case where it's not a List (optional - log error, etc.)
            Get.log("Error: 'inserted_mappings' is not a List.");
          }
        } else {
          // Handle the case where 'inserted_mappings' key is missing or response.data is null (optional - log error, etc.)
          Get.log(
            "Warning: 'inserted_mappings' key not found in response data, or response.data is null.",
          );
        }
        unawaited(
          UserSyncDBHelper.addServerId(table: table, mapping: mappings),
        );
        unawaited(UserSyncDBHelper.emptyUserSyncTable());
      }
    } catch (e) {
      if (e is DioException) {
        Get.log(e.response?.data);
      } else {
        Get.log(e.toString());
      }
    }
  }

  Note? getNote(int suraNumber, int verseNumber) {
    var key = createSuraAyahKey(suraNumber, verseNumber);
    if (notes.containsKey(key)) {
      return notes[key];
    }
    return null;
  }

  Bookmark? getBookmark(int suraNumber, int verseNumber) {
    var key = createSuraAyahKey(suraNumber, verseNumber);
    if (bookmarks.containsKey(key)) {
      return bookmarks[key];
    }
    return null;
  }

  Future<void> saveBookmark(Bookmark bookmark) async {
    await UserSyncDBHelper.addBookmark(bookmark);
    var suraNumber = bookmark.suraNumber ?? 0;
    var verseNumber = bookmark.verseNumber ?? 0;
    var key =
        bookmark.type == BookmarkType.page
            ? createPageKey(bookmark.pageNumber ?? 0)
            : createSuraAyahKey(suraNumber, verseNumber);
    var tempBookmark = await UserSyncDBHelper.getBookmark(
      suraNumber: suraNumber,
      verseNumber: verseNumber,
    );
    if (tempBookmark != null) {
      bookmarks[key] = tempBookmark;
      // bookmarks.refresh();
      unawaited(sync());
    }
  }

  Future<void> removeFromBookmarks({
    int? suraNumber,
    int? verseNumber,
    int? pageNumber,
  }) async {
    var bookmark = await UserSyncDBHelper.removeFromBookmarks(
      suraNumber: suraNumber,
      verseNumber: verseNumber,
      pageNumber: pageNumber,
    );
    bookmarks.removeWhere(
      (key, value) =>
          key ==
          (bookmark?.type == BookmarkType.page
              ? createPageKey(pageNumber ?? 0)
              : createSuraAyahKey(suraNumber ?? 0, verseNumber ?? 0)),
    );
    // bookmarks.refresh();
    unawaited(sync());
  }

  bool hasPageBookmark(int pageNumber) {
    var key = createPageKey(pageNumber);
    return bookmarks.containsKey(key);
  }

  Future<void> addPageToBookmarks(int pageNumber) async {
    var key = createPageKey(pageNumber);
    await saveBookmark(
      Bookmark(
        pageNumber: pageNumber,
        type: BookmarkType.page,
        colorIndex: 0,
        createdAt: DateTime.now(),
      ),
    );
    var bookmark = await UserSyncDBHelper.getBookmark(pageNumber: pageNumber);
    if (bookmark == null) return;
    bookmarks[key] = bookmark;
    // bookmarks.refresh();
    unawaited(sync());
  }

  void deletePageFromBookmarks(int pageNumber) {
    var key = createPageKey(pageNumber);
    removeFromBookmarks(pageNumber: pageNumber);
    bookmarks.remove(key);
    bookmarks.refresh();
    unawaited(sync());
  }

  static String createSuraAyahKey(int suraNumber, int verseNumber) {
    return "${suraNumber.toString().padLeft(3, '0')}_${verseNumber.toString().padLeft(3, '0')}";
  }

  static String createPageKey(int pageNumber) {
    return pageNumber.toString().padLeft(3, '0');
  }

  Future<void> updateOrAddNote(Note note) async {
    await UserSyncDBHelper.updateOrAddNote(note);
    var tempNote = await UserSyncDBHelper.getNote(
      note.suraNumber ?? 0,
      note.verseNumber ?? 0,
    );
    if (tempNote == null) return;
    var key = createSuraAyahKey(
      tempNote.suraNumber ?? 0,
      tempNote.verseNumber ?? 0,
    );
    notes[key] = tempNote;
    unawaited(sync());
    // notes.refresh();
  }

  void deleteNote(int suraNumber, int verseNumber) {
    UserSyncDBHelper.deleteNote(suraNumber, verseNumber);
    var key = createSuraAyahKey(suraNumber, verseNumber);
    notes.remove(key);
    // notes.refresh();
    unawaited(sync());
  }

  Future<void> importNotesFromHive() async {
    var notes = BoxesHelper.getAllNotes();

    if (notes.isEmpty) return;

    for (var note in notes) {
      await updateOrAddNote(Note.fromJson(note.toJson()));
    }

    await BoxesHelper.deleteAllNotes();
  }

  Future<void> importBookmarksFromHive() async {
    var bookmarks = BoxesHelper.getAllBookmarks();

    for (var bookmark in bookmarks) {
      await saveBookmark(Bookmark.fromJson(bookmark.toJson()));
    }

    await BoxesHelper.deleteAllBookmarks();
  }

  Future<void> checkAndShowLoginDialog() async {
    final prefs = await SharedPreferences.getInstance();
    // await prefs.setBool(prefsKeyDontShowLoginDialog, false);
    // Get the preference, defaulting to 'false' if not set
    final dontShowAgain = prefs.getBool(prefsKeyDontShowLoginDialog) ?? false;

    // Only show the dialog if the user hasn't opted out
    if (!dontShowAgain) {
      _showLoginDialog(); // Call the private function that shows the dialog
    } else {
      Get.log("User has opted out of seeing the login encouragement dialog.");
    }
  }

  // --- The actual dialog implementation ---
  void _showLoginDialog() {
    // Use StatefulBuilder to manage the checkbox state locally within the dialog
    var localDontShowAgain = false; // Initial state for the checkbox

    Get.dialog(
      StatefulBuilder(
        // Use StatefulBuilder for local state management of the checkbox
        builder: (context, setState) {
          return AlertDialog(
            title: Text('Login'.tr),
            content: Column(
              mainAxisSize: MainAxisSize.min, // Important to prevent stretching
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Log in or sign up to sync your bookmarks, notes, and favorited hdaiat across devices.'
                      .tr,
                ),
                const SizedBox(height: 15),
                // "Don't show again" Checkbox
                Row(
                  children: [
                    Checkbox(
                      value: localDontShowAgain,
                      onChanged: (bool? value) {
                        if (value != null) {
                          // Use the setState from StatefulBuilder to update the UI
                          setState(() {
                            localDontShowAgain = value;
                          });
                        }
                      },
                    ),
                    // Text("Don't show this again".tr),
                    // GestureDetector makes the text tappable as well
                    Expanded(
                      // Use Expanded if you want text to wrap and fill space
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            localDontShowAgain = !localDontShowAgain;
                          });
                        },
                        child: Text("Don't show this again".tr),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  'Maybe Later'.tr,
                  style: const TextStyle(fontSize: 12),
                ),
                onPressed: () async {
                  // Save preference if checked
                  if (localDontShowAgain) {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool(prefsKeyDontShowLoginDialog, true);
                    Get.log("Login dialog preference saved: Don't show again.");
                  }
                  Get.back(); // Close the dialog
                },
              ),
              ElevatedButton(
                child: Text(
                  'Login / Sign Up'.tr,
                  style: const TextStyle(fontSize: 12),
                ),
                onPressed: () async {
                  // Save preference if checked, even if logging in now
                  if (localDontShowAgain) {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool(prefsKeyDontShowLoginDialog, true);
                    Get.log("Login dialog preference saved: Don't show again.");
                  }
                  Get.back(); // Close the dialog first
                  // Navigate to your login/signup screen
                  // Replace with your actual route name
                  // Get.toNamed(Routes.loginPage);
                  // Or Get.to(YourLoginPage());
                },
              ),
            ],
          );
        },
      ),
      barrierDismissible: false, // User must interact with buttons
    );
  }
}
