import 'dart:async';

import 'package:get/get_core/get_core.dart';
import 'package:sqlbrite/sqlbrite.dart';
import 'package:tadars/helpers/sql_helper.dart';

import '../../models/bookmark.dart';
import '../../models/note.dart';
import '../enums/bookmark_type.dart';
import '../enums/sync_action.dart';
import '../services/user_sync_service.dart';

class UserSyncDBHelper {
  static final Future<BriteDatabase?> _db = SqlHelper.briteDB;
  static String userSyncTableName = 'user_syncs';

  static Future<Map<String, dynamic>> getTablesLastIds({
    required List<String> tables,
  }) async {
    var db = await _db;
    var tablesLastIds = <String, dynamic>{};
    for (var table in tables) {
      var lastId =
          await db?.rawQuery(' SELECT MAX(server_id) AS last_id FROM $table');
      tablesLastIds[table] = lastId?.first['last_id'] ?? 0;
    }
    return tablesLastIds;
  }

  static Future<List<Map<String, dynamic>>> getNewRecordsToSync({
    required String table,
  }) async {
    var db = await _db;
    var localData =
        await db?.rawQuery('SELECT * FROM $table WHERE server_id IS NULL');
    return localData?.map((record) {
          var recordWithoutServerId =
              Map<String, dynamic>.from(record); // Create a copy
          recordWithoutServerId.remove('server_id'); // Remove the key
          return recordWithoutServerId;
        }).toList() ??
        [];
  }

  static Future<List<Map<String, dynamic>>> getUpdatedRecordsToSync({
    required String table,
  }) async {
    var db = await _db;
    var ids = await getUpdatedRecordsIds(table: table);
    if (ids.isEmpty) return [];
    var localData = await db?.rawQuery(
        'SELECT * FROM $table WHERE server_id IN (${ids.join(',')})');
    return localData?.toList() ?? [];
  }

  static Future<List<int>> getUpdatedRecordsIds({
    required String table,
  }) async {
    var db = await _db;
    var localData = await db?.rawQuery(
        'SELECT DISTINCT record_id FROM $userSyncTableName WHERE table_name = ? AND action = ?',
        [
          table,
          SyncAction.update.value,
        ]);

    return localData?.map((e) => e['record_id'] as int).toList() ?? [];
  }

  // deleted records
  static Future<List<int>> getDeletedRecordsIds({
    required String table,
  }) async {
    var db = await _db;
    var localData = await db?.rawQuery(
        'SELECT DISTINCT record_id FROM $userSyncTableName WHERE table_name = ? AND action = ?',
        [
          table,
          SyncAction.delete.value,
        ]);

    return localData?.map((e) => e['record_id'] as int).toList() ?? [];
  }

  static Future<List<int>> getDeletedRecordsToSync({
    required String table,
  }) async {
    var ids = await getDeletedRecordsIds(table: table);
    return ids;
  }

  // check if not exists

  static Future<int> noteExists(int surahNumber, int verseNumber) async {
    var db = await _db;
    var result = await db?.rawQuery(
      'SELECT * FROM notes WHERE sura_number = ? AND verse_number = ? limit 1',
      [surahNumber, verseNumber],
    );
    var id = (result?.isEmpty ?? true)
        ? 0
        : int.parse(result?.first['id']?.toString() ?? '0');
    return id;
  }

  // add note
  static Future<int> addNote(Note note) async {
    var db = await _db;
    var result = await db?.insert('notes', note.toJson());
    return result ?? 0;
  }

  // update note
  static Future<int> updateNote(Note note) async {
    var db = await _db;
    var tempNote = await getNote(note.suraNumber ?? 0, note.verseNumber ?? 0);
    var result = await db?.update(
      'notes',
      note.toJson(),
      where: 'id = ?',
      whereArgs: [tempNote?.id ?? note.id],
    );
    if (result != 0 && tempNote?.serverId != null) {
      await addToSync('notes', tempNote?.serverId ?? 0, SyncAction.update);
    }
    return result ?? 0;
  }

  // update or add note
  static Future<int> updateOrAddNote(Note note) async {
    var id = await noteExists(note.suraNumber ?? 0, note.verseNumber ?? 0);
    if (id != 0) {
      note.id = id;
      return await updateNote(note);
    } else {
      return await addNote(note);
    }
  }

  // delete note
  static Future<int> deleteNote(int suraNumber, int verseNumber) async {
    var db = await _db;
    var note = await getNote(suraNumber, verseNumber);
    var id = note?.id ?? 0;
    var serverId = note?.serverId ?? 0;
    var result = await db?.delete('notes', where: 'id = ?', whereArgs: [id]);
    if (result != 0 && serverId != 0) {
      await addToSync('notes', serverId, SyncAction.delete);
    }
    return result ?? 0;
  }

  // get note by sura number and verse number
  static Future<Note?> getNote(int suraNumber, int verseNumber) async {
    var db = await _db;
    var result = await db?.rawQuery(
      'SELECT * FROM notes WHERE sura_number = ? AND verse_number = ? limit 1',
      [suraNumber, verseNumber],
    );

    return result?.map((e) => Note.fromJson(e)).firstOrNull;
  }

  // add bookmark
  static Future<int> addBookmark(Bookmark bookmark) async {
    var db = await _db;
    int? result = 0;
    var tempBookmark = await getBookmark(
        suraNumber: bookmark.suraNumber, verseNumber: bookmark.verseNumber);
    if (tempBookmark != null) {
      bookmark.id = tempBookmark.id;
      bookmark.serverId = tempBookmark.serverId;
      result = await updateBookmark(bookmark);
    } else {
      result = await db?.insert('bookmarks', bookmark.toJson());
    }
    return result ?? 0;
  }

  static Future<int> updateBookmark(Bookmark bookmark) async {
    var db = await _db;
    var result = await db?.update(
      'bookmarks',
      bookmark.toJson(),
      where: 'id = ?',
      whereArgs: [
        bookmark.id,
      ],
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    if (result != 0 && bookmark.serverId != null) {
      unawaited(
          addToSync('bookmarks', bookmark.serverId ?? 0, SyncAction.update));
    }
    return result ?? 0;
  }

  static Future<Bookmark?> getBookmark(
      {int? suraNumber, int? verseNumber, int? pageNumber}) async {
    var db = await _db;
    Get.log('getBookmark $suraNumber $verseNumber $pageNumber');

    List<Map<String, Object?>>? result;
    if (pageNumber == null) {
      result = await db?.rawQuery(
        'SELECT * FROM bookmarks WHERE sura_number = ? AND verse_number = ? AND type = ? limit 1',
        [suraNumber, verseNumber, BookmarkType.verse.value],
      );
    } else {
      result = await db?.rawQuery(
        'SELECT * FROM bookmarks WHERE page_number = ? AND type = ? limit 1',
        [pageNumber, BookmarkType.page.value],
      );
    }
    Get.log(result.toString());
    return result?.map((e) => Bookmark.fromJson(e)).firstOrNull;
  }

  // delete bookmark

  static Future<Bookmark?> removeFromBookmarks({
    int? suraNumber,
    int? verseNumber,
    int? pageNumber,
  }) async {
    print("removeFromBookmarks $suraNumber $verseNumber $pageNumber");
    var db = await _db;
    var tempBookmark = await getBookmark(
      suraNumber: suraNumber,
      verseNumber: verseNumber,
      pageNumber: pageNumber,
    );
    int? result;
    if (pageNumber == null) {
      result = await db?.delete('bookmarks',
          where: 'sura_number = ? AND verse_number = ? AND type = ?',
          whereArgs: [
            suraNumber,
            verseNumber,
            BookmarkType.verse.value,
          ]);
    } else {
      result = await db?.delete(
        'bookmarks',
        where: 'page_number = ? AND type = ?',
        whereArgs: [
          pageNumber,
          BookmarkType.page.value,
        ],
      );
    }
    print("result $result ${tempBookmark?.serverId}");
    if (tempBookmark?.serverId != null) {
      await addToSync(
          'bookmarks', tempBookmark?.serverId ?? 0, SyncAction.delete);
    }
    return tempBookmark;
  }

  static Future<int> removePageFromBookmarks(int pageNumber) async {
    var db = await _db;
    var tempBookmark = await getBookmark(
      pageNumber: pageNumber,
    );
    var result =
        await db?.delete('bookmarks', where: 'page_number = ? ', whereArgs: [
      pageNumber,
    ]);
    if (result != 0 && tempBookmark?.serverId != null) {
      await addToSync(
          'bookmarks', tempBookmark?.serverId ?? 0, SyncAction.delete);
    }
    return result ?? 0;
  }

  // // add to favorited hdaiat

  // static Future<int> addHdaiahToFavorite(
  //   int hdaiahId,
  //   String hdaiahType,
  // ) async {
  //   var result = await SqlHelper.addHdaiahToFavorite(
  //     hdaiahId: hdaiahId,
  //     hdaiahType: hdaiahType,
  //   );
  //   return result;
  // }

  // static Future<int> removeHdaiahFromFavorite({
  //   required int hdaiahId,
  //   required String hdaiahType,
  // }) async {
  //   var favoritedHdaiah = await getFavoritedHdaiat(
  //     hdaiahId: hdaiahId,
  //     hdaiatType: hdaiahType,
  //   );
  //   var result = await HdaiatSqlHelper.removeHdaiahFromFavorite(
  //     hdaiahId: hdaiahId,
  //     hdaiahType: hdaiahType,
  //   );
  //   if (result != 0 && favoritedHdaiah?['server_id'] != null) {
  //     await addToSync(
  //       'favorited_hdaiat',
  //       favoritedHdaiah?['server_id'] as int? ?? 0,
  //       SyncAction.delete,
  //     );
  //   }
  //   return result;
  // }

  // add to sync
  static Future<int> addToSync(
    String tableName,
    int recordId,
    SyncAction action,
  ) async {
    var db = await _db;
    var result = await db?.insert(userSyncTableName, {
      'table_name': tableName,
      'record_id': recordId,
      'action': action.value,
    });
    return result ?? 0;
  }

  static Future<List<Map<String, dynamic>>> getAllUserSyncData() async {
    var db = await _db;
    var result = await db?.rawQuery('SELECT * FROM ${userSyncTableName}');
    return result ?? [];
  }

  static Future<Map<String, Note>> getAllNotes() async {
    var db = await _db;
    var result = await db?.rawQuery('SELECT * FROM notes');
    var notes = <Note>[];
    if (result != null) {
      notes = result.map((e) => Note.fromJson(e)).toList();
    }
    var notesMap = <String, Note>{};
    for (var note in notes) {
      notesMap[UserSyncService.createSuraAyahKey(
          note.suraNumber ?? 0, note.verseNumber ?? 0)] = note;
    }
    return notesMap;
  }

  static Future<Map<String, Bookmark>> getAllBookmarks() async {
    var db = await _db;
    var result = await db?.rawQuery('SELECT * FROM bookmarks');
    var bookmarks = <Bookmark>[];
    if (result != null) {
      bookmarks = result.map((e) => Bookmark.fromJson(e)).toList();
    }
    var bookmarksMap = <String, Bookmark>{};
    for (var bookmark in bookmarks) {
      bookmarksMap[bookmark.type == BookmarkType.page
          ? UserSyncService.createPageKey(bookmark.pageNumber ?? 0)
          : UserSyncService.createSuraAyahKey(
              bookmark.suraNumber ?? 0, bookmark.verseNumber ?? 0)] = bookmark;
    }
    return bookmarksMap;
  }

  static Future<int> addServerId({
    required String table,
    required List<Map<String, dynamic>> mapping,
  }) async {
    var db = await _db;
    if (db == null) {
      return 0; // Return 0 if database is not initialized
    }

    int updatedCount = 0;
    var batch = db.batch(); // Start a batch operation

    for (var map in mapping) {
      final localId = map['local_id'];
      final serverId = map['server_id'];

      if (localId != null && serverId != null) {
        batch.rawUpdate(
          'UPDATE $table SET server_id = ? WHERE id = ?',
          [serverId, localId],
        );
        updatedCount++; // Increment count for each record added to batch
      }
    }
    if (updatedCount > 0) {
      await batch.commit(noResult: true); // Commit the batch operation
    }
    return updatedCount;
  }

  static Future<void> emptyUserSyncTable() async {
    var db = await _db;
    await db?.delete(userSyncTableName);
  }

  static Future<Map<String, Object?>?> getFavoritedHdaiat(
      {required int hdaiahId, required String hdaiatType}) async {
    var db = await _db;
    var favoritedHdaiah = await db?.query('favorited_hdaiat',
        where: 'hdaiah_type = ? AND hdaiah_id = ? ',
        whereArgs: [
          hdaiatType,
          hdaiahId,
        ]);
    return favoritedHdaiah?.firstOrNull;
  }

  static Future<int> deleteRecordsFromServer({
    required String table,
    required List<int> serverIds,
  }) async {
    var db = await _db;
    if (db == null) {
      return 0; // Return 0 if database is not initialized
    }
    var deletedCount = 0;
    var batch = db.batch(); // Start a batch operation

    for (var serverId in serverIds) {
      batch.delete(
        table,
        where: 'server_id = ?',
        whereArgs: [serverId],
      );
      deletedCount++; // Increment count for each record added to batch
    }
    if (deletedCount > 0) {
      await batch.commit(noResult: true); // Commit the batch operation
    }
    return deletedCount;
  }

  static Future<int> updateRecordsFromServer({
    required String table,
    required List<Map<String, dynamic>> records,
  }) async {
    var db = await _db;
    if (db == null) {
      return 0; // Return 0 if database is not initialized
    }
    int updatedCount = 0;
    var batch = db.batch(); // Start a batch operation

    for (var record in records) {
      final serverId = record['server_id'];

      if (serverId != null) {
        batch.update(
          table,
          record, // Assuming the record map has all the fields for the table
          where: 'server_id = ?',
          whereArgs: [serverId],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        updatedCount++; // Increment count for each record added to batch
      }
    }
    if (updatedCount > 0) {
      await batch.commit(noResult: true); // Commit the batch operation
    }
    return updatedCount;
  }

  static Future<void> emptyTables() async {
    var db = await _db;
    var tables = ['bookmarks', 'notes'];
    for (var table in tables) {
      await db?.delete(table);
    }
  }

  static Future<void> addToTable(
      {required String table,
      required List<Map<String, dynamic>> records}) async {
    var db = await _db;
    var batch = db?.batch();
    if (batch == null) return;
    for (var record in records) {
      record.remove('user_id');
      record.remove('updated_at');
      batch.insert(table, record, conflictAlgorithm: ConflictAlgorithm.replace);
    }
    await batch.commit(noResult: true);
  }
}
