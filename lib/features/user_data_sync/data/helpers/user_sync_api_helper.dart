import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
part 'user_sync_api_helper.g.dart';

@RestApi(baseUrl: 'https://api.tadars.com/v1/')
abstract class UserSyncApiHelper {
  factory UserSyncApiHelper(Dio dio, {String baseUrl}) = _UserSyncApiHelper;

  @GET('check-new-records')
  Future<HttpResponse> checkNewRecords({
    @Queries() required Map<String, dynamic> queries,
  });

  @GET('check-updated-records')
  Future<HttpResponse> checkUpdatedRecords({
    @Query('last_sync_id') required int lastSyncId,
  });

  @GET('get-new-records')
  Future<HttpResponse> getNewRecords({
    @Queries() required Map<String, dynamic> queries,
  });

  @GET('get-updated-records')
  Future<HttpResponse> getUpdatedRecords({
    @Query('last_sync_id') required int lastSyncId,
    @Query('table_name') required String table,
  });

  @POST('sync')
  Future<HttpResponse> sync({
    @Body() required Map<String, dynamic> body,
  });
}
