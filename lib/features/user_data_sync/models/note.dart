import '../data/helpers/user_sync_db_helper.dart';

class Note {
  int? id;
  int? serverId;

  int? suraNumber;
  int? verseNumber;
  String? note;
  DateTime? createdAt;
  Note({
    required this.suraNumber,
    required this.note,
    required this.createdAt,
    required this.verseNumber,
    this.id,
    this.serverId,
  });

  //from json

  factory Note.fromJson(Map<String, dynamic> json) {
    return Note(
      suraNumber: json['sura_number'],
      verseNumber: json['verse_number'],
      note: json['note'],
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at']),
      id: json['id'],
      serverId: json['server_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (id != null) {
      data['id'] = id;
    }
    if (suraNumber != null) {
      data['sura_number'] = suraNumber;
    }
    if (verseNumber != null) {
      data['verse_number'] = verseNumber;
    }
    if (note != null) {
      data['note'] = note;
    }
    if (createdAt != null) {
      data['created_at'] = createdAt?.toIso8601String();
    }
    if (serverId != null) {
      data['server_id'] = serverId;
    }
    return data;
  }

  void delete() {
    UserSyncDBHelper.deleteNote(suraNumber ?? 0, verseNumber ?? 0);
  }
}
