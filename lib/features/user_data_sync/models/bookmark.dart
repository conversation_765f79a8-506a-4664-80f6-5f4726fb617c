import '../data/enums/bookmark_type.dart';
import '../data/services/user_sync_service.dart';

class Bookmark {
  int? id;
  int? serverId;
  int? pageNumber;
  int? suraNumber;
  int? colorIndex;
  int? verseNumber;
  DateTime createdAt;
  BookmarkType? type;

  Bookmark({
    this.pageNumber,
    this.suraNumber,
    required this.colorIndex,
    this.verseNumber,
    required this.createdAt,
    required this.type,
    this.id,
    this.serverId,
  });

  //from json
  factory Bookmark.fromJson(Map<String, dynamic> json) {
    return Bookmark(
      pageNumber: json['page_number'],
      suraNumber: json['sura_number'],
      colorIndex: json['color_index'],
      verseNumber: json['verse_number'],
      createdAt: DateTime.parse(json['created_at']),
      type: json['type'] == 1 ? BookmarkType.verse : BookmarkType.page,
      id: json['id'],
      serverId: json['server_id'],
    );
  }

  //to json
  Map<String, dynamic> toJson() {
    var data = <String, dynamic>{};
    if (pageNumber != null) {
      data['page_number'] = pageNumber;
    }
    if (suraNumber != null) {
      data['sura_number'] = suraNumber;
    }
    if (colorIndex != null) {
      data['color_index'] = colorIndex;
    }
    if (verseNumber != null) {
      data['verse_number'] = verseNumber;
    }
    if (createdAt != null) {
      data['created_at'] = createdAt.toIso8601String();
    }
    if (type != null) {
      data['type'] = type!.value;
    }
    if (id != null) {
      data['id'] = id;
    }
    if (serverId != null) {
      data['server_id'] = serverId;
    }
    return data;
  }

  void delete() {
    UserSyncService.instance.removeFromBookmarks(
        suraNumber: suraNumber,
        verseNumber: verseNumber,
    );
  }
}
