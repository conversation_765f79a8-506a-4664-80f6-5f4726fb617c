class Translation {
  int id;
  int ayahNumber;
  int surahNumber;
  int bookId;
  String content;
  String textAlignment;

  Translation({
    required this.id,
    required this.ayahNumber,
    required this.surahNumber,
    required this.bookId,
    required this.content,
    required this.textAlignment,
  });

  factory Translation.fromJson(json) => Translation(
        ayahNumber: int.parse(json['ayah_number'].toString()),
        bookId: int.parse(json['book_id'].toString()),
        content: json['content'].toString(),
        id: int.parse(json['id'].toString()),
        surahNumber: int.parse(json['surah_number'].toString()),
        textAlignment: json['text_alignment'].toString(),
      );
}
