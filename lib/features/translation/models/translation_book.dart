class TranslationBook {
  int id;

  String name;
  String arabicName;
  String authorName;
  String arabicAuthorName;
  int sort;

  TranslationBook({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.authorName,
    required this.arabicAuthorName,
    required this.sort,
  });

  factory TranslationBook.fromJson(json) => TranslationBook(
        name: (json['name'].toString()),
        arabicName: json['arabic_name'].toString(),
        arabicAuthorName: json['arabic_author_name'].toString(),
        authorName: json['author_name'].toString(),
        id: int.parse(json['id'].toString()),
        sort: int.parse(json['sort'].toString()),
      );
}
