import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart' hide QuranPage;
import 'package:tadars/controllers/home_controller.dart';

import 'package:tadars/features/translation/translation_sql_helper.dart';
import 'package:tadars/features/translation/widgets/verse_translation.dart';
import 'package:tadars/features/translation/widgets/verse_widget.dart';

import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/quran_suar_icons.dart';
import 'package:tadars/view/pages/home/<USER>/page_background.dart';
import 'package:tadars/view/pages/home/<USER>/page_footer.dart';
import 'package:tadars/view/pages/home/<USER>/page_header.dart';

import '../../../helpers/boxes_helper.dart';
import '../models/translation_book.dart';
import 'translation_picker_button.dart';

class TranslationPageView extends StatefulWidget {
  const TranslationPageView({super.key, this.tapBar});
  final Widget? tapBar;
  @override
  State<TranslationPageView> createState() => _TranslationPageViewState();
}

class _TranslationPageViewState extends State<TranslationPageView> {
  var bookId = BoxesHelper.getTranslationbookId();
  @override
  Widget build(BuildContext context) {
    //

    Map<int, Page> pages = HomeController.to.quranPageController.pages ?? {};
    var height = (MediaQuery.of(context).size.height);
    double newheight =
        height -
        Get.mediaQuery.viewPadding.top -
        Get.mediaQuery.viewPadding.bottom;
    if (kDebugMode) {
      print(Get.mediaQuery.viewPadding.top);
    }
    var lastPage = HomeController.to.getLastPage();
    HomeController.to.quranPageController.pageController = PageController(
      initialPage: lastPage - 1,
    );

    return PageView.builder(
      onPageChanged: (i) {
        var page = pages[i + 1];
        HomeController.to.quranPageController.pageController.jumpToPage(i);

        HomeController.to.currentPage.value = page?.number ?? 1;
        HomeController.to.currentPageNumber.value = page?.number ?? 1;
        HomeController.to.saveLastPageAndSura(
          page?.number ?? 1,
          page?.surahNumber ?? 1,
        );
        // var lastLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
        //     .values
        //     .lastWhere((element) => element.pageNumber == page.pageNumber);
        HomeController.to.lastVerseNumberInPage.value =
            page?.lastVerseNumber ?? 1;
        HomeController.to.lastSuraNumberInPage.value =
            page?.lastSurahNumber ?? 1;
        // var firstLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
        //     .values
        //     .firstWhere(
        //         (element) => element.pageNumber == page!.partNumber);
        HomeController.to.firstVerseNumberInPage.value =
            page?.firstVerseNumber ?? 1;
        HomeController.to.firstSuraNumberInPage.value =
            page?.firstSurahNumber ?? 1;
      },
      controller: HomeController.to.quranPageController.pageController,
      itemCount: pages.length,
      dragStartBehavior: DragStartBehavior.down,
      itemBuilder: (context, i) {
        var page = pages[i + 1];
        List<Verse> verses = [];

        for (var surah in page!.surahs) {
          verses.addAll(surah.verses);
        }

        bool isPortrait =
            (HomeController.to.pageWidth / HomeController.to.pageHeight) >
            (context.mediaQuery.size.width / context.mediaQuery.size.height);
        return SingleChildScrollView(
          child: SizedBox(
            height: newheight,
            width: Get.width,
            child: Stack(
              children: [
                if (isPortrait) PageBackground(isLeft: i.isEven),
                GestureDetector(
                  onTap: () {
                    HomeController.to.toggleShowOverlay();
                  },
                  child: Container(
                    color: HomeController.to.pageColor.value?.withValues(
                      alpha: 0.2,
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        PageHeader(
                          suraName: page.surahs.first.name,
                          pageNumber: i + 1,
                        ),
                        if (widget.tapBar != null) widget.tapBar!,
                        Expanded(
                          child: Stack(
                            children: [
                              ListView.builder(
                                primary: true,
                                shrinkWrap: true,
                                padding: const EdgeInsets.only(bottom: 40),
                                itemCount: verses.length,
                                itemBuilder: (context, index) {
                                  return Builder(
                                    builder: (ctx) {
                                      return Column(
                                        children: [
                                          if (verses[index].number == 1)
                                            Container(
                                              margin: const EdgeInsets.only(
                                                top: 8,
                                              ),
                                              child: Column(
                                                children: [
                                                  _buildSuraHeader(
                                                    verses[index],
                                                  ),
                                                  _builBasml(verses[index]),
                                                ],
                                              ),
                                            ),
                                          Column(
                                            children: [
                                              VerseWidget(verse: verses[index]),
                                              VerseTranslation(
                                                verseNumber:
                                                    verses[index].number,
                                                suraNumber:
                                                    verses[index].surahNumber,
                                                bookId: bookId,
                                              ),
                                            ],
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                              PositionedDirectional(
                                bottom: 0,
                                end: 0,
                                child: FutureBuilder<List<TranslationBook>>(
                                  future:
                                      TranslationSqlHelper.getTranslationBooks(),
                                  builder: (ctx, snapshot) {
                                    if (snapshot.connectionState ==
                                            ConnectionState.waiting ||
                                        (snapshot.data?.isEmpty ?? true)) {
                                      return SizedBox.shrink();
                                    }
                                    return TranslationPickerButton(
                                      books: snapshot.data ?? [],
                                      defaultBook: snapshot.data!.firstWhere(
                                        (book) => book.id == bookId,
                                      ),
                                      onBookChange: (book) {
                                        setState(() {
                                          bookId = book.id;
                                          BoxesHelper.setTranslationbookId(
                                            book.id,
                                          );
                                        });
                                      },
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        PageFooter(
                          partNumber: page.partNumber,
                          quarter: page.quarter,
                          quarterNumber: page.quarterNumber,
                          pageNumber: page.number,
                          hizbNumber: page.hizbNumber,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void onPageChange(int index) {}

  Stack _buildSuraHeader(Verse verse) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Image.asset(
          "assets/images/header.png",
          color:
              HomeController.to.pageColor.value == Colors.white
                  ? Get.theme.primaryColor
                  : HomeController.to.pageColor.value,
          colorBlendMode: BlendMode.srcATop,
          fit: BoxFit.fill,
        ),
        SizedBox(
          width: double.maxFinite,
          child: Icon(verse.surahNumber.toSuraIconData(), size: 23),
        ),
      ],
    );
  }

  Widget _builBasml(Verse verse) {
    if (verse.surahNumber != 1 && verse.surahNumber != 9) {
      return Container(
        padding: const EdgeInsets.only(top: 8),
        width: double.maxFinite,
        child: const Icon(QuranSuarIcons.basmala, size: 35),
      );
    }

    return SizedBox.shrink();
  }
}
