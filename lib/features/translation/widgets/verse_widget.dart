import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';

import '../../../controllers/home_controller.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/common_constants.dart';
import '../../../view/pages/home/<USER>/verse_marker.dart';

class VerseWidget extends StatelessWidget {
  const VerseWidget({super.key,required this.verse});
  final Verse verse;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HomeController.to.toggleShowOverlay();
      },
      onLongPress: () {
        HomeController.to.openVerseOptionsDailog(
          suraNumber: verse.surahNumber,
          verseNumber: verse.number,
          pageNumber: verse.pageNumber,
        );
      },
      onTapDown: (detail) {
        HomeController.to.selectedVerseNumber.value = verse.number;
        HomeController.to.selectedSuraNumber.value = verse.surahNumber;
      },
      onTapUp: (detail) {
        if (!QuranAudioService.playing.value) {
          HomeController.to.selectedVerseNumber.value = 0;
          HomeController.to.selectedSuraNumber.value = 0;
        } else {
          HomeController.to.selectedVerseNumber.value =
              QuranAudioService.currentVerse.value?.number ?? 0;
          HomeController.to.selectedSuraNumber.value =
              QuranAudioService.currentSurah.value?.number ?? 0;
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: CommonStyles.borderRadius,
          color: (verse.number ==
                      HomeController.to.selectedVerseNumber.value &&
                 verse.surahNumber ==
                      HomeController.to.selectedSuraNumber.value)
              ? Get.isDarkMode ||
                      HomeController.to.pageColor.value == Colors.white
                  ? Get.theme.primaryColor.withValues(alpha:0.3)
                  : HomeController.to.pageColor.value?.withValues(alpha:0.4)
              : null,
        ),
        child: Text.rich(
          TextSpan(
            text: "${verse.text} ",
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: VerseMarker(
                  size: HomeController.to.pageFontSize.value * 1.2,
                  verseNumber: verse.number,
                  suraNumber: verse.surahNumber,
                ),
              ),
            ],
          ),
          style: TextStyle(
            fontSize: HomeController.to.pageFontSize.value,
            fontFamily: CommonConstants.hafsFontFamily,
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
