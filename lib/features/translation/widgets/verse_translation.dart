import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/home_controller.dart';
import '../../../utils/common_styles.dart';
import '../models/translation.dart';
import '../translation_sql_helper.dart';

class VerseTranslation extends StatelessWidget {
  final int verseNumber;
  final int suraNumber;
  final int bookId;
  const VerseTranslation(
      {super.key,
      required this.verseNumber,
      required this.suraNumber,
      required this.bookId});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Translation?>(
      future:
          TranslationSqlHelper.getTranslations(bookId, suraNumber, verseNumber),
      builder: (BuildContext context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox.shrink();
          // return Container(
          //     padding: const EdgeInsets.all(16),
          //     width: double.maxFinite,
          //     margin: const EdgeInsets.symmetric(horizontal: 8),
          //     decoration: BoxDecoration(
          //       color: HomeController.to.pageColor.value == Colors.white
          //           ? Get.theme.primaryColor.withValues(alpha:0.2)
          //           : HomeController.to.pageColor.value?.withValues(alpha:0.2),
          //       borderRadius: CommonStyles.borderRadius,
          //     ),
          //     child: Column(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Shimmer.fromColors(
          //           baseColor: Colors.white,
          //           highlightColor: Colors.white54,
          //           direction: ShimmerDirection.rtl,
          //           // enabled: false,

          //           // period: const Duration(seconds: 3),
          //           child: Container(
          //             decoration: BoxDecoration(
          //                 color: Colors.white,
          //                 borderRadius: BorderRadius.circular(20)),
          //             height: 20,
          //             width: double.maxFinite,
          //           ),
          //         ),
          //       ],
          //     ));
        }

        Translation? translation = snapshot.data;
        if (translation == null) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            width: double.maxFinite,
            decoration: BoxDecoration(
              color: Get.isDarkMode
                  ? Get.theme.primaryColor.withValues(alpha: 0.3)
                  : HomeController.to.pageColor.value == Colors.white
                      ? Get.theme.primaryColor.withValues(alpha: 0.2)
                      : HomeController.to.pageColor.value
                          ?.withValues(alpha: 0.2),
              borderRadius: CommonStyles.borderRadius,
            ),
            child: Center(
              child: Text(
                "لاتوجد مادة في هذا القسم".tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode
                      ? Colors.white
                      : HomeController.to.pageColor.value == Colors.white
                          ? Get.theme.primaryColor
                          : HomeController.to.pageColor.value,
                ),
              ),
            ),
          );
        }
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: Get.isDarkMode
                ? Get.theme.primaryColor.withValues(alpha: 0.3)
                : HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor.withValues(alpha: 0.2)
                    : HomeController.to.pageColor.value?.withValues(alpha: 0.2),
            borderRadius: CommonStyles.borderRadius,
          ),
          child: Text(
            translation.content,
            // textAlign: TextAlign.end,
            textDirection: TextDirection.ltr,
          ),
        );
      },
    );
  }
}
