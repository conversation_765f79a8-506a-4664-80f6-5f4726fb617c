import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tadars/features/translation/widgets/translation_picker_button.dart';
import '../translation_sql_helper.dart';
import 'verse_widget.dart';

import '../../../controllers/home_controller.dart';
import '../../../helpers/boxes_helper.dart';

import '../../../utils/constants/custom_colors.dart';

import 'verse_translation.dart';

class TranslationDialog extends StatefulWidget {
  final int suraNumber;
  final int verseNumber;
  const TranslationDialog({
    super.key,
    required this.suraNumber,
    required this.verseNumber,
  });

  @override
  State<TranslationDialog> createState() => _TranslationDialogState();
}

class _TranslationDialogState extends State<TranslationDialog> {
  var bookId = BoxesHelper.getTranslationbookId();
  @override
  void initState() {
    currentVerse.value = widget.verseNumber;
    currentSura.value = widget.suraNumber;

    HomeController.to.currentPage.listen((pageNumber) {
      // currentVerse.value =
      //     BoxesHelper.getPageById(pageNumber)?.firstVerseNumber ?? 0;
      // currentSura.value = BoxesHelper.getPageById(pageNumber)?.suraNumber ?? 0;

      //refresh vese
      QuranDatabaseProvider.getVerseByNumber(
        currentSura.value,
        currentVerse.value,
      ).then((value) {
        verse.value = value;
      });
    });

    QuranDatabaseProvider.getVerseByNumber(
      currentSura.value,
      currentVerse.value,
    ).then((value) {
      verse.value = value;
    });
    super.initState();
  }

  var currentVerse = 0.obs;
  var currentSura = 0.obs;
  var verse = Rx<Verse?>(null);

  void goPrevVerse() {
    //
    QuranDatabaseProvider.getVerseByNumber(
      currentSura.value,
      currentVerse.value - 1,
    ).then((prevVerse) {
      if (prevVerse != null) {
        setState(() {
          currentVerse.value = prevVerse.number;
          currentSura.value = prevVerse.surahNumber;
          HomeController.to.goToVerse(currentSura.value, currentVerse.value);

          verse.value = prevVerse;
          print(verse.value?.textWithoutDiacs);
        });
      }
    });
  }

  void goNextVerse() {
    //
    QuranDatabaseProvider.getVerseByNumber(
      currentSura.value,
      currentVerse.value + 1,
    ).then((prevVerse) {
      if (prevVerse != null) {
        setState(() {
          currentVerse.value = prevVerse.number;
          currentSura.value = prevVerse.surahNumber;
          HomeController.to.goToVerse(currentSura.value, currentVerse.value);

          verse.value = prevVerse;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Obx(() {
          print(verse.value?.textWithoutDiacs ?? "");
          return SizedBox(
            width: Get.width * 0.9,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: CustomColors.primaryColor.withValues(alpha: 0.3),
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(10),
                    ),
                  ),
                  width: double.maxFinite,
                  child: Text(
                    "ترجمة الاية".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),

                if (verse.value != null)
                  Container(
                    constraints: BoxConstraints(
                      minHeight: Get.height * 0.3,
                      maxHeight: Get.height * 0.6,
                    ),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          padding: const EdgeInsets.only(
                            right: 16,
                            left: 16,
                            bottom: 50,
                            top: 8,
                          ),
                          child: Column(
                            children: [
                              VerseWidget(verse: verse.value!),
                              VerseTranslation(
                                verseNumber: verse.value!.number,
                                suraNumber: verse.value!.surahNumber,
                                bookId: bookId,
                              ),
                            ],
                          ),
                        ),

                        PositionedDirectional(
                          bottom: 10,
                          end: 10,
                          child:

                           FutureBuilder(
                            future: TranslationSqlHelper.getTranslationBooks(),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                      ConnectionState.waiting ||
                                  (snapshot.data?.isEmpty ?? true)) {
                                return SizedBox.shrink();
                              }
                              return TranslationPickerButton(
                                books: snapshot.data ?? [],
                                defaultBook: snapshot.data!.firstWhere(
                                  (e) => e.id == bookId,
                                ),
                                onBookChange: (bkId) {
                                  setState(() {
                                    bookId = bkId.id;
                                  });
                                },
                              );
                            },
                          ),



                        ),



                      ],
                    ),
                  ),

                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: CustomColors.primaryColor,
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(10),
                    ),
                  ),
                  width: double.maxFinite,
                  child: Row(
                    children: [
                      // close
                      TranslationDialogAction(
                        onTap: () {
                          Get.back();
                        },
                        text: "إغلاق".tr,
                        icon: Icons.close,
                      ),
                      const Spacer(),
                      // previos verse
                      TranslationDialogAction(
                        onTap: () {
                          goPrevVerse();
                        },

                        text: "السابق".tr,
                        icon: Icons.arrow_back,
                      ),
                      const Spacer(),
                      // next verse
                      TranslationDialogAction(
                        onTap: () {
                          goNextVerse();
                        },
                        text: "الآية التالية".tr,
                        icon: Icons.arrow_forward,
                      ),
                      const Spacer(),
                      // share
                      TranslationDialogAction(
                        onTap: () async {
                          String text =
                              (await TranslationSqlHelper.getTranslations(
                                bookId,
                                currentSura.value,
                                currentVerse.value,
                              ))?.content ??
                              "";

                          Share.share(text);
                        },
                        text: "مشاركة".tr,
                        icon: Icons.share,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}

class TranslationDialogAction extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final IconData icon;
  const TranslationDialogAction({
    super.key,
    required this.onTap,
    required this.text,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(icon, color: Colors.white, size: 20),
      ),
    );
  }
}
