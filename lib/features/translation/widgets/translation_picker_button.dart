import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/view/components/bottomsheets/custom_bottom_sheet.dart';

import '../models/translation_book.dart';

class TranslationPickerButton extends StatefulWidget {
  const TranslationPickerButton(
      {super.key,
      required this.books,
      this.onBookChange,
      required this.defaultBook});
  final List<TranslationBook> books;
  final TranslationBook defaultBook;

  final void Function(TranslationBook)? onBookChange;

  @override
  State<TranslationPickerButton> createState() =>
      _TranslationPickerButtonState();
}

class _TranslationPickerButtonState extends State<TranslationPickerButton> {
  TranslationBook? book;

  @override
  void initState() {
    book = widget.defaultBook;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.bottomSheet(
          CustomBottomSheet(
            // title: "كتاب",

            body: Container(
              width: double.maxFinite,
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...widget.books.map(
                    (bk) => Column(
                      children: [
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              Get.back();
                              setState(() {
                                book = bk;
                              });
                              if (widget.onBookChange != null) {
                                widget.onBookChange!(bk);
                              }
                            },
                            child: Container(
                                width: double.maxFinite,
                                padding: EdgeInsets.all(8),
                                child: Text(Get.locale?.languageCode == "ar"
                                    ? bk.arabicName
                                    : bk.name)),
                          ),
                        ),
                        if (bk.id != widget.books.last.id) Divider()
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10)),
        child: Row(
          children: [
            Text(
              Get.locale?.languageCode == "ar"
                  ? book?.arabicName ?? ""
                  : book?.name ?? "",
              style: TextStyle(color: Colors.white),
            ),
            SizedBox(
              width: 2,
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.white,
            )
          ],
        ),
      ),
    );
  }
}
