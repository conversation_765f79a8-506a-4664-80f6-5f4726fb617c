import 'package:sqlbrite/sqlbrite.dart';

import '../../helpers/sql_helper.dart';
import 'models/translation.dart';
import 'models/translation_book.dart';

class TranslationSqlHelper {
  static String translationBooksTable = "translation_books";
  static String translationsTable = "translations";

  static Future<BriteDatabase?> get db => SqlHelper.briteDB;

  static Future<List<TranslationBook>> getTranslationBooks() async {
    var books = (await (await db)?.query(
          translationBooksTable,
          orderBy: "sort ASC",
        ))
            ?.toList() ??
        [];
    return books.map((e) => TranslationBook.fromJson(e)).toList();
  }

  static Future<Translation?> getTranslations(
      int bookId, int surahNumber, int ayahNumber) async {
    var translation = (await (await db)?.query(translationsTable,
                where: "book_id=? and surah_number=? and ayah_number=?",
                whereArgs: [bookId, surahNumber, ayahNumber]))
            ?.toList() ??
        [];
    return Translation.fromJson(translation.firstOrNull);
  }
}
