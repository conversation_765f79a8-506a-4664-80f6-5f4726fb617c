import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/features/intro_verse/widgets/ios_verse_notfication_bottom_sheet.dart';
import 'dart:io' show Platform;

import 'package:tadars/services/notification_service.dart';
import 'package:tadars/helpers/shared_prefrence_helper.dart';

import 'widgets/intro_vese_widget.dart';

class IntroVerseFunction {
  static int verseNotficationId = 9899000;
  static Map<int, String> get verseDuration {
    var value = {
      2: "دقيقتين",
      3: "ثلاث دقائق",
      4: "أربع دقائق",
      5: "خمس دقائق",
      10: "10 دقائق",
      15: "ربع ساعة",
      30: "نصف ساعة",
      60: "ساعة",
    };

    for (var i = 2; i <= 24; i++) {
      value.addAll({i * 60: "$i ساعة"});
    }
    value.addAll({100: ""});

    print("value $value");
    return value;
  }

  static Future<void> showOverlay() async {
    if (Platform.isAndroid) {
      await showAndroidOverlay();
    }
  }

  static Future<void> hideOverlay() async {
    FlutterOverlayWindow.closeOverlay();
  }

  static Future<void> showAndroidOverlay() async {
    bool status = await FlutterOverlayWindow.isPermissionGranted();

    if (status == false) {
      status = await FlutterOverlayWindow.requestPermission() ?? false;
      if (status == false) {
        return;
      }
    }

    await FlutterOverlayWindow.showOverlay(
      height: 0,
      overlayTitle: "تدارس القران",
      overlayContent: "إذ اختفى هذا الاشعار من فضلك قم بفتح البرنامج مرة اخرى",
    );
  }

  static Future<void> showIosVerseNotfication(bool value) async {
    SharedPrefrenceHelper.setVerseNotficationEnable(value);
    if (value) {
      await Get.bottomSheet(IosVerseNotficationBottomSheet());
    }

    initVerseNotfication();
  }

  static void unSchedulVerseNotfication() {
    var notficatonPlugin =
        NotificationService().flutterLocalNotificationsPlugin;

    notficatonPlugin.cancel(verseNotficationId);
  }

  static void initVerseNotfication() async {
    if (await SharedPrefrenceHelper.getVerseNotficationEnable()) {
      var lastVerseId = await SharedPrefrenceHelper.getLastVerseId();

      int minuts = await SharedPrefrenceHelper.getVerseNotficationDuration();

      var verse = await QuranDatabaseProvider.getNextVerseById(lastVerseId);
      schedulVerseNotfication(
        "سورة (${verse!.surahNumber})",

        verse.searchableText,
        minuts,
      );
    } else {
      unSchedulVerseNotfication();
    }
  }

  static void schedulVerseNotfication(String title, String body, int minuts) {
    var notficatonPlugin =
        NotificationService().flutterLocalNotificationsPlugin;

    notficatonPlugin.periodicallyShowWithDuration(
      verseNotficationId,
      title,
      body,
      Duration(minutes: minuts),
      NotificationDetails(
        // Android details
        android: AndroidNotificationDetails(
          verseNotficationId.toString(),
          'verse channel',
          channelDescription: "channel to show verse periodically",
          importance: Importance.max,
          priority: Priority.max,
        ),
        // iOS details
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
    );
  }

  static onVerseNotficationOpend(NotificationResponse details) {
    if (details.id == verseNotficationId) {
      Get.dialog(
        Container(
          constraints: BoxConstraints(maxHeight: Get.height * 0.8),
          child: IntroVersewidget(),
        ),
      );
    }
  }
}
