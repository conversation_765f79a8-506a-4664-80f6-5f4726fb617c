import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:quran_core/quran_core.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/features/translation/translation.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/hive/quran_tafseer.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/extensions.dart';

import '../../../utils/constants/tadars_constants.dart';
import '../../../view/components/waqafat/tdbr_view.dart';
import '../../../view/pages/home/<USER>/page_views/tafseer_page_view.dart';

class IntroVersePage extends StatefulWidget {
  const IntroVersePage({super.key});

  @override
  State<IntroVersePage> createState() => _IntroVersePageState();
}

class _IntroVersePageState extends State<IntroVersePage> {
  // Verse? verse;
  // Surah? surah;
  QuranTafseer? tafseer;

  @override
  void initState() {
    // Verse? lastVerse = BoxesHelper.getLastVerseIntro();

    // HomeController.to.quranPageController.pages!.values
    //     .expand(
    //       (page) => page.surahs.expand(
    //         (surah) => surah.verses.firstWhere((e) => e.id),
    //       ),
    //     )
    //     .toList();

    // verse = surah?.verses.firstWhere(
    //   (verse) => verse.number == lastVerse['verseNumber'],
    // );

    getTafseer();
    super.initState();
  }

  void getTafseer() {
    tafseer = BoxesHelper.getTafseerById(BoxesHelper.getSettingTafseerId());

    if (tafseer == null || !checkHiveTafseerExist(tafseer)) {
      tafseer = BoxesHelper.getTafseerByName('sa3dy');
    }
  }

  bool checkHiveTafseerExist(QuranTafseer? tafseer) {
    if (tafseer == null) {
      return false;
    }
    if (["sa3dy", "sraj"].contains(tafseer.name)) {
      return true;
    }
    if (BoxesHelper.getTafseerFromDownloaded(tafseer.name) != null) {
      return true;
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    Verse? lastVerse = BoxesHelper.getLastVerseIntro();
    print("lastVerse ${lastVerse?.toJson()}");

    return Scaffold(
      body: FutureBuilder(
        future:
            lastVerse == null
                ? QuranDatabaseProvider.getVerseByNumber(1, 1)
                : QuranDatabaseProvider.getNextVerseByNumber(
                  lastVerse.surahNumber,
                  lastVerse.number,
                ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: SingleChildScrollView());
          }

          Verse? verse = snapshot.data;

          return DefaultTabController(
            length: TadarsConstants.getEnabledTdbrTypes().length,
            child: SafeArea(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(top: 16, left: 16, right: 16),
                child: Column(
                  children: [
                    _buildSuraHeader(verse?.surahNumber),
                    _buildVerseCard(verse),
                    _buildLastReadTile(verse),
                    _buildTafseerCard(verse),
                    TdbrTab(verse: verse),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Container _buildSuraHeader(int? surahNumber) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Get.theme.primaryColor.withValues(alpha: 0.1),
      ),
      margin: EdgeInsets.only(bottom: 16),
      width: double.maxFinite,
      child: FutureBuilder(
        future:
            surahNumber == null
                ? null
                : QuranDatabaseProvider.getSurahByNumber(surahNumber),
        builder: (context, snapshot) {
          Surah? surah = snapshot.data;
          return Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                "assets/images/header.png",
                // width: 100,
                // height: 100,
              ),

              Text(
                surah?.unicode ?? "",
                style: TextStyle(
                  fontSize: 24,
                  color: CustomColors.primaryColor,
                  fontFamily: "suar_name",
                  package: "quran_core",
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Container _buildTafseerCard(Verse? verse) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            // margin: EdgeInsets.only(top: 8),
            padding: EdgeInsets.only(left: 16, right: 16, bottom: 8, top: 50),
            decoration: BoxDecoration(
              color: CustomColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),

            child: FutureBuilder(
              future: BoxesHelper.getTafseersByVerseNumber(
                tafseer!.name,
                verse?.surahNumber ?? 1,
                verse?.number ?? 1,
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }
                return Column(
                  children: [
                    Divider(color: CustomColors.accentColor, thickness: 2),
                    snapshot.data != null
                        ? TafseerHtmlView(htmlText: snapshot.data!.text)
                        : Text(
                          "لايوجد تفسير لهذه الآية".tr,
                          textAlign: TextAlign.center,
                        ),
                    Divider(color: CustomColors.accentColor, thickness: 2),
                    Text(
                      tafseer?.title ?? "",
                      style: TextStyle(
                        color: CustomColors.primaryColor,
                        fontSize: 18,
                        fontFamily: CommonConstants.coconFontFamily,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          Positioned(
            top: 0,
            // left: 0,
            // right: 0,
            child: Container(
              padding: EdgeInsets.only(left: 4, right: 4, bottom: 4),

              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 20),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor,
                  borderRadius: BorderRadius.vertical(
                    bottom: Radius.circular(8),
                  ),
                ),
                child: Text(
                  "تفسير",
                  style: TextStyle(
                    fontFamily: CommonConstants.coconFontFamily,
                    color: Colors.white,
                    fontSize: 17,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastReadTile(Verse? verse) {
    var page =
        HomeController.to.quranPageController.pages?[Hive.box(
          Boxes.settings,
        ).get(SettingsConstants.lastPageKey, defaultValue: 1)];
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 4),
            child: Text(
              "اخر قراءه".tr,
              style: TextStyle(
                fontSize: 20,
                color: CustomColors.primaryColor,
                fontFamily: CommonConstants.coconFontFamily,

                // fontWeight: FontWeight.bold,
              ),
            ),
          ),

          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: CustomColors.primaryColor.withValues(alpha: 0.1),
            ),
            child: Container(
              margin: EdgeInsetsDirectional.only(end: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: CustomColors.primaryColor.withValues(alpha: 0.3),
              ),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.all(8),
                    child: Text(
                      page?.surahs.first.unicode ?? "",
                      style: TextStyle(
                        fontSize: 24,
                        color: CustomColors.primaryColor,
                        fontFamily: "suar_name",
                        package: "quran_core",
                      ),
                    ),
                  ),

                  Container(
                    decoration: BoxDecoration(color: CustomColors.accentColor),
                    width: 1,
                    height: 25,
                    margin: EdgeInsets.symmetric(horizontal: 8),
                  ),
                  Container(
                    margin: EdgeInsets.all(8),
                    child: Text(
                      "${"رقم الصفحة".tr}: ${page?.number.toArabicNumber() ?? ""}",
                      style: TextStyle(
                        fontSize: 16,
                        color: CustomColors.primaryColor,
                        fontFamily: CommonConstants.coconFontFamily,

                        // fontFamily: CommonConstants.kitabFontFamily,

                        // fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Spacer(),

                  InkWell(
                    onTap: () {
                      BoxesHelper.setLastVerseIntro(verse);
                      Get.offAndToNamed(Routes.homePage);
                    },
                    child: Container(
                      padding: EdgeInsets.all(6),
                      margin: EdgeInsetsDirectional.only(end: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: CustomColors.accentColor.withValues(alpha: 0.7),
                      ),
                      child: Icon(
                        Icons.keyboard_double_arrow_left,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container _buildVerseCard(Verse? verse) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      width: double.maxFinite,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CustomColors.primaryColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: CustomColors.primaryColor.withValues(alpha: 0.3),
            spreadRadius: 1,
            // blurRadius: 2,
            offset: Offset(2, 4), // changes position of shadow
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: " ﴿ ",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: CommonConstants.hafsFontFamily,
                    ),
                  ),
                  TextSpan(
                    text: verse?.text,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontFamily: CommonConstants.hafsFontFamily,
                      // fontFamily:
                      //     "mushaf_${HomeController.to.quranPageController.mushafId}_page_${verse.pageNumber}",
                    ),
                  ),

                  TextSpan(
                    text: " ﴾ ",
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),

            // Text(
            //   "[ الفاتحة: 2]",
            //   style: TextStyle(color: Colors.white, fontSize: 14),
            // ),
            FutureBuilder(
              future: TranslationSqlHelper.getTranslations(
                BoxesHelper.getTranslationbookId(),
                verse?.surahNumber ?? 1,
                verse?.number ?? 1,
              ),
              builder: (context, snapshot) {
                return Text(
                  snapshot.data?.content ?? "",
                  textDirection: TextDirection.ltr,
                  style: TextStyle(fontFamily: CommonConstants.coconFontFamily),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class TdbrTab extends StatefulWidget {
  Verse? verse;

  TdbrTab({super.key, this.verse});
  @override
  State<TdbrTab> createState() => _TdbrTabState();
}

class _TdbrTabState extends State<TdbrTab> {
  late TdbrType tdbrType;

  @override
  void initState() {
    tdbrType = TadarsConstants.getEnabledTdbrTypes()[0];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0, left: 8, bottom: 8),
          child: Theme(
            data: ThemeData(
              tabBarTheme: TabBarTheme(
                tabAlignment: TabAlignment.start,
                indicatorColor:
                    Get.isDarkMode
                        ? Colors.white
                        : HomeController.to.pageColor.value == Colors.white ||
                            Get.isDarkMode
                        ? Get.theme.primaryColor
                        : HomeController.to.pageColor.value,
                labelColor:
                    Get.isDarkMode
                        ? Colors.white
                        : HomeController.to.pageColor.value == Colors.white
                        ? Get.theme.primaryColor
                        : HomeController.to.pageColor.value,
                unselectedLabelStyle: TextStyle(
                  color: Get.isDarkMode ? Colors.white : null,
                  fontFamily: Get.textTheme.titleSmall?.fontFamily,
                ),
                labelStyle: TextStyle(
                  fontFamily: Get.textTheme.titleSmall?.fontFamily,
                ),
              ),
            ),
            child: TabBar(
              onTap: (index) {
                setState(() {
                  tdbrType = TadarsConstants.getEnabledTdbrTypes()[index];
                });
              },
              isScrollable: true,
              tabs:
                  TadarsConstants.getEnabledTdbrTypes()
                      .map((e) => Tab(text: e.name.tr))
                      .toList(),
            ),
          ),
        ),

        Builder(
          builder: (context) {
            return FutureBuilder(
              future: SqlHelper.getVerseTdbrs(
                widget.verse?.surahNumber ?? 1,
                widget.verse?.number ?? 1,
                tdbrType,
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    width: double.maxFinite,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color:
                          HomeController.to.pageColor.value == Colors.white
                              ? Get.theme.primaryColor.withValues(alpha: 0.2)
                              : HomeController.to.pageColor.value?.withValues(
                                alpha: 0.2,
                              ),
                      borderRadius: CommonStyles.borderRadius,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: Colors.white54,
                          direction: ShimmerDirection.rtl,
                          // enabled: false,

                          // period: const Duration(seconds: 3),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            height: 20,
                            width: double.maxFinite,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                var tdbrs = snapshot.data ?? [];
                if (tdbrs.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.symmetric(vertical: 32),
                    decoration: BoxDecoration(
                      color:
                          Get.isDarkMode
                              ? Get.theme.primaryColor.withValues(alpha: 0.2)
                              : HomeController.to.pageColor.value ==
                                  Colors.white
                              ? Get.theme.primaryColor.withValues(alpha: 0.2)
                              : HomeController.to.pageColor.value,
                      borderRadius: CommonStyles.borderRadius,
                    ),
                    child: Center(
                      child: Text(
                        "لاتوجد مادة في هذا القسم".tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              Get.isDarkMode
                                  ? Colors.white
                                  : HomeController.to.pageColor.value ==
                                      Colors.white
                                  ? Get.theme.primaryColor
                                  : HomeController.to.pageColor.value,
                        ),
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  shrinkWrap: true,

                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: snapshot.data?.length ?? 0,
                  itemBuilder: (ctx, index) {
                    return TdbrView(
                      tdbr: snapshot.data![index],
                      tdbrType: tdbrType,
                    );
                  },
                );
              },
            );
          },
        ),
      
      
      
      ],
    );
  }
}
