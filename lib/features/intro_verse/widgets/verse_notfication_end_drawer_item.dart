import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';

import 'package:tadars/features/intro_verse/intro_verse_function.dart';
import 'package:tadars/features/intro_verse/widgets/ios_verse_notfication_bottom_sheet.dart';
import 'package:tadars/helpers/shared_prefrence_helper.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class VerseNotficationEndDrawerItem extends StatefulWidget {
  const VerseNotficationEndDrawerItem({super.key});

  @override
  State<VerseNotficationEndDrawerItem> createState() =>
      _VerseNotficationEndDrawerItemState();
}

class _VerseNotficationEndDrawerItemState
    extends State<VerseNotficationEndDrawerItem> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: SharedPrefrenceHelper.getVerseNotficationEnable(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox.shrink();
        }
        var enabled = snapshot.data ?? false;
        return InkWell(
          onTap: () async {
            await Get.bottomSheet(IosVerseNotficationBottomSheet());
            IntroVerseFunction.initVerseNotfication();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "تنبيهات الايات الدورية".tr,
                  style: const TextStyle(fontSize: 16),
                ),
                FlutterSwitch(
                  activeColor: CustomColors.accentColor,
                  height: 27,
                  width: 50,
                  onToggle: (bool value) async {
                    IntroVerseFunction.showIosVerseNotfication(value);

                    setState(() {});
                  },
                  value: enabled,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
