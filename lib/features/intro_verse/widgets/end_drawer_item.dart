import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/state_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tadars/features/intro_verse/intro_verse_function.dart';
import 'package:tadars/features/intro_verse/widgets/ios_verse_notfication_bottom_sheet.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class EndDrawerItem extends StatefulWidget {
  const EndDrawerItem({super.key});

  @override
  State<EndDrawerItem> createState() => _EndDrawerItemState();
}

class _EndDrawerItemState extends State<EndDrawerItem> {
  Future<bool> showVeserWhenUnlock() async {
    // Obtain shared preferences.
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('show_verse_when_unlock') ?? false;
  }

  Future<void> setShowVerseWhenUnlock(enable) async {
    // Obtain shared preferences.
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('show_verse_when_unlock', enable);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: showVeserWhenUnlock(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox.shrink();
        }
        var enabled = snapshot.data ?? false;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "عرض اية عند فتح الشاشة".tr,
                style: const TextStyle(fontSize: 16),
              ),
              FlutterSwitch(
                activeColor: CustomColors.accentColor,
                height: 27,
                width: 50,
                onToggle: (bool value) async {
                  if (value) {
                    IntroVerseFunction.showOverlay();
                  } else {
                    IntroVerseFunction.hideOverlay();
                  }
                  // Get.bottomSheet(IosVerseNotficationBottomSheet());
                  setShowVerseWhenUnlock(value);
                  setState(() {});
                },
                value: enabled,
              ),
            ],
          ),
        );
      },
    );
  }
}
