import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:tadars/view/components/buttons/custom_outlined_button.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';
import 'package:tadars/view/components/dialogs/source_waqafat_dialog.dart';
import 'package:tadars/view/components/dialogs/tadars_dialog.dart';
import 'package:tadars/view/components/form_fields/custom_number_form_field.dart';
import 'package:tadars/view/components/waqafat/links_view.dart';
import 'package:tadars/view/components/waqafat/tdbr_action.dart';
import 'package:tadars/view/components/waqafat/verses_view.dart';
import 'package:tadars/view/components/waqafat/video_preview.dart';

import '../../../controllers/home_controller.dart';
import '../../../controllers/tadars_controller.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../helpers/sql_helper.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../../utils/constants/tadars_constants.dart';

class TdbrView extends StatelessWidget {
  const TdbrView({
    super.key,
    required this.tdbr,
    required this.tdbrType,
    this.isSuraTdbr = false,
    this.showTdbTitle = false,
  });

  final Map<String, dynamic> tdbr;
  final TdbrType tdbrType;
  final bool isSuraTdbr;
  final bool showTdbTitle;

  @override
  Widget build(BuildContext context) {
    if (tdbr.isEmpty) {
      return Container();
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        borderRadius: CommonStyles.borderRadius,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showTdbTitle)
            Text(
              TadarsConstants.tdbrTranlation[TadarsConstants
                      .tdbrMappingForDb[tdbrType]] ??
                  "",
              style: const TextStyle(color: Colors.grey),
            ),

          if ((tdbr['verses']?.length ?? 0) > 0)
            VersesView(verses: tdbr['verses']),
          SizedBox(
            width: double.maxFinite,
            child: Builder(
              builder: (context) {
                var markIndex = tdbr['fulltext'].toString().indexOf(",");

                if (markIndex == -1) {
                  return Text(
                    tdbr['fulltext'].toString(),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      fontFamily: CommonConstants.adwaaAlsalafFontFamily,
                    ),
                  );
                }
                return Text.rich(
                  TextSpan(
                    text: tdbr['fulltext'].toString().substring(
                      0,
                      markIndex - 1,
                    ),
                    children: [
                      const TextSpan(
                        text: ",",
                        style: TextStyle(
                          fontFamily: CommonConstants.amiriFontFamily,
                        ),
                      ),
                      TextSpan(
                        text: tdbr['fulltext'].toString().substring(
                          markIndex + 1,
                        ),
                      ),
                    ],
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      fontFamily: CommonConstants.adwaaAlsalafFontFamily,
                    ),
                  ),
                );
              },
            ),
          ),
         
         
         
         
          Container(
            alignment: Alignment.bottomLeft,
            child: InkWell(
              onTap: () {
                Get.dialog(
                  SourceWaqafatDialog(
                    sourceId: tdbr['source'],
                    sourceName: tdbr['source_name'],
                    sourceDetail: tdbr['source_details'] ?? '',
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "المصدر: ".tr + (tdbr['source_name'] ?? ""),
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ),
          ),
          // LinksView(links: tdbr['links']),
          const Divider(height: 16),

          //to add likes and shares in bottom of tadaboor
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.spaceAround,
          //   children: [
          //     TdbrAction(
          //       selected: BoxesHelper.isTdbrExistInBookmark(
          //         tdbr["id"],
          //         TadarsConstants.tdbrMapping[tdbrType] ?? "",
          //       ),
          //       selectedIcon: Icons.star,
          //       icon: Icons.star_outline_rounded,
          //       label: 'تفضيل'.tr,
          //       selectedLabel: 'الغاء التفضيل'.tr,
          //       onTap: () {
          //         if (BoxesHelper.isTdbrExistInBookmark(
          //           tdbr["id"],
          //           TadarsConstants.tdbrMapping[tdbrType] ?? "",
          //         )) {
          //           BoxesHelper.removeTdbrFromBookmark(
          //             tdbr["id"],
          //             TadarsConstants.tdbrMapping[tdbrType] ?? "",
          //           );
          //         } else {
          //           BoxesHelper.addTdbrToBookmark(
          //             tdbr["id"],
          //             TadarsConstants.tdbrMapping[tdbrType] ?? "",
          //           );
          //         }
          //       },
          //     ),
          //     TdbrAction(
          //       icon: Icons.warning_amber_rounded,
          //       label: 'إبلاغ',
          //       onTap: () async {
          //         SystemChrome.setEnabledSystemUIMode(
          //           SystemUiMode.manual,
          //           overlays: [],
          //         );
          //         var formKey = GlobalKey<FormState>();
          //         String reason = "";
          //         await Get.dialog(
          //           TadarsDialog(
          //             title: 'الإبلاغ عن محتوى مخالف!',
          //             content: Form(
          //               key: formKey,
          //               child: Column(
          //                 children: [
          //                   const Text(
          //                     'يرجى كتابة توضيح عن سبب تصنيفك لهذه المادة بإنها مخالفة!',
          //                     textAlign: TextAlign.center,
          //                   ),
          //                   Container(
          //                     margin: const EdgeInsets.only(top: 4),
          //                     child: CustomTextFormField(
          //                       // titleText: "السبب".tr,
          //                       hintText: "اكتب توضيح سبب المخالفة".tr,
          //                       maxLine: 2,
          //                       validator: (value) {
          //                         return null;
          //                       },
          //                       onSave: (value) {
          //                         reason = value ?? "";
          //                       },
          //                     ),
          //                   ),
          //                   const SizedBox(height: 16),
          //                   Row(
          //                     children: [
          //                       Expanded(
          //                         child: CustomFilledButton(
          //                           onPressed: () async {
          //                             if (formKey.currentState!.validate()) {
          //                               formKey.currentState!.save();
          //                               await TadarsController.instance.reportTdbr(
          //                                 tdbr['id'],
          //                                 TadarsConstants
          //                                         .tdbrMappingForDb[tdbrType] ??
          //                                     "",
          //                                 reason,
          //                               );
          //                             }
          //                           },
          //                           text: "إبلاغ".tr,
          //                         ),
          //                       ),
          //                       const SizedBox(width: 16),
          //                       Expanded(
          //                         child: CustomOutlinedButton(
          //                           onPressed: () {
          //                             Get.back();
          //                           },
          //                           text: 'الغاء الأمر',
          //                         ),
          //                       ),
          //                     ],
          //                   ),
          //                 ],
          //               ),
          //             ),
          //           ),
          //           barrierDismissible: false,
          //           useSafeArea: false,
          //         );
          //         SystemChrome.setEnabledSystemUIMode(
          //           SystemUiMode.manual,
          //           overlays: [],
          //         );
          //       },
          //     ),
          //     TdbrAction(
          //       icon: Icons.share_outlined,
          //       label: tdbr['shares']?.toString() ?? "0",
          //       onTap: () async {
          //         Get.bottomSheet(
          //           SafeArea(
          //             child: CustomBottomSheet(
          //               title: "مشاركة",
          //               body: Column(
          //                 children: [
          //                   ListTile(
          //                     leading: const Icon(QuranUIIcons.share, size: 20),
          //                     trailing: const Icon(Icons.chevron_right_rounded),
          //                     title: Text('مشاركة كنص'.tr),
          //                     onTap: () {
          //                       Get.back();
          //                       shareAsText();
          //                     },
          //                   ),
          //                   const Divider(),
          //                   ListTile(
          //                     leading: const Icon(QuranUIIcons.image, size: 20),
          //                     trailing: const Icon(Icons.chevron_right_rounded),
          //                     title: Text('مشاركة كصوره'.tr),
          //                     onTap: () {
          //                       Get.back();
          //                       shareAsImage();
          //                     },
          //                   ),
          //                 ],
          //               ),
          //             ),
          //           ),
          //         );
          //       },
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }
}

//   Future<void> shareAsText() async {
//     var verses = await SqlHelper.getTdbrVerses(tdbr['id'], tdbrType);
//     var versesText = "";
//     for (var verse in verses) {
//       versesText =
//           BoxesHelper.getVerse(
//             verse['sora_num'],
//             verse['ayah_num'],
//           )?.verseWithDiac ??
//           ""
//               "\n";
//     }

//     String text = "";
//     if (tdbrType == TdbrType.media) {
//       text = tdbr['details'] + "\n" + tdbr['url'];
//     } else {
//       text = tdbr['fulltext'];
//     }
//     if (versesText.trim().isNotEmpty) {
//       text = "﴿$versesText﴾\n$text";
//     }
//     Share.share(
//       """$text

// https://tadars.com/tdbr/${TadarsConstants.tdbrMapping[tdbrType]}/${tdbr['id']}""",
//     );
//     CommonFunctions.shareItem(
//       tdbr['id'],
//       TadarsConstants.tdbrMapping[tdbrType] ?? "",
//     );
//   }

//   Future<void> shareAsImage() async {
//     var verses = await SqlHelper.getTdbrVerses(tdbr['id'], tdbrType);
//     var versesText = "";
//     print(verses);
//     for (var verse in verses) {
//       versesText =
//           BoxesHelper.getVerse(
//             verse['sora_num'],
//             verse['ayah_num'],
//           )?.verseWithDiac ??
//           ""
//               "\n";
//     }

//     String text = "";
//     if (tdbrType == TdbrType.media) {
//       text = tdbr['details'] + "\n" + tdbr['url'];
//     } else {
//       text = tdbr['fulltext'];
//     }
//     if (versesText.trim().isNotEmpty) {
//       text = "﴿$versesText﴾\n$text";
//     }

//     String shareText = """$text
// """;
//     print(shareText);
//     Get.back();
//     Get.dialog(
//       ChooseShareImageDialog(
//         shareText: shareText,
//         sourceText: tdbr['source_name'],
//       ),
//     );
//     CommonFunctions.shareItem(
//       tdbr['id'],
//       TadarsConstants.tdbrMapping[tdbrType] ?? "",
//     );
//   }
// }

// class ChooseShareImageDialog extends StatefulWidget {
//   const ChooseShareImageDialog({
//     super.key,
//     this.shareText,
//     this.sourceText,
//     this.verses,
//   });
//   final String? shareText;
//   final String? sourceText;
//   final List<QuranVerse>? verses;

//   @override
//   State<ChooseShareImageDialog> createState() => _ChooseShareImageDialogState();
// }

// class _ChooseShareImageDialogState extends State<ChooseShareImageDialog> {
// int currentIndex = 0;
// @override
// Widget build(BuildContext context) {
//   return Dialog(
//     backgroundColor: Colors.transparent,
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//     child: Container(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         children: [
//           Expanded(
//             child: Container(
//               padding: const EdgeInsets.all(16),
//               decoration: const BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.all(Radius.circular(16)),
//               ),
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(10),
//                 child: GridView.builder(
//                   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 2,
//                   ),
//                   itemCount: 32,
//                   itemBuilder: (context, index) {
//                     return InkWell(
//                       onTap: () {
//                         setState(() {
//                           currentIndex = index;
//                         });
//                       },
//                       child: Stack(
//                         children: [
//                           CachedNetworkImage(
//                             imageUrl:
//                                 "https://tadars.com/images/share_backgrounds/quran_${index + 1}.jpg",
//                             width: double.maxFinite,
//                             height: double.maxFinite,
//                             fit: BoxFit.cover,
//                           ),
//                           if (currentIndex == index)
//                             Container(
//                               width: double.maxFinite,
//                               height: double.maxFinite,
//                               color: Get.theme.primaryColor.withOpacity(0.7),
//                               child: const Icon(
//                                 QuranUIIcons.check,
//                                 color: Colors.white,
//                               ),
//                             ),
//                         ],
//                       ),
//                     );
//                   },
//                 ),
//               ),
//             ),
//           ),
//           const SizedBox(height: 16),
//           InkWell(
//             borderRadius: BorderRadius.circular(10),
//             onTap: () {
//               Get.back();
//               var url =
//                   "https://tadars.com/images/share_backgrounds/quran_${currentIndex + 1}.jpg";
//               showImageEditor(
//                 shareText: widget.shareText,
//                 sourceText: widget.sourceText,
//                 url: url,
//                 verses: widget.verses,
//               );
//             },
//             child: Container(
//               width: double.maxFinite,
//               padding: const EdgeInsets.all(8),
//               decoration: BoxDecoration(
//                 color: CustomColors.primaryColor,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               child: const Text(
//                 'اختيار',
//                 textAlign: TextAlign.center,
//                 style: TextStyle(color: Colors.white),
//               ),
//             ),
//           ),
//           const SizedBox(height: 16),
//           InkWell(
//             borderRadius: BorderRadius.circular(10),
//             onTap: () {
//               Get.back();
//               Get.bottomSheet(
//                 CustomBottomSheet(
//                   title: 'اختر الصورة',
//                   body: SafeArea(
//                     child: Column(
//                       children: [
//                         ListTile(
//                           leading: const Icon(QuranUIIcons.image, size: 20),
//                           trailing: const Icon(Icons.chevron_right_rounded),
//                           title: Text('الاستوديو'.tr),
//                           onTap: () async {
//                             Get.back();
//                             final ImagePicker _picker = ImagePicker();
//                             var image = await _picker.pickImage(
//                               source: ImageSource.gallery,
//                             );
//                             if (image != null) {
//                               showImageEditor(
//                                 shareText: widget.shareText,
//                                 sourceText: widget.sourceText,
//                                 file: File(image.path),
//                                 verses: widget.verses,
//                               );
//                             }
//                           },
//                         ),
//                         ListTile(
//                           leading: const Icon(
//                             Icons.camera_alt_outlined,
//                             size: 25,
//                           ),
//                           trailing: const Icon(Icons.chevron_right_rounded),
//                           title: Text('الكاميرا'.tr),
//                           onTap: () async {
//                             Get.back();
//                             final ImagePicker _picker = ImagePicker();
//                             var image = await _picker.pickImage(
//                               source: ImageSource.camera,
//                               imageQuality: 80,
//                             );
//                             if (image != null) {
//                               showImageEditor(
//                                 shareText: widget.shareText,
//                                 sourceText: widget.sourceText,
//                                 file: File(image.path),
//                                 verses: widget.verses,
//                               );
//                             }
//                           },
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               );
//             },
//             child: Container(
//               width: double.maxFinite,
//               padding: const EdgeInsets.all(8),
//               decoration: BoxDecoration(
//                 color: CustomColors.primaryColor,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               child: const Text(
//                 'اختر من الاستوديو / الكاميرا',
//                 textAlign: TextAlign.center,
//                 style: TextStyle(color: Colors.white),
//               ),
//             ),
//           ),
//         ],
//       ),
//     ),
//   );
// }

//   void showImageEditor({
//     String? shareText,
//     String? sourceText,
//     String? url,
//     File? file,
//     List<QuranVerse>? verses,
//   }) {
//     Get.dialog(
//       ImageEditorDialog(
//         file: file,
//         url: url,
//         shareText: shareText,
//         sourceText: sourceText,
//         verses: verses,
//       ),
//       barrierDismissible: false,
//     );
//   }
// }

// class ImageEditorDialog extends StatefulWidget {
//   const ImageEditorDialog({
//     super.key,
//     this.file,
//     this.url,
//     this.shareText,
//     this.sourceText,
//     this.verses,
//   });
//   final File? file;
//   final String? url;
//   final String? shareText;
//   final String? sourceText;
//   final List<QuranVerse>? verses;

//   @override
//   State<ImageEditorDialog> createState() => _ImageEditorDialogState();
// }

// class _ImageEditorDialogState extends State<ImageEditorDialog> {
//   double fontSize = 13;
//   ScreenshotController controller = ScreenshotController();
//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       backgroundColor: Colors.transparent,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Container(
//             margin: const EdgeInsets.only(bottom: 8),
//             padding: const EdgeInsets.only(top: 8),
//             decoration: BoxDecoration(
//               color: Get.theme.scaffoldBackgroundColor,
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: Column(
//               children: [
//                 const Text('حجم الخط'),
//                 Slider(
//                   value: fontSize,
//                   min: 2,
//                   max: 27,
//                   onChanged: (value) {
//                     setState(() {
//                       fontSize = value;
//                     });
//                   },
//                 ),
//               ],
//             ),
//           ),
//           Expanded(
//             child: Center(
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(8),
//                 child: SingleChildScrollView(
//                   child: SizedBox(
//                     width: double.maxFinite,
//                     child: Screenshot(
//                       controller: controller,
//                       child: Stack(
//                         fit: StackFit.passthrough,
//                         children: [
//                           if (widget.file != null)
//                             Image.file(widget.file!, width: double.maxFinite)
//                           else
//                             CachedNetworkImage(imageUrl: widget.url!),
//                           Positioned(
//                             top: 15,
//                             left: 15,
//                             child: Image.asset(
//                               'assets/images/share_watermark.png',
//                               width: 45,
//                             ),
//                           ),
//                           Positioned(
//                             bottom: 0,
//                             right: 0,
//                             left: 0,
//                             child: Container(
//                               margin: const EdgeInsets.all(16),
//                               padding: const EdgeInsets.all(12),
//                               // alignment: Alignment.bottomCenter,
//                               decoration: BoxDecoration(
//                                 color: Colors.white.withOpacity(0.8),
//                                 borderRadius: BorderRadius.circular(8),
//                               ),
//                               child:
//                                   widget.verses != null
//                                       ? Column(
//                                         children: [
//                                           VersesTextWidget(
//                                             verses: widget.verses!,
//                                             fontSize: fontSize,
//                                           ),
//                                           SizedBox(
//                                             width: double.maxFinite,
//                                             child: Text(
//                                               widget.sourceText ?? "",
//                                               textAlign: TextAlign.left,
//                                               style: TextStyle(
//                                                 color: Colors.black,
//                                                 fontSize: fontSize,
//                                                 fontFamily:
//                                                     CommonConstants
//                                                         .adwaaAlsalafFontFamily,
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       )
//                                       : Column(
//                                         children: [
//                                           Text(
//                                             widget.shareText ?? "",
//                                             style: TextStyle(
//                                               color: Colors.black,
//                                               fontSize: fontSize,
//                                               fontFamily:
//                                                   CommonConstants
//                                                       .adwaaAlsalafFontFamily,
//                                             ),
//                                           ),
//                                           SizedBox(
//                                             width: double.maxFinite,
//                                             child: Text(
//                                               widget.sourceText ?? "",
//                                               textAlign: TextAlign.left,
//                                               style: TextStyle(
//                                                 color: Colors.black,
//                                                 fontSize: fontSize,
//                                                 fontFamily:
//                                                     CommonConstants
//                                                         .adwaaAlsalafFontFamily,
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//           CustomFilledButton(
//             color: CustomColors.primaryColor,
//             fullWidth: true,
//             margin: const EdgeInsets.only(top: 8),
//             fontSize: 16,
//             text: 'مشاركة',
//             onPressed: () async {
//               Get.back();
//               var image = await controller.capture();
//               if (image != null) {
//                 await Share.shareXFiles([
//                   XFile.fromData(image, mimeType: 'image/png'),
//                 ]);
//               }
//             },
//           ),
//           CustomFilledButton(
//             fullWidth: true,
//             margin: const EdgeInsets.only(top: 8),
//             fontSize: 16,
//             color: Colors.red,
//             text: 'الغاء الامر',
//             onPressed: () {
//               Get.back();
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
