import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:screen_state/screen_state.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';
import 'package:tadars/features/intro_verse/widgets/play_button.dart';
import 'package:tadars/helpers/shared_prefrence_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/hive/tdbr_tadabor.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';

import 'icon_button.dart';

class IntroVersewidget extends StatefulWidget {
  const IntroVersewidget({super.key});

  @override
  State<IntroVersewidget> createState() => _IntroVersewidgetState();
}

class _IntroVersewidgetState extends State<IntroVersewidget> {
  final Screen _screen = Screen();
  StreamSubscription<ScreenStateEvent>? _subscription;
  final int mushafId = 2;

  bool prev = false;

  Future<bool> showVeserWhenUnlock() async {
    // Obtain shared preferences.
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('show_verse_when_unlock') ?? false;
  }

  @override
  void initState() {
    super.initState();

    if (Platform.isAndroid) {
      _initScreenStateListener();

      showVeserWhenUnlock().then((value) async {
        if (value) {
          if (!await FlutterOverlayWindow.isActive()) {
            IntroVerseFunction.showOverlay();
          }
        }
      });
    }
  }

  Future<int> getLastVerseId() async {
    return await SharedPrefrenceHelper.getLastVerseId();
  }

  Future<void> setLastVerseId(int? id) async {
    await SharedPrefrenceHelper.setLastVerseId(id);

    setState(() {});
  }

  void _initScreenStateListener() {
    _subscription = _screen.screenStateStream.listen((event) {
      if (event == ScreenStateEvent.SCREEN_OFF) {
        FlutterOverlayWindow.resizeOverlay(0, 0, false);
      } else if (event == ScreenStateEvent.SCREEN_ON ||
          event == ScreenStateEvent.SCREEN_UNLOCKED) {
        FlutterOverlayWindow.resizeOverlay(WindowSize.matchParent, 400, false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 30),
        child: Material(
          color: Colors.white,
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          child: FutureBuilder(
            future: Future.microtask(() async {
              var lastVerseId = await getLastVerseId();

              if (prev) {
                prev = false;
                return QuranDatabaseProvider.getPrevVerseById(lastVerseId);
              }
              return QuranDatabaseProvider.getNextVerseById(lastVerseId);
            }),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              }
              Verse? verse = snapshot.data;

              if (verse == null) {
                return SizedBox.shrink();
              }

              return Column(
                children: [
                  _buildHeader(verse),

                  Expanded(
                    child: Center(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [_buildVerse(verse), _buildTadbor(verse)],
                        ),
                      ),
                    ),
                  ),

                  _buildBottom(verse),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Padding _buildVerse(Verse verse) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: " ﴿ ",
              style: TextStyle(
                // color: Colors.white,
                fontSize: 16,
                fontFamily: CommonConstants.hafsFontFamily,
              ),
            ),
            TextSpan(
              text: verse.text,
              style: TextStyle(
                // color: Colors.white,
                fontSize: 20,
                fontFamily: CommonConstants.hafsFontFamily,
                // fontFamily:
                //     "mushaf_${HomeController.to.quranPageController.mushafId}_page_${verse.pageNumber}",
              ),
            ),

            TextSpan(
              text: " ﴾ ",
              style: TextStyle(
                // color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
      ),
    );
  }

  Builder _buildTadbor(Verse verse) {
    return Builder(
      builder: (context) {
        return FutureBuilder<TdbrTadabor?>(
          future: SqlHelper.getMostSharedTadabor(
            verse.surahNumber,
            verse.number,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Container(
                padding: const EdgeInsets.all(16),
                width: double.maxFinite,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  borderRadius: CommonStyles.borderRadius,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.white,
                      highlightColor: Colors.white54,
                      direction: ShimmerDirection.rtl,
                      // enabled: false,

                      // period: const Duration(seconds: 3),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        height: 20,
                        width: double.maxFinite,
                      ),
                    ),
                  ],
                ),
              );
            }

            TdbrTadabor? tdbr = snapshot.data;
            if (tdbr == null) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 32),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  borderRadius: CommonStyles.borderRadius,
                ),
                child: Center(
                  child: Text(
                    "لاتوجد مادة في هذا القسم".tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              );
            }

            return Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              width: double.maxFinite,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                borderRadius: CommonStyles.borderRadius,
              ),
              child: Builder(
                builder: (context) {
                  var markIndex = tdbr.fullText.toString().indexOf(",");

                  if (markIndex == -1) {
                    return Text(
                      tdbr.fullText.toString(),
                      textAlign: TextAlign.center,

                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        fontFamily: CommonConstants.adwaaAlsalafFontFamily,
                      ),
                    );
                  }
                  return Text.rich(
                    TextSpan(
                      text: tdbr.fullText.toString().substring(
                        0,
                        markIndex - 1,
                      ),
                      children: [
                        const TextSpan(
                          text: ",",
                          style: TextStyle(
                            fontFamily: CommonConstants.amiriFontFamily,
                          ),
                        ),
                        TextSpan(
                          text: tdbr.fullText.toString().substring(
                            markIndex + 1,
                          ),
                        ),
                      ],

                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        fontFamily: CommonConstants.adwaaAlsalafFontFamily,
                      ),
                    ),

                    textAlign: TextAlign.center,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Container _buildHeader(Verse verse) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
        color: CustomColors.primaryColor.withValues(alpha: 0.3),
      ),
      padding: const EdgeInsets.only(top: 16.0, right: 16, left: 16, bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          PlayButton(verse: verse),
          Text(
            "تدارس القران",
            style: TextStyle(
              color: CustomColors.primaryColor,
              fontSize: 19,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconBtn(
            icon: QuranUIIcons.check,
            size: 15,
            color: CustomColors.primaryColor,

            onTap: () {
              if (Platform.isAndroid) {
                FlutterOverlayWindow.resizeOverlay(0, 0, false);
              } else {
                Get.back();
              }
              //set last verse id
              setLastVerseId(verse.id);
              IntroVerseFunction.initVerseNotfication();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottom(Verse verse) {
    return Container(
      // padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(10)),
        color: CustomColors.primaryColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconBtn(
            color: Colors.white,
            onTap: () {
              if (Platform.isAndroid) {
                FlutterOverlayWindow.resizeOverlay(0, 0, false);
              } else {
                Get.back();
              }
            },
            icon: Icons.watch_later,
          ),

          IconBtn(
            color: Colors.white,
            onTap: () {
              if (verse.id == 1) return;
              setLastVerseId(verse.id).then((_) {
                setState(() {
                  prev = true;
                });
              });
            },
            icon: Icons.arrow_back,
          ),

          IconBtn(
            color: Colors.white,
            onTap: () {
              setLastVerseId(verse.id).then((_) {
                setState(() {});
              });
            },
            icon: Icons.arrow_forward,
          ),

          IconBtn(
            color: Colors.white,
            onTap: () {
              FlutterOverlayWindow.resizeOverlay(0, 0, false);

              var text = "{${verse.searchableText}}";

              Share.share(text);
            },
            icon: Icons.share,
          ),
        ],
      ),
    );
  }
}
