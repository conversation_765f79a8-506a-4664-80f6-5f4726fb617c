import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';
import 'package:numberpicker/numberpicker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';
import 'package:tadars/helpers/shared_prefrence_helper.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';
import 'package:tadars/view/components/form_fields/searchable_dropdown_field.dart';

class IosVerseNotficationBottomSheet extends StatefulWidget {
  const IosVerseNotficationBottomSheet({super.key});

  @override
  State<IosVerseNotficationBottomSheet> createState() =>
      _IosVerseNotficationBottomSheetState();
}

class _IosVerseNotficationBottomSheetState
    extends State<IosVerseNotficationBottomSheet> {
  var notficationPeriod = 2;

  @override
  void initState() {
    SharedPrefrenceHelper.getVerseNotficationDuration().then((value) {
      int index = IntroVerseFunction.verseDuration.keys.toList().indexOf(value);
      setState(() {
        notficationPeriod = index;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: "تخصيص التنبيه".tr,
      body: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  "التبيه كل".tr,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(width: 70),
                NumberPicker(
                  itemHeight: 31,
                  // itemWidth: 60,
                  minValue: 0,
                  maxValue: IntroVerseFunction.verseDuration.length - 1,
                  textMapper: (numberText) {
                    int index = int.parse(numberText);

                    int key =
                        IntroVerseFunction.verseDuration.keys.toList()[index];

                    return IntroVerseFunction.verseDuration[key] ?? "";
                  },

                  // step: 2,
                  itemCount: 2,
                  value: notficationPeriod,
                  onChanged: (number) {
                    setState(() {
                      notficationPeriod = number;
                    });
                  },

                  // haptics: true,
                  decoration: BoxDecoration(
                    // borderRadius: CommonStyles.borderRadius,
                    // border: Border.all(width: 1, color: Colors.grey.shade100),
                  ),
                  selectedTextStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),

            Divider(),
            Text(
              "يجب عليك عرض (فتح) التنبيه الخاص بالايات ليتم احتساب قراءة الاية والانتقال للاية التالية "
                  .tr,
              textAlign: TextAlign.justify,
              style: TextStyle(fontSize: 16, color: CustomColors.primaryColor),
            ),

            SizedBox(height: 20),
            CustomFilledButton(
              text: "حفظ".tr,
              fullWidth: true,
              onPressed: () async {
                int key =
                    IntroVerseFunction.verseDuration.keys
                        .toList()[notficationPeriod];
                notficationPeriod = key;

                await SharedPrefrenceHelper.setVerseNotficationDuration(
                  notficationPeriod,
                );
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }
}
