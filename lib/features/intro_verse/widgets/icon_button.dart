import 'package:flutter/material.dart';

class IconBtn extends StatelessWidget {
  const IconBtn( {super.key, this.onTap, this.icon, this.size, this.color});
  final void Function()? onTap;
  final IconData? icon;
  final double? size;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      customBorder: CircleBorder(),
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(icon, size: size, color: color),
      ),
    );
  }
}
