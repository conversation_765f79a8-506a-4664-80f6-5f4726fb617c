import 'package:flutter/material.dart';
import 'package:quran_core/quran_core.dart';
import 'package:audio_service/audio_service.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/audio_player/player_action.dart';

class PlayButton extends StatelessWidget {
  const PlayButton({super.key, required this.verse});

  final Verse verse;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: QuranAudioService.processingState,

      builder: (context, state, child) {
        bool isLoading = state == AudioProcessingState.loading;

        if (isLoading) {
          return Container(
            padding: const EdgeInsets.all(4),
            child: const Center(
              child: SizedBox(
                height: 16,
                width: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        } else {
          return StreamBuilder(
            stream: QuranAudioService.playing,
            builder: (context, snapshot) {
              if (snapshot.data != true) {
                return PlayerAction(
                  icon: QuranUIIcons.play,
                  onTap:
                      isLoading
                          ? null
                          : () {
                            QuranAudioService.playVerse(
                              verse.surahNumber,
                              verse.number,
                            );
                          },
                );
              } else {
                return PlayerAction(
                  icon: QuranUIIcons.pause,
                  onTap:
                      isLoading
                          ? null
                          : () {
                            QuranAudioService.pause();
                          },
                );
              }
            },
          );
        }

      },
    );
    
  }
}
