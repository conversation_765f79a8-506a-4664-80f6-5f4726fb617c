import 'dart:io';

String fileName = "tdbr_ayat.dart";

Map<String, dynamic> api = {
  "id": 90440,
  "ayah_num": 19,
  "sora_num": 22,
  "ayah_id": 2614,
  "tdbr_type": "tdbr_eloquence",
  "tdbr_id": 4610,
  "status": 0,
  "created_at": "2018-07-23 02:34:42",
  "updated_at": "2018-07-23 02:34:42"
};

String path = "models/$fileName";

void main(List<String> args) {
  var file = fileName.split(".")[0];
  var indexOfUnderScor = file.indexOf("_");
  var fileNameWithDeletedUS = file.replaceAll("_", "");
//calss name
  var className0 = fileNameWithDeletedUS.replaceRange(
    indexOfUnderScor,
    indexOfUnderScor,
    fileNameWithDeletedUS[indexOfUnderScor].toUpperCase(),
  );

  var className = className0.replaceRange(0, 0, className0[0].toUpperCase());
  String constr = constructor();
  String att = instanceVar();
  String fromjson = fromJson(className);
  String tojson = toJson(className);
  File(path).writeAsString("""
 class $className {
$className({$constr});

 $att
 $fromjson
$tojson
}
""");
}

String returnDataType(inpu) {
  return int.tryParse(inpu.toString()) == null ? "String?" : "int?";
}

String convertKeyToVariable(String key) {
  var indexOfUnderScor = key.indexOf("_");
  var fileNameWithDeletedUS =
      indexOfUnderScor == -1 ? key : key.replaceAll("_", "");
  var className = indexOfUnderScor == -1
      ? fileNameWithDeletedUS
      : fileNameWithDeletedUS.replaceAll(
          fileNameWithDeletedUS[indexOfUnderScor],
          fileNameWithDeletedUS[indexOfUnderScor].toUpperCase());
  return className;
}

String constructor() {
  //this.${convertKeyToVariable(element)},
  String cons =
      "${api.keys.map<String>(<String>(e) => "this.${convertKeyToVariable(e)}")}"
          .replaceAll("(", '')
          .replaceAll(")", '')
          .replaceAll(",...", ",")
          .replaceAll("...", "");

  return cons;
}

String instanceVar() {
  //this.${convertKeyToVariable(element)},
  String att =
      "${api.keys.map<String>((element) => "${returnDataType(api[element])} ${convertKeyToVariable(element)};\n")}"
          .replaceAll("(", '')
          .replaceAll(")", '')
          .replaceAll(",", '')
          .replaceAll(",...", ",")
          .replaceAll("...", "");

  return att;
}

String fromJson(classname) {
  String fromJ =
      "${api.keys.map((element) => "${convertKeyToVariable(element)}:json['$element']")}"
          .replaceAll("(", "")
          .replaceAll(")", '')
          .replaceAll(",...", "")
          .replaceAll("...", "");
  return """
factory $classname.fromJson(Map<String, dynamic> json)=>$classname( 
  $fromJ);
""";
}

String toJson(classname) {
  String toj = "${api.keys.map((element) => """
      "$element":${convertKeyToVariable(element)}\n""")}"
      .replaceAll("(", "")
      .replaceAll(")", '')
      .replaceAll(",...", "")
      .replaceAll("...", "");
  return """
Map<String,dynamic> toJson()=>{
$toj};
""";
}
