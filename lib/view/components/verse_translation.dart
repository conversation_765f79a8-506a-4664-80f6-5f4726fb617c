
// import 'package:flutter/material.dart';

// class VerseTdbrView extends StatelessWidget {
//   final int verseNumber;
//   final int suraNumber;
//   final TdbrType tdbrType;
//   final bool showTdbrTitle;
//   const VerseTdbrView(
//       {super.key,
//       required this.verseNumber,
//       required this.suraNumber,
//       required this.tdbrType,
//       this.showTdbrTitle = false});

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<List<Map<String, dynamic>>>(
//       future: SqlHelper.getVerseTdbrs(suraNumber, verseNumber, tdbrType),
//       builder: (BuildContext context,
//           AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return Container(
//               padding: const EdgeInsets.all(16),
//               width: double.maxFinite,
//               margin: const EdgeInsets.symmetric(horizontal: 8),
//               decoration: BoxDecoration(
//                 color: HomeController.to.pageColor.value == Colors.white
//                     ? Get.theme.primaryColor.withOpacity(0.2)
//                     : HomeController.to.pageColor.value?.withOpacity(0.2),
//                 borderRadius: CommonStyles.borderRadius,
//               ),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Shimmer.fromColors(
//                     baseColor: Colors.white,
//                     highlightColor: Colors.white54,
//                     direction: ShimmerDirection.rtl,
//                     // enabled: false,

//                     // period: const Duration(seconds: 3),
//                     child: Container(
//                       decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(20)),
//                       height: 20,
//                       width: double.maxFinite,
//                     ),
//                   ),
//                 ],
//               ));
//         }

//         var tdbrs = snapshot.data ?? [];
//         if (tdbrs.isEmpty) {
//           return Container(
//             padding: const EdgeInsets.symmetric(vertical: 32),
//             decoration: BoxDecoration(
//               color: Get.isDarkMode
//                   ? Get.theme.primaryColor.withOpacity(0.2)
//                   : HomeController.to.pageColor.value == Colors.white
//                       ? Get.theme.primaryColor.withOpacity(0.2)
//                       : HomeController.to.pageColor.value,
//               borderRadius: CommonStyles.borderRadius,
//             ),
//             child: Center(
//               child: Text(
//                 "لاتوجد مادة في هذا القسم".tr,
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                   color: Get.isDarkMode
//                       ? Colors.white
//                       : HomeController.to.pageColor.value == Colors.white
//                           ? Get.theme.primaryColor
//                           : HomeController.to.pageColor.value,
//                 ),
//               ),
//             ),
//           );
//         }
//         return Column(
//           children: tdbrs.map(
//             (e) {
//               print("item ${e['id']}");
//               return TdbrView(
//                 tdbr: e,
//                 tdbrType: tdbrType,
//                 showTdbTitle: showTdbrTitle,
//               );
//             },
//           ).toList(),
//         );
//       },
//     );
//   }
// }
