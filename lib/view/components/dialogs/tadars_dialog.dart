import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TadarsDialog extends StatelessWidget {
  final String title;
  final Widget content;
  const TadarsDialog({
    super.key,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: Dialog(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          child: <PERSON><PERSON>(
            alignment: Alignment.center,
            child: Material(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(
                      8,
                    ),
                    decoration: BoxDecoration(
                      color: Get.theme.primaryColor.withOpacity(0.3),
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(10),
                      ),
                    ),
                    width: double.maxFinite,
                    height: 50,
                    child: NavigationToolbar(
                      middle: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Get.theme.primaryColor,
                        ),
                      ),
                      trailing: Icon<PERSON>utton(
                        icon: Icon(
                          Icons.close,
                          size: 20,
                          color: Get.theme.primaryColor,
                        ),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ),
                  ),
                  Padding(
                      padding: const EdgeInsets.only(
                        right: 16,
                        left: 16,
                        bottom: 8,
                        top: 8,
                      ),
                      child: content),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
