import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/waqafat/tdbr_listview.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../helpers/sql_helper.dart';
import '../../../utils/constants/tadars_constants.dart';
import '../waqafat/tdbr_view.dart';

class WaqafatDialog extends StatefulWidget {
  const WaqafatDialog({Key? key}) : super(key: key);

  @override
  State<WaqafatDialog> createState() => _WaqafatDialogState();
}

class _WaqafatDialogState extends State<WaqafatDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "جميع الوقفات".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              FutureBuilder<Map<TdbrType, int>>(
                  future: SqlHelper.getAllTadbrsCounts(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox(
                        height: 300,
                        child: Center(child: CircularProgressIndicator()),
                      );
                    }
                    Map<TdbrType, int> tdbrCounts = snapshot.data ?? {};
                    return DefaultTabController(
                      length: TadarsConstants.getEnabledTdbrTypes().length,
                      child: Container(
                        constraints: BoxConstraints(
                          maxHeight: Get.height * 0.7,
                          minHeight: Get.height * 0.2,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                right: 8.0,
                                left: 8,
                                bottom: 8,
                              ),
                              child: TabBar(
                                isScrollable: true,
                                onTap: (index) {},
                                tabs: TadarsConstants.getEnabledTdbrTypes()
                                    .map((e) {
                                  return Tab(
                                      text:
                                          TadarsConstants.tdbrKeyTranlation[e],
                                    iconMargin:
                                        const EdgeInsetsDirectional.only(
                                            bottom: 0, start: 20),
                                    icon: tdbrCounts[e] == null
                                        ? null
                                        : Container(
                                            alignment:
                                                AlignmentDirectional.bottomEnd,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                                color: Get.theme.colorScheme
                                                    .secondary,
                                                borderRadius:
                                                    BorderRadius.circular(16)),
                                            child: Text(
                                              tdbrCounts[e].toString(),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                                height: 1,
                                              ),
                                            ),
                                          ),
                                  );
                                }).toList(),
                              ),
                            ),
                            Expanded(
                              child: Stack(
                                children: [
                                  TabBarView(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    children:
                                        TadarsConstants.getEnabledTdbrTypes()
                                            .map((
                                      tdbrType,
                                    ) {
                                      return TdbrListView(tdbrType: tdbrType);
                                    }).toList(),
                                  ),
                                  Positioned.directional(
                                    textDirection: TextDirection.rtl,
                                    bottom: 70,
                                    end: 20,
                                    child: FloatingActionButton(
                                      onPressed: () {
                                        launchUrl(Uri.parse(
                                            'https://tadars.com/?export=doc'));
                                      },
                                      tooltip: 'تصدير الوقفات'.tr,
                                      child:
                                          const Icon(Icons.ios_share_rounded),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
              ),
            ],
          ),
        ),
      ),
    );
  }
}


