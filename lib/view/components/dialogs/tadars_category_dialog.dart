import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/view/components/dialogs/tadars_dialog.dart';

import '../../../utils/constants/boxes.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../../utils/constants/settings.dart';
import '../../../utils/constants/tadars_constants.dart';

class TadarsCategoryDialog extends StatelessWidget {
  const TadarsCategoryDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return TadarsDialog(
      title: 'تصنيفات التدارس'.tr,
      content: ListView.builder(
        shrinkWrap: true,
        itemCount: TadarsConstants.tdbrTypes.length,
        itemBuilder: (BuildContext context, int index) {
          var tdbr = TadarsConstants.tdbrTypes[index];
          return Column(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(tdbr.name.tr),
                    ),
                    ValueListenableBuilder(
                      builder:
                          (BuildContext context, Box value, Widget? child) {
                        return FlutterSwitch(
                          padding: 2,
                          activeColor: CustomColors.accentColor,
                          //toggleColor: Get.theme.primaryColor,
                          height: 20,
                          width: 40,
                          onToggle: (bool value) {
                            Hive.box(Boxes.settings).put(
                                SettingsConstants.tdbrEnabledKeyList[tdbr],
                                !Hive.box(Boxes.settings).get(
                                    SettingsConstants.tdbrEnabledKeyList[tdbr],
                                    defaultValue: true));
                          },
                          value: value.get(SettingsConstants.tdbrEnabledKeyList[tdbr],
                              defaultValue: true),
                        );
                      },
                      valueListenable: Hive.box(Boxes.settings).listenable(
                          keys: [SettingsConstants.tdbrEnabledKeyList[tdbr]]),
                    ),
                  ],
                ),
              ),
              if (index != TadarsConstants.tdbrTypes.length - 1)
                const Divider(
                  height: 1,
                ),
            ],
          );
        },
      ),
    );
  }
}
