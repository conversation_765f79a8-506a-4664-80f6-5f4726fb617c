import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/models/hive/tafseer.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/view/pages/home/<USER>/page_views/tafseer_page_view.dart';

import '../../../controllers/home_controller.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../models/hive/quran_tafseer.dart';
import '../../../utils/constants/common_constants.dart';
import '../../pages/home/<USER>/verse_marker.dart';

class ShowTafseerDialog extends StatefulWidget {
  final Tafseer tafseer;
  final QuranTafseer? tafseerBook;
  const ShowTafseerDialog(
      {Key? key, required this.tafseer, required this.tafseerBook})
      : super(key: key);

  @override
  State<ShowTafseerDialog> createState() => _ShowTafseerDialogState();
}

class _ShowTafseerDialogState extends State<ShowTafseerDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "عرض التفسير".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  minHeight: Get.height * 0.2,
                  maxHeight: Get.height * 0.7,
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Column(
                        children: [
                          InkWell(
                            onTap: () {
                              Get.offNamedUntil(
                                  Routes.homePage, (route) => route.isFirst);
                              HomeController.to.goToVerse(
                                widget.tafseer.suraNumber,
                                widget.tafseer.verseNumber,
                              );
                            },
                            child: Text.rich(
                              TextSpan(
                                text: BoxesHelper.getVerse(
                                            widget.tafseer.suraNumber,
                                            widget.tafseer.verseNumber)
                                        ?.verseWithDiac ??
                                    "",
                                children: [
                                  WidgetSpan(
                                      child: Text(
                                          ' [${BoxesHelper.getSuraById(widget.tafseer.suraNumber)?.nameWithDiac ?? ""} - ${widget.tafseer.verseNumber}]')),
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: VerseMarker(
                                      size:
                                          HomeController.to.pageFontSize.value *
                                              1.2,
                                      verseNumber: widget.tafseer.verseNumber,
                                      suraNumber: widget.tafseer.suraNumber,
                                    ),
                                  ),
                                ],
                              ),
                              style: TextStyle(
                                fontSize: HomeController.to.pageFontSize.value,
                                fontFamily: CommonConstants.hafsFontFamily,
                              ),
                              textDirection: TextDirection.rtl,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const Divider(),
                          TafseerHtmlView(
                            htmlText: widget.tafseer.text,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
