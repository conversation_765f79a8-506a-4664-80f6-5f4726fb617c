import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/dialogs/source_waqafat_dialog.dart';

import '../../../helpers/boxes_helper.dart';
import '../../../helpers/sql_helper.dart';

class SourcesBookmarkDialog extends StatefulWidget {
  const SourcesBookmarkDialog({Key? key}) : super(key: key);

  @override
  State<SourcesBookmarkDialog> createState() => _SourcesBookmarkDialogState();
}

class _SourcesBookmarkDialogState extends State<SourcesBookmarkDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "المصادر المفضلة".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxHeight: Get.height * 0.7,
                  minHeight: Get.height * 0.2,
                ),
                child: ValueListenableBuilder<Box>(
                    valueListenable: BoxesHelper.getListenableSourceBookmarks(),
                    builder: (context, box, child) {
                      return FutureBuilder<List<Map<String, dynamic>>>(
                        future: SqlHelper.getBookmarkedSources(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const SizedBox(
                              height: 300,
                              child: Center(child: CircularProgressIndicator()),
                            );
                          }
                          var sources = snapshot.data;

                          if (sources?.isEmpty ?? true) {
                            return const SizedBox(
                              height: 300,
                              child: Center(
                                  child: Text(
                                      'عذراً .. لم يتم إضافة مصادر للمفضلة')),
                            );
                          }
                          return ListView.builder(
                            itemCount: sources?.length ?? 0,
                            itemBuilder: (context, index) {
                              return Column(
                                children: [
                                  Slidable(
                                    endActionPane: ActionPane(
                                      motion: const ScrollMotion(),
                                      extentRatio: 0.30,
                                      children: <Widget>[
                                        SlidableAction(
                                          label: 'الغاء التفضيل'.tr,
                                          backgroundColor: Colors.redAccent,
                                          icon: Icons.star,
                                          onPressed: (context) {
                                            BoxesHelper
                                                .removeSourceFromBookmark(
                                                    sources?[index]['id'] ??
                                                        "");
                                          },
                                        ),
                                      ],
                                    ),
                                    child: ListTile(
                                      onTap: () {
                                        Get.dialog(
                                          SourceWaqafatDialog(
                                            sourceId:
                                                sources?[index]['id'] ?? "",
                                            sourceName:
                                                sources?[index]['name'] ?? "",
                                            sourceDetail:
                                                sources?[index]['detail'] ?? "",
                                          ),
                                        );
                                      },
                                      title: Text(
                                        sources?[index]['name'] ?? "",
                                      ),
                                    ),
                                  ),
                                  const Divider(
                                    height: 1,
                                  )
                                ],
                              );
                            },
                          );
                        },
                      );
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
