import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/view/components/buttons/custom_outlined_button.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';

import '../../../controllers/home_controller.dart';
import '../../../enums/tdbr_type.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/common_constants.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../../utils/constants/tadars_constants.dart';
import '../../pages/home/<USER>/verse_marker.dart';
import '../waqafat/tdbr_view.dart';

class TafseerDialogAction extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final IconData icon;
  const TafseerDialogAction({
    super.key,
    required this.onTap,
    required this.text,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }
}

class VerseWaqafatDialog extends StatefulWidget {
  final int suraNumber;
  final int verseNumber;
  const VerseWaqafatDialog(
      {Key? key, required this.suraNumber, required this.verseNumber})
      : super(key: key);

  @override
  State<VerseWaqafatDialog> createState() => _VerseWaqafatDialogState();
}

class _VerseWaqafatDialogState extends State<VerseWaqafatDialog> {
  int currentSura = 0;
  int currentVerse = 0;

  int currentTabIndex = 0;
  @override
  void initState() {
    currentSura = widget.suraNumber;
    currentVerse = widget.verseNumber;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var verse = BoxesHelper.getVerse(currentSura, currentVerse);
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: Get.theme.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "وقفات الآية".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              DefaultTabController(
                length: TadarsConstants.getEnabledTdbrTypes().length + 1,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.7,
                    minHeight: Get.height * 0.2,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          right: 8.0,
                          left: 8,
                          bottom: 8,
                        ),
                        child: TabBar(
                          isScrollable: true,
                          onTap: (index) {
                            setState(() {
                              currentTabIndex = index;
                            });
                          },
                          tabs: [
                            Tab(text: "الكل".tr,
                            ),
                            ...TadarsConstants.getEnabledTdbrTypes().map((e) {
                              return Tab(
                                text: TadarsConstants.tdbrKeyTranlation[e],
                              );
                            })
                          ],
                        ),
                      ),
                      Expanded(
                        child: TabBarView(
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            Scrollbar(
                              child: ListView(
                                  padding: const EdgeInsets.all(8),
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Text.rich(
                                        TextSpan(
                                          text: "${verse?.verseWithDiac} ",
                                          children: [
                                            WidgetSpan(
                                              alignment:
                                                  PlaceholderAlignment.middle,
                                              child: VerseMarker(
                                                size: HomeController
                                                        .to.pageFontSize.value *
                                                    1.2,
                                                verseNumber: currentVerse,
                                                suraNumber: currentSura,
                                              ),
                                            ),
                                          ],
                                        ),
                                        style: TextStyle(
                                          fontSize: HomeController
                                              .to.pageFontSize.value,
                                          fontFamily:
                                              CommonConstants.hafsFontFamily,
                                        ),
                                        textDirection: TextDirection.rtl,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    ...TadarsConstants.getEnabledTdbrTypes()
                                        .map((
                                      tdbrType,
                                    ) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8.0),
                                        child: VerseTdbrView(
                                          verseNumber: currentVerse,
                                          suraNumber: currentSura,
                                          tdbrType: tdbrType,
                                          showTdbrTitle: true,
                                        ),
                                      );
                                    })
                                  ]),
                            ),
                            ...TadarsConstants.getEnabledTdbrTypes().map((
                              tdbrType,
                            ) {
                              return ListView(
                                padding: const EdgeInsets.all(8),
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(8),
                                    child: Text.rich(
                                      TextSpan(
                                        text: "${verse?.verseWithDiac} ",
                                        children: [
                                          WidgetSpan(
                                            alignment:
                                                PlaceholderAlignment.middle,
                                            child: VerseMarker(
                                              size: HomeController
                                                      .to.pageFontSize.value *
                                                  1.2,
                                              verseNumber: currentVerse,
                                              suraNumber: currentSura,
                                            ),
                                          ),
                                        ],
                                      ),
                                      style: TextStyle(
                                        fontSize: HomeController
                                            .to.pageFontSize.value,
                                        fontFamily:
                                            CommonConstants.hafsFontFamily,
                                      ),
                                      textDirection: TextDirection.rtl,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  VerseTdbrView(
                                    verseNumber: currentVerse,
                                    suraNumber: currentSura,
                                    tdbrType: tdbrType,
                                  ),
                                ],
                              );
                            })
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(
                  2,
                ),
                decoration: BoxDecoration(
                  color: Get.theme.primaryColor,
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: Row(
                  children: [
                    // close

                    const Spacer(),
                    // previos verse
                    TafseerDialogAction(
                      onTap: () {
                        print('previos verse');

                        var prevVerse = BoxesHelper.getPreviousVerse(
                            currentSura, currentVerse);
                        if (prevVerse != null) {
                          setState(() {
                            currentVerse = prevVerse.verseNumber;
                            currentSura = prevVerse.suraNumber;
                            HomeController.to
                                .goToVerse(currentSura, currentVerse);
                          });
                        }
                      },
                      text: "السابق",
                      icon: Icons.arrow_back,
                    ),
                    const Spacer(),
                    // next verse
                    TafseerDialogAction(
                      onTap: () {
                        var nextVerse =
                            BoxesHelper.getNextVerse(currentSura, currentVerse);
                        if (nextVerse != null) {
                          setState(() {
                            currentSura = nextVerse.suraNumber;
                            currentVerse = nextVerse.verseNumber;
                            HomeController.to
                                .goToVerse(currentSura, currentVerse);
                          });
                        }
                      },
                      text: "الآية التالية",
                      icon: Icons.arrow_forward,
                    ),
                    const Spacer(),
                    if (currentTabIndex != 0)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: InkWell(
                          onTap: () async {
                            bool result = await Get.defaultDialog(
                                title: "مشاركه مع المصدر",
                                middleText: "هل تود المشاركه مع المصدر",
                                actions: [
                                  CustomFilledButton(
                                      onPressed: () {
                                        Get.back(result: true);
                                      },
                                      text: ("نعم")),
                                  CustomOutlinedButton(
                                    onPressed: () {
                                      Get.back(result: false);
                                    },
                                    text: "لا",
                                  )
                                ]);

                            var tdbr = await SqlHelper.getVerseTdbrs(
                                currentSura,
                                currentVerse,
                                TadarsConstants.tdbrTypes[currentTabIndex - 1]);

                            var text = "${verse?.verseWithDiac} \n";

                            // text += "==== وقفة======== \n";

                            if (currentTabIndex - 1 == 6) {
                              for (var e in tdbr) {
                                text += "\n=======وقفة========== \n";

                                text += e['details'] + "\n" + e['url'] + " \n";
                                if (result) {
                                  text += "المصدر :   ${e['source_name']}\n";
                                }
                              }
                            } else {
                              for (var e in tdbr) {
                                text += "\n=======وقفة========== \n";

                                text += e['fulltext'] + "\n";
                                if (result) {
                                  text += "المصدر :   ${e['source_name']}\n";
                                }
                              }
                            }

                            text += "\n \n \n تطبيق تدارس القران";

                            Share.share(text);
                          },
                          child: const Icon(
                            Icons.share_outlined,
                            color: Colors.white,
                          ),
                        ),
                      )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class VerseTdbrView extends StatelessWidget {
  final int verseNumber;
  final int suraNumber;
  final TdbrType tdbrType;
  final bool showTdbrTitle;
  const VerseTdbrView(
      {super.key,
      required this.verseNumber,
      required this.suraNumber,
      required this.tdbrType,
      this.showTdbrTitle = false});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: SqlHelper.getVerseTdbrs(suraNumber, verseNumber, tdbrType),
      builder: (BuildContext context,
          AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
              padding: const EdgeInsets.all(16),
              width: double.maxFinite,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor.withOpacity(0.2)
                    : HomeController.to.pageColor.value?.withOpacity(0.2),
                borderRadius: CommonStyles.borderRadius,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.white,
                    highlightColor: Colors.white54,
                    direction: ShimmerDirection.rtl,
                    // enabled: false,

                    // period: const Duration(seconds: 3),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20)),
                      height: 20,
                      width: double.maxFinite,
                    ),
                  ),
                ],
              ));
        }

        var tdbrs = snapshot.data ?? [];
        if (tdbrs.isEmpty) {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 32),
            decoration: BoxDecoration(
              color: Get.isDarkMode
                  ? Get.theme.primaryColor.withOpacity(0.2)
                  : HomeController.to.pageColor.value == Colors.white
                      ? Get.theme.primaryColor.withOpacity(0.2)
                      : HomeController.to.pageColor.value,
              borderRadius: CommonStyles.borderRadius,
            ),
            child: Center(
              child: Text(
                "لاتوجد مادة في هذا القسم".tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode
                      ? Colors.white
                      : HomeController.to.pageColor.value == Colors.white
                          ? Get.theme.primaryColor
                          : HomeController.to.pageColor.value,
                ),
              ),
            ),
          );
        }
        return Column(
          children: tdbrs.map(
            (e) {
              print("item ${e['id']}");
              return TdbrView(
                tdbr: e,
                tdbrType: tdbrType,
                showTdbTitle: showTdbrTitle,
              );
            },
          ).toList(),
        );
      },
    );
  }
}
