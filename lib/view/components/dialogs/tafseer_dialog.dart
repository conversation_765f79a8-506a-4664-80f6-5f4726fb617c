import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../../../controllers/home_controller.dart';
import '../../../controllers/tafseer_controller.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../models/hive/quran_verse.dart';
import '../../../utils/common_functions.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/common_constants.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../pages/home/<USER>/page_views/tafseer_page_view.dart';
import '../../pages/home/<USER>/verse_marker.dart';

class TafseerDialog extends StatefulWidget {
  final int suraNumber;
  final int verseNumber;
  const TafseerDialog({
    super.key,
    required this.suraNumber,
    required this.verseNumber,
  });

  @override
  State<TafseerDialog> createState() => _TafseerDialogState();
}

class _TafseerDialogState extends State<TafseerDialog> {
  @override
  void initState() {
    currentVerse.value = widget.verseNumber;
    currentSura.value = widget.suraNumber;

    HomeController.to.currentPage.listen((pageNumber) {
      currentVerse.value =
          BoxesHelper.getPageById(pageNumber)?.firstVerseNumber ?? 0;
      currentSura.value = BoxesHelper.getPageById(pageNumber)?.suraNumber ?? 0;

      //refresh vese
      verse.value = BoxesHelper.getVerse(currentSura.value, currentVerse.value);
     
    });

    verse.value = BoxesHelper.getVerse(currentSura.value, currentVerse.value);
    super.initState();
  }

  var currentVerse = 0.obs;
  var currentSura = 0.obs;
  var verse = Rx<QuranVerse?>(null);

  @override
  Widget build(BuildContext context) {
    if (!Get.isRegistered<TafseerController>()) {
      Get.put(TafseerController(), permanent: true);
    }

    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Obx(
          () => SizedBox(
            width: Get.width * 0.9,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(
                    8,
                  ),
                  decoration: BoxDecoration(
                    color: CustomColors.primaryColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(10),
                    ),
                  ),
                  width: double.maxFinite,
                  child: Text(
                    "تفسير الآية".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
                Container(
                  constraints: BoxConstraints(
                    minHeight: Get.height * 0.3,
                    maxHeight: Get.height * 0.6,
                  ),
                  child: Stack(
                    fit: StackFit.passthrough,
                    children: [
                      SingleChildScrollView(
                        padding: const EdgeInsets.only(
                          right: 16,
                          left: 16,
                          bottom: 50,
                          top: 8,
                        ),
                        child: Column(
                          children: [
                            Padding(
                              key: Key(Random().toString()),
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Text.rich(
                                TextSpan(
                                  
                                  text:
                                      "${BoxesHelper.getVerse(currentSura.value, currentVerse.value)?.verseWithDiac}",
                                  children: [
                                    WidgetSpan(
                                      alignment: PlaceholderAlignment.middle,
                                      child: VerseMarker(
                                        size: HomeController
                                                .to.pageFontSize.value *
                                            1.2,
                                        verseNumber: currentVerse.value,
                                        suraNumber: currentSura.value,
                                      ),
                                    ),
                                  ],
                                ),
                                style: TextStyle(
                                  fontSize:
                                      HomeController.to.pageFontSize.value,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                ),
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Obx(() {
                              print(TafseerController.instance
                                  .getVerseTafseer(
                                      currentVerse.value, currentSura.value)
                                  ?.text);
                              return Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                    borderRadius: CommonStyles.borderRadius,
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.15)),
                                child: TafseerHtmlView(
                                  htmlText: TafseerController.instance
                                          .getVerseTafseer(currentVerse.value,
                                              currentSura.value)
                                          ?.text ??
                                      "لايوجد تفسير لهذه الأية",
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                      const Positioned(
                        left: 8,
                        bottom: 8,
                        child: TafseerPickerButton(),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(
                    2,
                  ),
                  decoration: BoxDecoration(
                    color: CustomColors.primaryColor,
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(10),
                    ),
                  ),
                  width: double.maxFinite,
                  child: Row(
                    children: [
                      // close
                      TafseerDialogAction(
                        onTap: () {
                          Get.back();
                        },
                        text: "إغلاق",
                        icon: Icons.close,
                      ),
                      const Spacer(),
                      // previos verse
                      TafseerDialogAction(
                        onTap: () {
                          print('previos verse');
                          var page =
                              TafseerController.instance.currentPage.value;

                          if (page != null) {
                            var prevVerse = BoxesHelper.getPreviousVerse(
                                currentSura.value, currentVerse.value);
                            if (prevVerse != null) {
                              setState(() {
                                currentVerse.value = prevVerse.verseNumber;
                                currentSura.value = prevVerse.suraNumber;
                                print(currentVerse.value);
                                HomeController.to.goToVerse(
                                    currentSura.value, currentVerse.value);
                                if (currentSura.value < page.firstSuraNumber) {
                                  HomeController.to
                                      .goToPage(prevVerse.pageNumber - 1);
                                  TafseerController.instance
                                      .needToCallGetTafseer.value = true;
                                } else {
                                  if (currentVerse.value <
                                      page.firstVerseNumber) {
                                    HomeController.to
                                        .goToPage(prevVerse.pageNumber - 1);

                                    TafseerController.instance
                                        .needToCallGetTafseer.value = true;
                                  }
                                }
                              });
                            }
                          }
                        },
                        text: "السابق",
                        icon: Icons.arrow_back,
                      ),
                      const Spacer(),
                      // next verse
                      TafseerDialogAction(
                        onTap: () {
                          var page =
                              TafseerController.instance.currentPage.value;
                          if (page != null) {
                            var nextVerse = BoxesHelper.getNextVerse(
                                currentSura.value, currentVerse.value);
                            if (nextVerse != null) {
                              setState(() {
                                currentSura.value = nextVerse.suraNumber;
                                currentVerse.value = nextVerse.verseNumber;
                                HomeController.to.goToVerse(
                                    currentSura.value, currentVerse.value);
                                if (currentSura.value > page.lastSuraNumber) {
                                  HomeController.to
                                      .goToPage(nextVerse.pageNumber - 1);
                                  TafseerController.instance
                                      .needToCallGetTafseer.value = true;
                                } else {
                                  if (currentVerse.value >
                                      page.lastVerseNumber) {
                                    HomeController.to
                                        .goToPage(nextVerse.pageNumber - 1);

                                    TafseerController.instance
                                        .needToCallGetTafseer.value = true;
                                  }
                                }
                              });
                            }
                          }
                        },
                        text: "الآية التالية",
                        icon: Icons.arrow_forward,
                      ),
                      const Spacer(),
                      // share
                      TafseerDialogAction(
                        onTap: () {
                          // ${verse?.verseWithDiac}($verseNumber)\n
                          var suraName =
                              BoxesHelper.getSuraById(widget.suraNumber)
                                      ?.name ??
                                  "";
                          var tafseerName =
                              TafseerController.instance.tafseer.value?.title ??
                                  "";
                          var text =
                              "${CommonFunctions.stripeHtmlTags((TafseerController.instance.getVerseTafseer(widget.verseNumber, widget.suraNumber)?.text) ?? "")}\n[$suraName - ${widget.verseNumber}] [$tafseerName]\n\n${'تدارس القرآن الكريم'.tr} \n https://tadars.com/app";
                          Share.share(text);
                        },
                        text: "مشاركة".tr,
                        icon: Icons.share,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class TafseerDialogAction extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final IconData icon;
  const TafseerDialogAction({
    Key? key,
    required this.onTap,
    required this.text,
    required this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }
}
