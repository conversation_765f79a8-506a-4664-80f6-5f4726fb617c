import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

import '../../../helpers/sql_helper.dart';
import '../waqafat/tdbr_view.dart';

class ShowTdbrDialog extends StatefulWidget {
  final int tdbrId;
  final TdbrType tdbrType;
  const ShowTdbrDialog({Key? key, required this.tdbrId, required this.tdbrType})
      : super(key: key);

  @override
  State<ShowTdbrDialog> createState() => _ShowTdbrDialogState();
}

class _ShowTdbrDialogState extends State<ShowTdbrDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: <PERSON>zedB<PERSON>(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "عرض الوقفة".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  minHeight: Get.height * 0.2,
                  maxHeight: Get.height * 0.7,
                ),
                child: FutureBuilder<Map<String, dynamic>>(
                  future: SqlHelper.getTdbrById(widget.tdbrId, widget.tdbrType),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    var tdbr = snapshot.data;
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child:
                          TdbrView(tdbr: tdbr ?? {}, tdbrType: widget.tdbrType),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
