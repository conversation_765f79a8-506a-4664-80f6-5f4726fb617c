import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tadars/helpers/sql_helper.dart';

import '../../../enums/tdbr_type.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../../utils/constants/tadars_constants.dart';
import '../waqafat/tdbr_view.dart';

class TafseerDialogAction extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final IconData icon;
  const TafseerDialogAction({
    super.key,
    required this.onTap,
    required this.text,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }
}

class SuraWaqafatDialog extends StatefulWidget {
  final int suraNumber;
  const SuraWaqafatDialog({super.key, required this.suraNumber});

  @override
  State<SuraWaqafatDialog> createState() => _SuraWaqafatDialogState();
}

class _SuraWaqafatDialogState extends State<SuraWaqafatDialog> {
  int currentSura = 0;
  int currentVerse = 0;
  @override
  void initState() {
    currentSura = widget.suraNumber;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var verse = BoxesHelper.getVerse(currentSura, currentVerse);
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: Get.theme.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "وقفات السورة".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              DefaultTabController(
                length: TadarsConstants.getEnabledTdbrTypes().length,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.7,
                    minHeight: Get.height * 0.2,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          right: 8.0,
                          left: 8,
                          bottom: 8,
                        ),
                        child: TabBar(
                          isScrollable: true,
                          tabs: TadarsConstants.getEnabledTdbrTypes().map((e) {
                            return Tab(
                              text: e.name.tr,
                            );
                          }).toList(),
                        ),
                      ),
                      Expanded(
                        child: TabBarView(
                          physics: const NeverScrollableScrollPhysics(),
                          children: TadarsConstants.getEnabledTdbrTypes().map((
                            tdbrType,
                          ) {
                            return ListView(
                              padding: const EdgeInsets.all(8),
                              children: [
                                VerseTdbrView(
                                  verseNumber: currentVerse,
                                  suraNumber: currentSura,
                                  tdbrType: tdbrType,
                                  isSuraTdbr: true,
                                ),
                              ],
                            );
                          }).toList(),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              // Container(
              //   padding: const EdgeInsets.all(
              //     2,
              //   ),
              //   decoration: BoxDecoration(
              //     color: Get.theme.primaryColor,
              //     borderRadius: const BorderRadius.vertical(
              //       bottom: Radius.circular(10),
              //     ),
              //   ),
              //   width: double.maxFinite,
              //   child: Row(
              //     children: [
              //       // close

              //       const Spacer(),
              //       // previos verse
              //       TafseerDialogAction(
              //         onTap: () {
              //           print('previos verse');

              //           var prevVerse = BoxesHelper.getPreviousVerse(
              //               currentSura, currentVerse);
              //           if (prevVerse != null) {
              //             setState(() {
              //               currentVerse = prevVerse.verseNumber;
              //               currentSura = prevVerse.suraNumber;
              //               HomeController.to
              //                   .goToVerse(currentSura, currentVerse);
              //             });
              //           }
              //         },
              //         text: "السابق",
              //         icon: Icons.arrow_back,
              //       ),
              //       const Spacer(),
              //       // next verse
              //       TafseerDialogAction(
              //         onTap: () {
              //           var nextVerse =
              //               BoxesHelper.getNextVerse(currentSura, currentVerse);
              //           if (nextVerse != null) {
              //             setState(() {
              //               currentSura = nextVerse.suraNumber;
              //               currentVerse = nextVerse.verseNumber;
              //               HomeController.to
              //                   .goToVerse(currentSura, currentVerse);
              //             });
              //           }
              //         },
              //         text: "الآية التالية",
              //         icon: Icons.arrow_forward,
              //       ),
              //       const Spacer(),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}

class VerseTdbrView extends StatelessWidget {
  final int verseNumber;
  final int suraNumber;
  final TdbrType tdbrType;
  final bool isSuraTdbr;
  const VerseTdbrView({
    super.key,
    required this.verseNumber,
    required this.suraNumber,
    required this.tdbrType,
    this.isSuraTdbr = false,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: SqlHelper.getVerseTdbrs(suraNumber, verseNumber, tdbrType),
      builder: (BuildContext context,
          AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
              padding: const EdgeInsets.all(16),
              width: double.maxFinite,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withOpacity(0.2),
                borderRadius: CommonStyles.borderRadius,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.white,
                    highlightColor: Colors.white54,
                    direction: ShimmerDirection.rtl,
                    // enabled: false,

                    // period: const Duration(seconds: 3),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20)),
                      height: 20,
                      width: double.maxFinite,
                    ),
                  ),
                ],
              ));
        }

        var tdbrs = snapshot.data ?? [];
        if (tdbrs.isEmpty) {
          return Container(
            height: Get.height * 0.4,
            decoration: BoxDecoration(
              color: Get.theme.primaryColor.withOpacity(0.2),
              borderRadius: CommonStyles.borderRadius,
            ),
            child: Center(
              child: Text(
                "لاتوجد مادة في هذا القسم".tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: CustomColors.primaryColor,
                ),
              ),
            ),
          );
        }
        return Column(
          children: tdbrs.map(
            (e) {
              return TdbrView(
                tdbr: e,
                tdbrType: tdbrType,
                isSuraTdbr: true,
              );
            },
          ).toList(),
        );
      },
    );
  }
}
