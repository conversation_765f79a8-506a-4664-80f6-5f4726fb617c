import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/custom_packages/flutter_html/lib/flutter_html.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/dialogs/tadars_dialog.dart';

import '../../../helpers/boxes_helper.dart';
import '../../../helpers/sql_helper.dart';
import '../../../utils/constants/tadars_constants.dart';
import '../waqafat/tdbr_view.dart';
import '../buttons/cutom_filled_button.dart';

class SourceWaqafatDialog extends StatefulWidget {
  final int sourceId;
  final String sourceName;
  final String sourceDetail;
  const SourceWaqafatDialog({
    Key? key,
    required this.sourceId,
    required this.sourceName,
    required this.sourceDetail,
  }) : super(key: key);

  @override
  State<SourceWaqafatDialog> createState() => _SourceWaqafatDialogState();
}

class _SourceWaqafatDialogState extends State<SourceWaqafatDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "وقفات المصدر: ".tr + widget.sourceName,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              FutureBuilder<Map<TdbrType, int>>(
                  future: SqlHelper.getSource(widget.sourceId),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox(
                        height: 200,
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }
                    var tdbrCounts = snapshot.data;
                    print(tdbrCounts);
                    var filterTdbrsCount = TadarsConstants.getEnabledTdbrTypes()
                        .where((e) => (tdbrCounts?[e] ?? 0) > 0)
                        .toList();
                    print("tdbrs ${filterTdbrsCount.length}");
                    return DefaultTabController(
                      length: filterTdbrsCount.length,
                      child: Container(
                        constraints: BoxConstraints(
                          maxHeight: Get.height * 0.8,
                          minHeight: Get.height * 0.2,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                children: [
                                  // Text(
                                  //   widget.sourceName,
                                  //   style: TextStyle(
                                  //     color: Get.theme.primaryColor,
                                  //     fontWeight: FontWeight.w600,
                                  //     fontSize: 18,
                                  //   ),
                                  // ),
                                  if (widget.sourceId != 1)
                                    Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(
                                            top: 8.0,
                                            bottom: 16,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              CustomFilledButton(
                                                text: 'نبذه عن المصدر',
                                                onPressed: () {
                                                  Get.dialog(TadarsDialog(
                                                      title: 'نبذة عن المصدر',
                                                      content: Column(
                                                        children: [
                                                          Text(
                                                            widget.sourceName,
                                                            style: TextStyle(
                                                              color: Get.theme
                                                                  .primaryColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontSize: 18,
                                                            ),
                                                          ),
                                                          Html(
                                                            data: widget
                                                                .sourceDetail,
                                                          ),
                                                        ],
                                                      )));
                                                },
                                              ),
                                              const SizedBox(
                                                width: 16,
                                              ),
                                              ValueListenableBuilder<Box>(
                                                valueListenable: BoxesHelper
                                                    .getListenableSourceBookmarks(),
                                                builder: (context, box, child) {
                                                  var favorated = BoxesHelper
                                                      .isSourceExistInBookmark(
                                                          widget.sourceId);
                                                  return CustomFilledButton(
                                                    icon: favorated
                                                        ? Icons.star_rounded
                                                        : Icons
                                                            .star_border_rounded,
                                                    text: favorated
                                                        ? 'الغاء التفضيل'
                                                        : 'تفضيل',
                                                    onPressed: () {
                                                      if (favorated) {
                                                        BoxesHelper
                                                            .removeSourceFromBookmark(
                                                                widget
                                                                    .sourceId);
                                                      } else {
                                                        BoxesHelper
                                                            .addSourceToBookmark(
                                                                widget
                                                                    .sourceId);
                                                      }
                                                    },
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                        const Divider(
                                          height: 1,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                            TabBar(
                              isScrollable: filterTdbrsCount.length > 2,
                              onTap: (index) {},
                              tabs: filterTdbrsCount.map((e) {
                                return Tab(
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(e.name.tr),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        Container(
                                          height: 20,
                                          alignment:
                                              AlignmentDirectional.center,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 4),
                                          decoration: BoxDecoration(
                                              color: Get
                                                  .theme.colorScheme.secondary,
                                              borderRadius:
                                                  BorderRadius.circular(16)),
                                          child: Text(
                                            tdbrCounts?[e].toString() ?? '',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 9,
                                              height: 1,
                                            ),
                                          ),
                                        ),
                                      ]),
                                );
                              }).toList(),
                            ),
                            Expanded(
                              child: Stack(
                                children: [
                                  TabBarView(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    children: filterTdbrsCount.map((
                                      tdbrType,
                                    ) {
                                      return TdbrListView(
                                          sourceId: widget.sourceId,
                                          tdbrType: tdbrType);
                                    }).toList(),
                                  ),
                                  // Positioned.directional(
                                  //   textDirection: TextDirection.rtl,
                                  //   bottom: 70,
                                  //   end: 20,
                                  //   child: FloatingActionButton(
                                  //     onPressed: () {
                                  //       launchUrl(Uri.parse(
                                  //           'https://tadars.com/?export=doc'));
                                  //     },
                                  //     tooltip: 'تصدير الوقفات'.tr,
                                  //     child: const Icon(Icons.ios_share_rounded),
                                  //   ),
                                  // )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
            ],
          ),
        ),
      ),
    );
  }
}

class TdbrListView extends StatefulWidget {
  final TdbrType tdbrType;
  final int sourceId;
  const TdbrListView(
      {super.key, required this.tdbrType, required this.sourceId});

  @override
  State<TdbrListView> createState() => _TdbrListViewState();
}

class _TdbrListViewState extends State<TdbrListView> {
  int page = 1;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
        future:
            SqlHelper.getSourceTdbrs(widget.sourceId, widget.tdbrType, page),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.hasError) {
            return Center(
              child: Text(snapshot.error.toString()),
            );
          }
          var tdbrs = snapshot.data?['tdbrs'] ?? [];
          var count = snapshot.data?['count'] ?? 0;
          var pagesCount = snapshot.data?['pages_count'] ?? 0;

          return Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: tdbrs.length,
                  itemBuilder: (BuildContext context, int index) {
                    return TdbrView(
                      tdbr: tdbrs[index],
                      tdbrType: widget.tdbrType,
                    );
                  },
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Get.theme.primaryColor,
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          'إجمالي الوقفات: '.tr + count.toString(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'عدد الصفحات: '.tr + pagesCount.toString(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 2,
                    ),
                    if (pagesCount > 1)
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              if (page > 1) {
                                setState(() {
                                  page--;
                                });
                              }
                            },
                            child: const Text(
                              '<< السابق',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                            
                              child: Center(
                                child: Builder(builder: (context) {
                                  var pageKey = GlobalKey();
                                  // ensure visible
                                  // SchedulerBinding.instance
                                  //     .addPostFrameCallback((_) {
                                  //   var cntx = pageKey.currentContext;

                                  //   if (cntx != null) {
                                  //     Scrollable.ensureVisible(
                                  //       cntx,
                                  //       alignment: 0.5,
                                  //       duration:
                                  //           const Duration(milliseconds: 400),
                                  //       curve: Curves.easeInOut,
                                  //     );
                                  //   }
                                  // });
                                  return Container(
                                    width: 100,
                                    child: DropdownButtonFormField(
                                        isExpanded: false,
                                        value: page,
                                        decoration: InputDecoration(
                                          fillColor:
                                              Colors.white.withOpacity(0.2),
                                              
                                        ),
                                        items: List.generate(
                                            pagesCount,
                                            (index) => DropdownMenuItem(
                                                  value: index + 1,
                                                  child: Text(
                                                    "${index + 1}",
                                                  ),
                                                )),
                                        onChanged: (value) {
                                          setState(() {
                                            page = value!;
                                          });
                                        }),
                                  );
                                  // return Row(
                                  //   mainAxisAlignment: MainAxisAlignment.center,
                                  //   crossAxisAlignment:
                                  //       CrossAxisAlignment.center,
                                  //   children: List.generate(
                                  //     pagesCount,
                                  //     (index) {
                                  //       return InkWell(
                                  //         key: page == index + 1
                                  //             ? pageKey
                                  //             : null,
                                  //         onTap: () {
                                  //           setState(() {
                                  //             page = index + 1;
                                  //           });
                                  //         },
                                  //         child: Container(
                                  //           decoration: BoxDecoration(
                                  //             color: page == index + 1
                                  //                 ? Colors.white
                                  //                 : Colors.white54,
                                  //             borderRadius:
                                  //                 BorderRadius.circular(4),
                                  //           ),
                                  //           margin: const EdgeInsets.all(2.0),
                                  //           padding: const EdgeInsets.symmetric(
                                  //               horizontal: 4, vertical: 2),
                                  //           child: Text((index + 1).toString(),
                                  //               textAlign: TextAlign.center,
                                  //               maxLines: 1,
                                  //               style: TextStyle(
                                  //                 fontSize: 9,
                                  //                 height: 0,
                                  //                 color: Get.theme.primaryColor,
                                  //               )),
                                  //         ),
                                  //       );
                                  //     },
                                  //   ),
                                  // );
                                }),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              if (page < pagesCount) {
                                setState(() {
                                  page++;
                                });
                              }
                            },
                            child: const Text(
                              'التالي >>',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          );
        });
  }
}
