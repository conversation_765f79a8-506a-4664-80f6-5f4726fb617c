import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

import '../../../helpers/boxes_helper.dart';
import '../../../helpers/sql_helper.dart';
import '../../../utils/constants/tadars_constants.dart';
import '../waqafat/tdbr_view.dart';

class TdbrBookmarkDialog extends StatefulWidget {
  const TdbrBookmarkDialog({Key? key}) : super(key: key);

  @override
  State<TdbrBookmarkDialog> createState() => _TdbrBookmarkDialogState();
}

class _TdbrBookmarkDialogState extends State<TdbrBookmarkDialog> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(
                  8,
                ),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "الوقفات المفضلة".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              DefaultTabController(
                length: TadarsConstants.tdbrTypes.length,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.7,
                    minHeight: Get.height * 0.2,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          right: 8.0,
                          left: 8,
                          bottom: 8,
                        ),
                        child: TabBar(
                          isScrollable: true,
                          onTap: (index) {},
                          tabs: TadarsConstants.tdbrTypes.map((e) {
                            return Tab(
                              text: e.name.tr,
                            );
                          }).toList(),
                        ),
                      ),
                      Expanded(
                        child: TabBarView(
                          physics: const NeverScrollableScrollPhysics(),
                          children: TadarsConstants.tdbrTypes.map((
                            tdbrType,
                          ) {
                            return ValueListenableBuilder(
                              valueListenable:
                                  BoxesHelper.getListenableTdbrBookmarks(),
                              builder:
                                  (BuildContext context, value, Widget? child) {
                                var bookmarksIds =
                                    BoxesHelper.getTdbrBookmarksIds(
                                        TadarsConstants.tdbrMapping[tdbrType] ??
                                            "");
                                print(bookmarksIds.length);
                                return FutureBuilder<
                                        List<Map<String, dynamic>>>(
                                    future: SqlHelper.getTdbrByIds(
                                        bookmarksIds, tdbrType),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return const Center(
                                          child: CircularProgressIndicator(),
                                        );
                                      }
                                      if (snapshot.hasError) {
                                        return Center(
                                          child:
                                              Text(snapshot.error.toString()),
                                        );
                                      }
                                      var tdbrs = snapshot.data ?? [];
                                      return ListView.builder(
                                        padding: const EdgeInsets.all(8),
                                        itemCount: tdbrs.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          return TdbrView(
                                            tdbr: tdbrs[index],
                                            tdbrType: tdbrType,
                                          );
                                        },
                                      );
                                    });
                              },
                            );
                          }).toList(),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

