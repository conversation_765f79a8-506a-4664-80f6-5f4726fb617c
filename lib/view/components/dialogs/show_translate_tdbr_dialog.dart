import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/constants/tadars_constants.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';

import '../waqafat/tdbr_view.dart';

class ShowTranslateTdbrDialog extends StatefulWidget {
  final Map<String, dynamic>? tdbr;
  final TdbrType tdbrType;
  const ShowTranslateTdbrDialog({
    super.key,
    required this.tdbr,
    required this.tdbrType,
  });

  @override
  State<ShowTranslateTdbrDialog> createState() => _ShowTdbrDialogState();
}

class _ShowTdbrDialogState extends State<ShowTranslateTdbrDialog> {
  Map<String, dynamic>? tdbr;

  @override
  void initState() {
    tdbr = widget.tdbr;
    print("tdbr $tdbr");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Material(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: SizedBox(
          width: Get.width * 0.9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 45,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withValues(alpha: 0.3),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                width: double.maxFinite,
                child: NavigationToolbar(
                  trailing: IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: CustomColors.primaryColor,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  middle: Text(
                    "ترجمة الوقفة بالذكاء الإصطناعي".tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primaryColor,
                    ),
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable:
                    BoxesHelper.getSelectedWaqfatTranslationListenable(),
                builder: (context, box, child) {
                  String selectedLang =
                      box.get(SettingsConstants.waqfatAItranslation) ?? "en";
                  return Container(
                    constraints: BoxConstraints(
                      minHeight: Get.height * 0.2,
                      maxHeight: Get.height * 0.7,
                    ),
                    child: Stack(
                      children: [
                        FutureBuilder(
                          future: ApiHelper(
                            Dio(),
                            baseUrl: CommonConstants.apiUrl,
                          ).translateTdbr(
                            selectedLang,
                            tdbr!['id'],
                            TadarsConstants.tdbrMappingForDb[widget.tdbrType]!,
                          ),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (snapshot.hasError) {
                              return Center(
                                child: Text(
                                  "هناك خطأ تاكد من اتصالك بالانترنت".tr,
                                ),
                              );
                            }
                            var translatableTdbr = snapshot.data;
                            tdbr?['fulltext'] =
                                translatableTdbr?.fulltext ?? "";
                            return SingleChildScrollView(
                              padding: const EdgeInsets.only(
                                top: 16,
                                left: 16,
                                right: 16,
                                bottom: 50,
                              ),
                              child: TdbrView(
                                tdbr: tdbr ?? {},
                                tdbrType: widget.tdbrType,
                                fullTextDirection: TextDirection.ltr,
                                showAITranslation: false,
                                sourceLable: "Source",
                              ),
                            );
                          },
                        ),

                        PositionedDirectional(
                          bottom: 10,
                          end: 10,
                          child: PopupMenuButton(
                            onSelected: (value) {
                              BoxesHelper.setSelectedWaqfatAITranslation(value);
                            },

                            itemBuilder: (context) {
                              return [
                                PopupMenuItem(
                                  value: "fr",
                                  child: Text("الفرنسية".tr),
                                ),
                                PopupMenuItem(
                                  value: "en",
                                  child: Text("الإنجليزية".tr),
                                ),
                              ];
                            },
                            child: Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: CustomColors.primaryColor,

                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Builder(
                                builder: (context) {
                                  return Row(
                                    children: [
                                      Text(
                                        // onPressed: () {},
                                        selectedLang == "fr"
                                            ? "الفرنسية".tr
                                            : "الإنجليزية".tr,
                                        style: TextStyle(color: Colors.white),
                                      ),
                                      SizedBox(width: 2),
                                      Icon(
                                        Icons.arrow_drop_down,
                                        color: Colors.white,
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
