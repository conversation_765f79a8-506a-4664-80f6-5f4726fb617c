import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';
import 'package:tadars/features/user_data_sync/models/note.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/bookmark.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/dialogs/verse_waqafat_dialog.dart';
import 'package:tadars/view/components/waqafat/tdbr_view.dart';

import '../../../controllers/tadars_controller.dart';
import '../../../features/translation/widgets/translation_dialog.dart';
import '../../../models/hive/quran_sura.dart';
import '../../../models/hive/quran_verse.dart';
import '../../../utils/common_styles.dart';
import '../../../utils/constants/boxes.dart';
import '../../../utils/constants/common_constants.dart';
import '../../../utils/constants/custom_colors.dart';
import '../../../utils/constants/routes.dart';
import '../../../utils/quran_ui_icons2.dart';
import '../bottomsheets/custom_play_bottom_sheet.dart';

class VerseOptionsDialog extends StatelessWidget {
  const VerseOptionsDialog(
      {super.key,
      required this.suraNumber,
      required this.verseNumber,
      required this.pageNumber});

  final int suraNumber;
  final int verseNumber;
  final int pageNumber;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Get.theme.scaffoldBackgroundColor,
              borderRadius: CommonStyles.borderRadius,
            ),
            child: StreamBuilder(
              stream: UserSyncService.instance.bookmarks.stream,
              builder: (context, snapsho) {
                return Row(
                  children: List.generate(
                    CustomColors.bookmarksColors.length,
                    (index) {
                      var bookmark = HomeController.to.getBookmark(
                        suraNumber: suraNumber,
                        verseNumber: verseNumber,
                      );
                      var ci = bookmark != null ? bookmark.colorIndex : -1;
                      return Expanded(
                        child: BookmarkToggle(
                          color: CustomColors.bookmarksColors[index],
                          isActive: ci == index,
                          onToggle: (value) {
                            // if (kDebugMode) {
                            //   print(value);
                            // }
                            if (value) {
                              HomeController.to.saveBookmark(
                                suraNumber: suraNumber,
                                verseNumber: verseNumber,
                                colorIndex: index,
                                pageNumber: pageNumber,
                              );
                            } else {
                              bookmark?.delete();
                            }
                          },
                        ),
                      );
                    },
                  ),
                );
              },
              // valueListenable: Hive.box<Bookmark>(Boxes.bookmarks).listenable(),
            ),
          ),
          Flexible(
            child: Container(
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: Get.theme.scaffoldBackgroundColor,
                borderRadius: CommonStyles.borderRadius,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      dense: true,
                      onTap: () {
                        Get.put(TadarsController());
                        Get.dialog(VerseWaqafatDialog(
                          suraNumber: suraNumber,
                          verseNumber: verseNumber,
                          ),
                        );
                        // Get.back();
                      },
                      leading: const Icon(
                        QuranUIIcons2.news,
                        size: 16,
                      ),
                      title: Text(
                        "عرض وقفات الآية".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                      trailing: Builder(builder: (ctx) {
                        if (!Hive.box(Boxes.settings).get(
                            SettingsConstants.showWaqfatCountKey,
                            defaultValue: false)) {
                          return const SizedBox.shrink();
                        }
                        int count = 0;
                        if (HomeController.to.waqafatCounts.value.isNotEmpty) {
                          count = HomeController.to.waqafatCounts.value[
                              "${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}"];
                        }

                        return Text(
                            Get.locale?.languageCode == "ar"
                                ? count.toArabicNumber()
                                : count.toString(),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          style: TextStyle(
                              // fontSize: size * 0.35,
                              fontFamily: CommonConstants.numbersFontFamily,
                              fontWeight: FontWeight.bold,
                              color: CustomColors.primaryColor),
                        );
                        // return Container(
                        //   height: 30,
                        //   width: 30,
                        //   // padding: EdgeInsets.all(0.12 * size),
                        //   decoration: BoxDecoration(
                        //       color: HomeController.to.pageColor.value
                        //           ?.withOpacity(0.1),
                        //       border: Border.all(
                        //         color: CustomColors.primaryColor,
                        //       ),
                        //       shape: BoxShape.circle),
                        //   child: Center(
                        //     child: Text(
                        //       count.toArabicNumber(),
                        //       textAlign: TextAlign.center,
                        //       maxLines: 1,
                        //       style: TextStyle(
                        //           // fontSize: size * 0.35,
                        //           fontFamily:
                        //               CommonConstants.numbersFontFamily,
                        //           fontWeight: FontWeight.bold,
                        //           color: CustomColors.primaryColor),
                        //     ),
                        //   ),
                        // );
                      }),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () {
                        // CommonFunctions.showAddNewTdbrDailog(
                        //     verseNumber, suraNumber);

                        Get.toNamed(Routes.newTdbr, arguments: {
                          'suraNumber': suraNumber,
                          'verseNumber': verseNumber,
                        });

                        // HomeController.to.futuretdbrSorces =
                        //     SqlHelper.getSources();
                      },
                      leading: const Icon(
                        QuranUIIcons2.addNote,
                        size: 16,
                      ),
                      title: Text(
                        "اضافة وقفة جديدة".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () async {
                        Get.back();
                        QuranAudioService.playVerse(suraNumber, verseNumber);
                      },
                      leading: const Icon(
                        QuranUIIcons.play,
                        size: 16,
                      ),
                      title: Text(
                        "تشغيل هذه الآية فقط".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () async {
                        Get.back();
                        QuranAudioService.playFromVerse(
                            suraNumber, verseNumber);
                      },
                      leading: const Icon(
                        QuranUIIcons.play,
                        size: 16,
                      ),
                      title: Text(
                        "بدء التشغيل من هذه الآية".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () async {
                        Get.back();
                        await Future.delayed(const Duration(milliseconds: 280));
                        Get.bottomSheet(CustomPlayBottomSheet(
                          surahNumber: suraNumber,
                          verseNumber: verseNumber,
                        ));
                      },
                      leading: const Icon(
                        QuranUIIcons.play,
                        size: 16,
                      ),
                      title: Text(
                        "تشغيل مخصص".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),



                    Builder(
                      builder: (context) {
                        var formKey = GlobalKey<FormState>();
                        var note = UserSyncService.instance.getNote(
                          suraNumber,
                          verseNumber,
                        );
                        if (kDebugMode) {
                          print(note?.toJson());
                        }
                        var noteText = note?.note ?? '';
                        return ListTile(
                          title: Text(
                            note == null
                                ? 'إضافة ملاحظة'.tr
                                : 'عرض الملاحظة'.tr,

                            style: TextStyle(fontSize: 16),
                          ),
                          leading: const Icon(QuranUIIcons.edit, size: 18),
                          onTap: () {
                            Get.back();
                            Get.customDialog(
                              title: 'الملاحظة'.tr,
                              content: SingleChildScrollView(
                                child: Form(
                                  key: formKey,
                                  child: TextFormField(
                                    controller: TextEditingController.fromValue(
                                      TextEditingValue(text: noteText),
                                    ),
                                    validator: (value) {
                                      if (value?.trim().isEmpty ?? true) {
                                        return 'لايمكن ان تكون الملاحظة فارغة'
                                            .tr;
                                      }
                                      return null;
                                    },
                                    onSaved: (value) {
                                      noteText = value ?? '';
                                    },
                                    minLines: 4,
                                    maxLines: 6,
                                    decoration: InputDecoration(
                                      hintText: 'اكتب ملاحظتك هنا...'.tr,
                                      contentPadding: const EdgeInsets.all(8),
                                      border: OutlineInputBorder(
                                        borderSide: const BorderSide(
                                          width: 1,
                                          color: Colors.grey,
                                        ),
                                        borderRadius: CommonStyles.borderRadius,
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: const BorderSide(
                                          width: 1,
                                          color: Colors.grey,
                                        ),
                                        borderRadius: CommonStyles.borderRadius,
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          width: 1,
                                          color: Colors.red,
                                        ),
                                        borderRadius: CommonStyles.borderRadius,
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          width: 1,
                                          color: Colors.grey.withValues(
                                            alpha: 0.2,
                                          ),
                                        ),
                                        borderRadius: CommonStyles.borderRadius,
                                      ),
                                      alignLabelWithHint: true,
                                    ),
                                  ),
                                ),
                              ),
                              onConfirm: () {
                                if (formKey.currentState?.validate() ?? false) {
                                  formKey.currentState?.save();
                                  Get.back();
                                  var note = Note(
                                    suraNumber: suraNumber,
                                    note: noteText,
                                    createdAt: DateTime.now(),
                                    verseNumber: verseNumber,
                                  );
                                  UserSyncService.instance.updateOrAddNote(
                                    note,
                                  );
                                  // BoxesHelper.addToNotes();
                                }
                              },
                              textConfirm: 'حفظ'.tr,
                              buttonColor: CustomColors.primaryColor,
                              confirmTextColor: Colors.white,
                              cancelTextColor: CustomColors.primaryColor,
                              textCancel: note != null ? 'حذف'.tr : null,
                              onCancel:
                                  note == null
                                      ? null
                                      : () {
                                        // if (kDebugMode) {
                                        //   debugPrint("delete note ${note.note}");
                                        // }
                                        Get.defaultDialog(
                                          title: 'تأكيد الحذف '.tr,
                                          middleText:
                                              'هل أنت متأكد من حذف الملاحظة؟'
                                                  .tr,
                                          textConfirm: 'نعم'.tr,
                                          textCancel: 'لا'.tr,
                                          buttonColor:
                                              CustomColors.primaryColor,
                                          confirmTextColor: Colors.white,
                                          cancelTextColor:
                                              CustomColors.primaryColor,
                                          onConfirm: () {
                                            Get.back();
                                            Get.back();
                                            UserSyncService.instance.deleteNote(
                                              suraNumber,
                                              verseNumber,
                                            );
                                          },
                                        );
                                      },
                            );
                          },
                        );

                        // ListTile(
                        //   dense: true,
                        //   onTap: () {
                        //     Get.back();
                        //     Get.customDialog(
                        //       title: 'الملاحظة'.tr,
                        //       content: SingleChildScrollView(
                        //         child: Form(
                        //           key: formKey,
                        //           child: TextFormField(
                        //             controller: TextEditingController.fromValue(
                        //               TextEditingValue(text: noteText),
                        //             ),
                        //             validator: (value) {
                        //               if (value?.trim().isEmpty ?? true) {
                        //                 return 'لايمكن ان تكون الملاحظة فارغة'
                        //                     .tr;
                        //               }
                        //               return null;
                        //             },
                        //             onSaved: (value) {
                        //               noteText = value ?? '';
                        //             },
                        //             minLines: 4,
                        //             maxLines: 6,
                        //             decoration: InputDecoration(
                        //               hintText: 'اكتب ملاحظتك هنا...'.tr,
                        //               contentPadding: const EdgeInsets.all(8),
                        //               border: OutlineInputBorder(
                        //                 borderSide: const BorderSide(
                        //                   width: 1,
                        //                   color: Colors.grey,
                        //                 ),
                        //                 borderRadius: CommonStyles.borderRadius,
                        //               ),
                        //               focusedBorder: OutlineInputBorder(
                        //                 borderSide: const BorderSide(
                        //                   width: 1,
                        //                   color: Colors.grey,
                        //                 ),
                        //                 borderRadius: CommonStyles.borderRadius,
                        //               ),
                        //               errorBorder: OutlineInputBorder(
                        //                 borderSide: const BorderSide(
                        //                   width: 1,
                        //                   color: CustomColors.kRedColor,
                        //                 ),
                        //                 borderRadius: CommonStyles.borderRadius,
                        //               ),
                        //               enabledBorder: OutlineInputBorder(
                        //                 borderSide: BorderSide(
                        //                   width: 1,
                        //                   color: Colors.grey.withValues(alpha:0.2),
                        //                 ),
                        //                 borderRadius: CommonStyles.borderRadius,
                        //               ),
                        //               alignLabelWithHint: true,
                        //             ),
                        //           ),
                        //         ),
                        //       ),
                        //       onConfirm: () {
                        //         if (formKey.currentState?.validate() ?? false) {
                        //           formKey.currentState?.save();
                        //           Get.back();
                        //           BoxesHelper.addToNotes(
                        //             Note(
                        //               suraNumber: suraNumber,
                        //               note: noteText,
                        //               createdAt: DateTime.now(),
                        //               verseNumber: verseNumber,
                        //               pageNumber: pageNumber,
                        //             ),
                        //           );
                        //         }
                        //       },
                        //       textConfirm: 'حفظ'.tr,
                        //       buttonColor: CustomColors.primaryColor,
                        //       confirmTextColor: Colors.white,
                        //       cancelTextColor: CustomColors.primaryColor,
                        //       textCancel: note != null ? 'حذف'.tr : null,
                        //       onCancel: note == null
                        //           ? null
                        //           : () {
                        //               // if (kDebugMode) {
                        //               //   debugPrint("delete note ${note.note}");
                        //               // }
                        //               Get.defaultDialog(
                        //                 title: 'تأكيد الحذف '.tr,
                        //                 middleText:
                        //                     'هل أنت متأكد من حذف الملاحظة؟'.tr,
                        //                 textConfirm: 'نعم'.tr,
                        //                 textCancel: 'لا'.tr,
                        //                 buttonColor: CustomColors.primaryColor,
                        //                 confirmTextColor: Colors.white,
                        //                 cancelTextColor:
                        //                     CustomColors.primaryColor,
                        //                 onConfirm: () {
                        //                   Get.back();
                        //                   Get.back();
                        //                   BoxesHelper.deleteNote(
                        //                       suraNumber, verseNumber);
                        //                 },
                        //               );
                        //             },
                        //     );
                        //   },
                        //   leading: const Icon(
                        //     QuranUIIcons.edit,
                        //     size: 18,
                        //   ),
                        //   title: Text(note == null
                        //       ? 'إضافة ملاحظة'.tr
                        //       : 'عرض الملاحظة'.tr),
                        // );
                      },
                    ),

                    // ValueListenableBuilder<Box<Note>>(
                    //     valueListenable: BoxesHelper.getListenableNote(
                    //         suraNumber, verseNumber),
                    //     builder: (context, box, child) {
                    //       var formKey = GlobalKey<FormState>();
                    //       Note? note =
                    //           BoxesHelper.getNote(suraNumber, verseNumber);
                    //       // if (kDebugMode) {
                    //       //   print(note?.key);
                    //       // }
                    //       String noteText = note?.note ?? "";
                    //       return ListTile(
                    //         dense: true,
                    //         onTap: () {
                    //           Get.back();
                    //           Get.customDialog(
                    //             title: "الملاحظة".tr,
                    //             content: SingleChildScrollView(
                    //               child: Form(
                    //                 key: formKey,
                    //                 child: TextFormField(
                    //                   controller:
                    //                       TextEditingController.fromValue(
                    //                     TextEditingValue(text: noteText),
                    //                   ),
                    //                   validator: (value) {
                    //                     if (value?.trim().isEmpty ?? true) {
                    //                       return "لايمكن ان تكون الملاحظة فارغة"
                    //                           .tr;
                    //                     }
                    //                     return null;
                    //                   },
                    //                   onSaved: (value) {
                    //                     noteText = value ?? "";
                    //                   },
                    //                   minLines: 4,
                    //                   maxLines: 6,
                    //                   decoration: InputDecoration(
                    //                     hintText: "اكتب ملاحظتك هنا...".tr,
                    //                     contentPadding: const EdgeInsets.all(8),
                    //                     border: OutlineInputBorder(
                    //                       borderSide: const BorderSide(
                    //                         width: 1,
                    //                         color: Colors.grey,
                    //                       ),
                    //                       borderRadius:
                    //                           CommonStyles.borderRadius,
                    //                     ),
                    //                     focusedBorder: OutlineInputBorder(
                    //                       borderSide: const BorderSide(
                    //                         width: 1,
                    //                         color: Colors.grey,
                    //                       ),
                    //                       borderRadius:
                    //                           CommonStyles.borderRadius,
                    //                     ),
                    //                     errorBorder: OutlineInputBorder(
                    //                       borderSide: const BorderSide(
                    //                         width: 1,
                    //                         color: Colors.red,
                    //                       ),
                    //                       borderRadius:
                    //                           CommonStyles.borderRadius,
                    //                     ),
                    //                     enabledBorder: OutlineInputBorder(
                    //                       borderSide: BorderSide(
                    //                         width: 1,
                    //                         color: Colors.grey.withOpacity(0.2),
                    //                       ),
                    //                       borderRadius:
                    //                           CommonStyles.borderRadius,
                    //                     ),
                    //                     alignLabelWithHint: true,
                    //                   ),
                    //                 ),
                    //               ),
                    //             ),
                    //             onConfirm: () {
                    //               if (formKey.currentState?.validate() ??
                    //                   false) {
                    //                 formKey.currentState?.save();
                    //                 Get.back();
                    //                 BoxesHelper.addToNotes(
                    //                   Note(
                    //                     suraNumber: suraNumber,
                    //                     note: noteText,
                    //                     createdAt: DateTime.now(),
                    //                     verseNumber: verseNumber,
                    //                     pageNumber: pageNumber,
                    //                   ),
                    //                 );
                    //               }
                    //             },
                    //             textConfirm: "حفظ".tr,
                    //             buttonColor: CustomColors.primaryColor,
                    //             confirmTextColor: Colors.white,
                    //             cancelTextColor: CustomColors.primaryColor,
                    //             textCancel: note != null ? "حذف".tr : null,
                    //             onCancel: note == null
                    //                 ? null
                    //                 : () {
                    //                     // if (kDebugMode) {
                    //                     //   print("delete note ${note.note}");
                    //                     // }
                    //                     Get.defaultDialog(
                    //                       title: "تأكيد الحذف ".tr,
                    //                       middleText:
                    //                           "هل أنت متأكد من حذف الملاحظة؟"
                    //                               .tr,
                    //                       textConfirm: "نعم".tr,
                    //                       textCancel: "لا".tr,
                    //                       buttonColor:
                    //                           CustomColors.primaryColor,
                    //                       confirmTextColor: Colors.white,
                    //                       cancelTextColor:
                    //                           CustomColors.primaryColor,
                    //                       onConfirm: () {
                    //                         Get.back();
                    //                         Get.back();
                    //                         BoxesHelper.deleteNote(
                    //                             suraNumber, verseNumber);
                    //                       },
                    //                     );
                    //                   },
                    //           );
                    //         },
                    //         leading: const Icon(
                    //           QuranUIIcons.edit,
                    //           size: 16,
                    //         ),
                    //         title: Text(
                    //           note == null
                    //               ? "إضافة ملاحظة".tr
                    //               : "عرض الملاحظة".tr,
                    //           style: const TextStyle(
                    //             fontSize: 16,
                    //           ),
                    //         ),
                    //       );
                    //     }),




                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () {
                        Get.back();
                        HomeController.to
                            .showTafseerDialog(suraNumber, verseNumber);
                      },
                      leading: const Icon(
                        QuranUIIcons2.book,
                        size: 16,
                      ),
                      title: Text(
                        "عرض التفسير".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 60,
                    ),
                    ListTile(
                      dense: true,
                      onTap: () {
                        Get.back();
                        Get.dialog(
                          TranslationDialog(
                            suraNumber: suraNumber,
                            verseNumber: verseNumber,
                          ),
                          barrierDismissible: false
                        );
                      },
                      leading: const Icon(QuranUIIcons2.book, size: 16),
                      title: Text(
                        "عرض الترجمة".tr,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    const Divider(height: 1, indent: 60),
                    ListTile(
                      dense: true,
                      onTap: () {
                        var sura = Hive.box<QuranSura>(Boxes.quranSuar).get(
                          suraNumber,
                        );
                        // if (kDebugMode) {
                        //   print(sura);
                        // }
                        ValueNotifier<int> from =
                            ValueNotifier<int>(verseNumber);
                        int to = verseNumber;
                        Get.back();
                        Get.defaultDialog(
                          radius: 9,
                          title: "${"مشاركة".tr} ${"من".tr} ${sura!.name.tr}",
                          contentPadding: const EdgeInsets.all(0),
                          content: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: Row(
                                  children: [
                                    Text("من".tr),
                                    Expanded(
                                      child: CustomNumberPaicker(
                                        minValue: 1,
                                        maxValue: sura.versesCount,
                                        value: from.value,
                                        onChanged: (value) {
                                          from.value = value;
                                        },
                                      ),
                                    ),
                                    Text("الى".tr),
                                    Expanded(
                                      child: ValueListenableBuilder<int>(
                                        builder: (context, value, child) {
                                          to = value;
                                          return CustomNumberPaicker(
                                              key: Key("key$value"),
                                              minValue: value,
                                              maxValue: sura.versesCount,
                                              value: value,
                                              onChanged: (value) {
                                                to = value;
                                              });
                                        },
                                        valueListenable: from,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              const Divider(
                                height: 1,
                                indent: 60,
                              ),
                              ListTile(
                                dense: true,
                                onTap: () {
                                  // ignore: todo
                                  //TODO get Solution for this
                                  var verses =
                                      Hive.box<QuranVerse>(Boxes.quranVerses)
                                          .valuesBetween(
                                            startKey:
                                                "${suraNumber.toString().padLeft(3, "0")}_${from.value.toString().padLeft(3, "0")}",
                                            endKey:
                                                "${suraNumber.toString().padLeft(3, "0")}_${to.toString().padLeft(3, "0")}",
                                          )
                                          .toList();

                                  // var children = List.generate(
                                  //     verses.length,
                                  //     (index) => TextSpan(
                                  //             text:
                                  //                 "${verses[index].verseWithDiac}",
                                  //             children: [
                                  //               WidgetSpan(
                                  //                 child: VerseMarker(
                                  //                   verseNumber:
                                  //                       verses[index].verseNumber,
                                  //                   suraNumber: suraNumber,
                                  //                   size: 16,
                                  //                 ),
                                  //               ),
                                  //             ]));

                                  // List<Widget> list = [];
                                  // for (var item in verses) {
                                  //   list.add(
                                  //     Text(
                                  //       "${item.verseWithDiac}",
                                  //       textAlign: TextAlign.justify,
                                  //       style: TextStyle(
                                  //         fontSize: 16,
                                  //         fontFamily:
                                  //             CommonConstants.hafsFontFamily,
                                  //         color: Colors.black,
                                  //       ),
                                  //     ),
                                  //   );
                                  //   list.add(Container(
                                  //     child: DefaultTextStyle(
                                  //       style: TextStyle(
                                  //         color: Colors.black,
                                  //       ),
                                  //       child: VerseMarker(
                                  //         size: 16,
                                  //         verseNumber: item.verseNumber,
                                  //         suraNumber: item.suraNumber,
                                  //       ),
                                  //     ),
                                  //   ));
                                  // }
                                  Widget texts =
                                      VersesTextWidget(verses: verses);
                                  // Widget temp = RichText(
                                  //     text: TextSpan(
                                  //         text: "\u202E", children: children));
                                  var screenshotController =
                                      ScreenshotController();
                                  Get.back();
                                  Get.defaultDialog(
                                    title: "مشاركة كصورة".tr,
                                    content: Container(
                                      constraints: const BoxConstraints(
                                        maxHeight: 400,
                                      ),
                                      child: SingleChildScrollView(
                                        child: Directionality(
                                          textDirection: TextDirection.rtl,
                                          child: Screenshot(
                                            controller: screenshotController,
                                            child: Container(
                                              color: Colors.white,
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(8),
                                                color: CustomColors.primaryColor
                                                    .withOpacity(0.1),
                                                width: 600,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      margin:
                                                          const EdgeInsets.only(
                                                              top: 4,
                                                              bottom: 4),
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: CustomColors
                                                                      .primaryColor
                                                                      .withOpacity(
                                                                          0.15),
                                                                ),
                                                                child:
                                                                    Image.asset(
                                                                  "assets/images/header.png",
                                                                  fit: BoxFit
                                                                      .fill,
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                width: double
                                                                    .maxFinite,
                                                                child: Icon(
                                                                  suraNumber
                                                                      .toSuraIconData(),
                                                                  size: 16,
                                                                  color: Colors
                                                                      .black,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          //temp
                                                          texts,
                                                          SizedBox(
                                                            width: double
                                                                .maxFinite,
                                                            child: Image.asset(
                                                                'assets/images/share_logo.png',
                                                                height: 25,
                                                                alignment: Alignment
                                                                    .centerLeft),
                                                          )
                                                          // Wrap(
                                                          //   children: list,
                                                          // )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    textConfirm: "مشاركة".tr,
                                    onConfirm: () {
                                      Get.back();
                                      screenshotController
                                          .capture(pixelRatio: 4)
                                          .then(
                                        (value) async {
                                          if (value != null) {
                                            // var fileName =
                                            //     "${Random().nextInt(99999)}.png";

                                            // final directory = Platform.isIOS? :
                                            //     await getExternalStorageDirectory();
                                            // var fileImg = File(
                                            //     '${directory!.path}/$fileName');
                                            // if (kDebugMode) {
                                            //   print(fileImg.path);
                                            // }
                                            // await fileImg
                                            //     .writeAsBytes(List.from(value));
                                            // var path = fileImg.path;
                                            await Share.shareXFiles([
                                              XFile.fromData(
                                                value,
                                                name: 'tadars_share.png',
                                                mimeType: 'image/png',
                                              )
                                            ]);
                                          }
                                        },
                                      );
                                    },
                                    textCancel: "الغاء الأمر".tr,
                                    confirmTextColor: Colors.white,
                                    buttonColor: Get.theme.primaryColor,
                                    cancelTextColor: Get.theme.primaryColor,
                                  );
                                },
                                leading: const Icon(
                                  QuranUIIcons.image,
                                  size: 16,
                                ),
                                title: Text(
                                  "مشاركة كصورة".tr,
                                ),
                              ),
                              const Divider(
                                height: 1,
                                indent: 60,
                              ),
                              ListTile(
                                dense: true,
                                onTap: () {
                                  // ignore: todo
                                  //TODO get Solution for this
                                  var verses =
                                      Hive.box<QuranVerse>(Boxes.quranVerses)
                                          .valuesBetween(
                                            startKey:
                                                "${suraNumber.toString().padLeft(3, "0")}_${from.value.toString().padLeft(3, "0")}",
                                            endKey:
                                                "${suraNumber.toString().padLeft(3, "0")}_${to.toString().padLeft(3, "0")}",
                                          )
                                          .toList();

                                  // var children = List.generate(
                                  //     verses.length,
                                  //     (index) => TextSpan(
                                  //             text:
                                  //                 "${verses[index].verseWithDiac}",
                                  //             children: [
                                  //               WidgetSpan(
                                  //                 child: VerseMarker(
                                  //                   verseNumber:
                                  //                       verses[index].verseNumber,
                                  //                   suraNumber: suraNumber,
                                  //                   size: 16,
                                  //                 ),
                                  //               ),
                                  //             ]));

                                  // List<Widget> list = [];
                                  // for (var item in verses) {
                                  //   list.add(
                                  //     Text(
                                  //       "${item.verseWithDiac}",
                                  //       textAlign: TextAlign.justify,
                                  //       style: TextStyle(
                                  //         fontSize: 16,
                                  //         fontFamily:
                                  //             CommonConstants.hafsFontFamily,
                                  //         color: Colors.black,
                                  //       ),
                                  //     ),
                                  //   );
                                  //   list.add(Container(
                                  //     child: DefaultTextStyle(
                                  //       style: TextStyle(
                                  //         color: Colors.black,
                                  //       ),
                                  //       child: VerseMarker(
                                  //         size: 16,
                                  //         verseNumber: item.verseNumber,
                                  //         suraNumber: item.suraNumber,
                                  //       ),
                                  //     ),
                                  //   ));
                                  // }
                                  var versesNumbers = from.value == to
                                      ? "$to"
                                      : "${from.value.toArabicNumber()} - ${to.toArabicNumber()}";

                                  Get.back();
                                  Get.dialog(
                                    ChooseShareImageDialog(
                                      shareText: "",
                                      sourceText:
                                          "[${sura.nameWithDiac} : $versesNumbers]",
                                      verses: verses,
                                    ),
                                  );
                                },
                                leading: const Icon(
                                  QuranUIIcons.image,
                                  size: 16,
                                ),
                                title: Text(
                                  "مشاركة على صورة".tr,
                                ),
                              ),
                              const Divider(
                                height: 1,
                                indent: 60,
                              ),
                              ListTile(
                                dense: true,
                                onTap: () {
                                  var verses =
                                      Hive.box<QuranVerse>(Boxes.quranVerses)
                                          .valuesBetween(
                                    startKey:
                                        "${suraNumber.toString().padLeft(3, "0")}_${from.value.toString().padLeft(3, "0")}",
                                    endKey:
                                        "${suraNumber.toString().padLeft(3, "0")}_${to.toString().padLeft(3, "0")}",
                                  );
                                  var text = "";
                                  for (var item in verses) {
                                    text +=
                                        "${item.verseWithoutDiac} (${item.verseNumber.toArabicNumber()}) ";
                                  }
                                  var versesNumbers = from.value == to
                                      ? "$to"
                                      : "${from.value.toArabicNumber()} - ${to.toArabicNumber()}";
                                  Share.share("""
                  ﴿$text﴾ [${sura.name} : $versesNumbers]

                  ${"تدارس القرآن الكريم".tr}
                  https://tadars.com/app""");
                                },
                                leading: Text(
                                  "ض",
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                title: Text(
                                  "مشاركة الآيات بدون تشكيل".tr,
                                ),
                              ),
                              const Divider(
                                height: 1,
                                indent: 60,
                              ),
                              ListTile(
                                dense: true,
                                onTap: () {
                                  var verses =
                                      Hive.box<QuranVerse>(Boxes.quranVerses)
                                          .valuesBetween(
                                    startKey:
                                        "${suraNumber.toString().padLeft(3, "0")}_${from.value.toString().padLeft(3, "0")}",
                                    endKey:
                                        "${suraNumber.toString().padLeft(3, "0")}_${to.toString().padLeft(3, "0")}",
                                  );
                                  var text = "";
                                  for (var item in verses) {
                                    text +=
                                        "${item.verseWithDiac} (${item.verseNumber.toArabicNumber()}) ";
                                  }
                                  var versesNumbers = from.value == to
                                      ? "$to"
                                      : "${from.value.toArabicNumber()} - ${to.toArabicNumber()}";
                                  Share.share("""
                  ﴿$text﴾ [${sura.nameWithDiac} : $versesNumbers]

                  ${"تدارس القرآن الكريم".tr}
                  https://tadars.com/app""");
                                },
                                leading: Text(
                                  "ضَّ",
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                title: Text(
                                  "مشاركة الآيات مع التشكيل".tr,
                                ),
                              ),
                              // const Divider(
                              //   height: 1,
                              //   indent: 60,
                              // ),
                              //                             ListTile(
                              //                               dense: true,
                              //                               onTap: () {
                              //                                 var verses =
                              //                                     Hive.box<QuranVerse>(Boxes.quranVerses)
                              //                                         .valuesBetween(
                              //                                   startKey:
                              //                                       suraNumber.toString().padLeft(3, "0") +
                              //                                           "_" +
                              //                                           from.value.toString().padLeft(3, "0"),
                              //                                   endKey:
                              //                                       suraNumber.toString().padLeft(3, "0") +
                              //                                           "_" +
                              //                                           to.toString().padLeft(3, "0"),
                              //                                 );
                              //                                 var text = "";
                              //                                 for (var item in verses) {
                              //                                   text +=
                              //                                       "${item.verseWithoutDiac} (${item.verseNumber.toArabicNumber()}) \n ${item.translation} \n";
                              //                                 }
                              //                                 var versesNumbers = from.value == to
                              //                                     ? "$to"
                              //                                     : "${from.value.toArabicNumber()} - ${to.toArabicNumber()}";
                              //                                 Share.share("""
                              // $text [${sura.name} : $versesNumbers]

                              // ${"تدارس القرآن الكريم".tr}
                              // https://tadars.com/app""");
                              //                               },
                              //                               leading: const Icon(
                              //                                 QuranUIIcons.translation,
                              //                                 size: 16,
                              //                               ),
                              //                               title: Text(
                              //                                 "مشاركة الآيات مع الترجمة".tr,
                              //                               ),
                              //                             ),
                            ],
                          ),
                        );
                      },
                      leading: const Icon(
                        QuranUIIcons.share,
                        size: 16,
                      ),
                      title: Text(
                        "مشاركة".tr,
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
