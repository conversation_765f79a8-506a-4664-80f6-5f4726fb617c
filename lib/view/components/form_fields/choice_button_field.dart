import 'package:flutter/material.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import '../../../utils/common_styles.dart';

class ChoiceItem<T> {
  String label;
  T value;
  ChoiceItem({required this.label, required this.value});
}

class ChoiceButtonField<T> extends StatelessWidget {
  const ChoiceButtonField({
    super.key,
    required this.items,
    this.onSaved,
    this.validator,
    this.margin,
    this.onChanged,
    required this.initialValue,
  });
  final List<ChoiceItem<T>> items;
  final EdgeInsetsGeometry? margin;
  final void Function(T?)? onSaved;
  final void Function(T?)? onChanged;
  final String Function(T?)? validator;
  final T initialValue;

  @override
  Widget build(BuildContext context) {
    return FormField<T>(
      onSaved: onSaved,
      validator: validator,
      initialValue: initialValue,
      builder: (FormFieldState<T> field) {
        return Container(
          margin: margin,
          decoration: BoxDecoration(
            borderRadius: CommonStyles.borderRadius,
          ),
          width: double.maxFinite,
          child: Row(
            children: List.generate(items.length, (index) {
              bool isSelected = items[index].value == field.value;
              return Expanded(
                child: Container(
                  decoration: isSelected ? const BoxDecoration() : null,
                  margin: const EdgeInsetsDirectional.only(end: 8),
                  child: InkWell(
                    onTap: () {
                      field.didChange(items[index].value);
                      onChanged!(items[index].value);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        borderRadius: CommonStyles.borderRadius,
                        border: isSelected
                            ? Border.all(
                                width: 1, color: CustomColors.accentColor)
                            : Border.all(
                                width: 0.1, color: Colors.grey.shade100),
                      ),
                      child: Text(
                        items[index].label,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
