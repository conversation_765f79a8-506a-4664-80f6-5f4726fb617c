import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../../utils/constants/custom_colors.dart';

class SearchableDropdownField<T> extends StatelessWidget {
  const SearchableDropdownField({
    super.key,
    required this.items,
    this.onChanged,
    this.initialItem,
    this.listItemBuilder,
    this.hintText,
    this.noResultFoundText,
    this.searchHintText,
    this.validator,
    this.padding,
    this.margin,
    this.headerBuilder,
    this.hintBuilder,
    this.onSaved,
  });
  final List<T> items;
  final void Function(T? value)? onChanged;
  final T? initialItem;
  final Widget Function(BuildContext context, T item, bool isSelected,
      void Function() onItemSelect)? listItemBuilder;
  final String? hintText;
  final String? noResultFoundText;
  final String? searchHintText;
  final String? Function(T? value)? validator;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Widget Function(BuildContext context, T item, bool isSelected)?
      headerBuilder;
  final Widget Function(BuildContext context, String item, bool isSelected)?
      hintBuilder;

  final void Function(T?)? onSaved;
  @override
  Widget build(BuildContext context) {
    return FormField(
        initialValue: initialItem,
        onSaved: onSaved,
        builder: (state) {
          return Container(
            padding: padding,
            margin: margin,
            child: CustomDropdown.search(
              hintText: hintText?.tr ?? 'بحث'.tr,
              headerBuilder: headerBuilder,
              hintBuilder: hintBuilder,
            //sea

              items: items,
              onChanged: (value) {
                state.didChange(value);
                if (onChanged != null) onChanged!(value);
              },
              initialItem: initialItem,
              searchHintText: searchHintText?.tr ?? 'بحث'.tr,
              decoration: CustomDropdownDecoration(
                closedBorder:
                    Border.all(width: 1, color: CustomColors.primaryColor),
                closedBorderRadius: BorderRadius.circular(10),
                expandedBorder:
                    Border.all(width: 1, color: CustomColors.primaryColor),
              ),
              listItemBuilder: listItemBuilder,
              noResultFoundText: noResultFoundText?.tr ?? 'لايوجد نتائج'.tr,
              validator: validator,

              // futureRequest: futureRequest,
            ),
          );
        });
  }
}
