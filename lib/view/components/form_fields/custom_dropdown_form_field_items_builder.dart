// // import 'package:flutter/material.dart';
// // import 'package:get/get.dart';
// // import 'package:tadars/utils/quran_ui_icons2.dart';

// // class CustomDropDownFormField<T, V> extends StatefulWidget {
// //   final List<T> items;
// //   final void Function(V?) onChanged;
// //   final V? value;

// //   const CustomDropDownFormField({
// //     Key? key,
// //     required this.items,
// //     required this.onChanged,
// //     this.value,
// //   }) : super(key: key);

// //   @override
// //   State<CustomDropDownFormField<T, V>> createState() =>
// //       _CustomDropDownFormFieldState<T, V>();
// // }

// // class _CustomDropDownFormFieldState<T, V>
// //     extends State<CustomDropDownFormField<T, V>> {
// //   V? _value;

// //   @override
// //   void initState() {
// //     _value = widget.value;
// //     super.initState();
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return Container(
// //       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
// //       width: double.maxFinite,
// //       child: DropdownButtonFormField<V>(
// //         selectedItemBuilder: (context) {
// //           return widget.items
// //               .map(
// //                 (e) => ListTile(
// //                   title: Text((e as Text).data ?? ''),
// //                 ),
// //               )
// //               .toList();
// //         },
// //         value: _value,
// //         alignment: const Alignment(0, 0),
// //         decoration: InputDecoration(
// //           focusedBorder: OutlineInputBorder(
// //             borderRadius: BorderRadius.circular(8),
// //             borderSide: BorderSide(
// //               color: Get.theme.dividerColor,
// //               width: 1,
// //             ),
// //           ),
// //           border: OutlineInputBorder(
// //             borderRadius: BorderRadius.circular(8),
// //             borderSide: BorderSide(
// //               color: Get.theme.dividerColor,
// //               width: 1,
// //             ),
// //           ),
// //           contentPadding:
// //               const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
// //           labelText: '',
// //         ),
// //         isExpanded: true,
// //         items: widget.items ,
// //         onChanged: (value) {
// //           setState(() {
// //             _value = value;
// //             widget.onChanged(value);
// //           });
// //         },
// //       ),
// //     );
// //   }
// // }

// import 'package:flutter/material.dart';

// class CustomDropDownFormField<T> extends FormField<T> {
//   final List<DropdownMenuItem<T>> items;
//   final T? value;
//   final void Function(T?)? onSaved;
//   final void Function(T?)? onChanged;
//   final String? hintText;
//   final String? titleText;
//   final double marginStart;
//   final double marginTop;
//   final double marginBottom;
//   final String? Function(T?)? validator;
//   final Widget? prefixIcon;
//   const CustomDropDownFormField({
//     Key? key,
//     required this.items,
//     this.value,
//     this.onSaved,
//     this.hintText,
//     this.titleText,
//     this.marginStart = 13,
//     this.marginTop = 16,
//     this.marginBottom = 6,
//     this.validator,
//     this.prefixIcon,
//     this.onChanged,
//   });

//   @override
//   Widget build(BuildContext context) {
//     Widget dropDownBottonFormFiled = DropdownButtonFormField<T>(
//       value: value,
//       onChanged: (value) {
//         if (onChanged != null) onChanged!(value);
//       },
//       onSaved: onSaved,
//       validator: validator,
//       decoration: InputDecoration(prefixIcon: prefixIcon),
//       items: items,
//       hint: hintText != null ? Text(hintText!) : Container(),
//     );
//     return titleText != null
//         ? Column(
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Container(
//                 margin: EdgeInsetsDirectional.only(
//                     start: marginStart, bottom: marginBottom, top: marginTop),
//                 child: Text(titleText!),
//               ),
//               dropDownBottonFormFiled
//             ],
//           )
//         : dropDownBottonFormFiled;
//   }
// }

import 'package:flutter/material.dart';

class CustomDropdown<T> extends StatefulWidget {
  /// the child widget for the button, this will be ignored if text is supplied
  final Widget child;

  /// onChange is called when the selected option is changed.;
  /// It will pass back the value and the index of the option.
  final void Function(T, int)? onChange;

  /// list of DropdownItems
  final List<DropdownItem<T>> items;
  // final DropdownStyle dropdownStyle;

  /// dropdownButtonStyles passes styles to OutlineButton.styleFrom()
  // final DropdownButtonStyle dropdownButtonStyle;

  /// dropdown button icon defaults to caret
  final Icon? icon;
  final bool hideIcon;

  /// if true the dropdown icon will as a leading icon, default to false
  final bool leadingIcon;
  const CustomDropdown({
    Key? key,
    this.hideIcon = false,
    required this.child,
    required this.items,
    // this.dropdownStyle = const DropdownStyle(),
    // this.dropdownButtonStyle = const DropdownButtonStyle(),
    this.icon,
    this.leadingIcon = false,
    this.onChange,
  }) : super(key: key);

  @override
  _CustomDropdownState<T> createState() => _CustomDropdownState<T>();
}

class _CustomDropdownState<T> extends State<CustomDropdown<T>>
    with TickerProviderStateMixin {
  final LayerLink _layerLink = LayerLink();
  late OverlayEntry _overlayEntry;
  bool _isOpen = false;
  int _currentIndex = -1;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();

    _animationController =
        AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _rotateAnimation = Tween(begin: 0.0, end: 0.5).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    // var style = widget.dropdownButtonStyle;
    // link the overlay to the button
    return CompositedTransformTarget(
      link: this._layerLink,
      child: Container(
        // width: style.width,
        // height: style.height,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
              // padding: style.padding,
              // backgroundColor: style.backgroundColor,
              // elevation: style.elevation,
              // primary: style.primaryColor,
              // shape: style.shape,
              ),
          onPressed: _toggleDropdown,
          child: Row(
            // mainAxisAlignment:
            //     style.mainAxisAlignment ?? MainAxisAlignment.center,
            textDirection:
                widget.leadingIcon ? TextDirection.rtl : TextDirection.ltr,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_currentIndex == -1) ...[
                widget.child,
              ] else ...[
                widget.items[_currentIndex],
              ],
              if (!widget.hideIcon)
                RotationTransition(
                  turns: _rotateAnimation,
                  child: widget.icon ?? const Icon(Icons.abc_rounded),
                ),
            ],
          ),
        ),
      ),
    );
  }

  OverlayEntry _createOverlayEntry() {
    // find the size and position of the current widget
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;

    var offset = renderBox.localToGlobal(Offset.zero);
    var topOffset = offset.dy + size.height + 5;
    return OverlayEntry(
      // full screen GestureDetector to register when a
      // user has clicked away from the dropdown
      builder: (context) => GestureDetector(
        onTap: () => _toggleDropdown(close: true),
        behavior: HitTestBehavior.translucent,
        // full screen container to register taps anywhere and close drop down
        child: SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: Stack(
            children: [
              Positioned(
                left: offset.dx,
                top: topOffset,
                // width: widget.dropdownStyle.width ?? size.width,
                child: CompositedTransformFollower(
                  // offset:
                  //     widget.dropdownStyle.offset ?? Offset(0, size.height + 5),
                  link: this._layerLink,
                  showWhenUnlinked: false,
                  child: Material(
                    // elevation: widget.dropdownStyle.elevation ?? 0,
                    // borderRadius:
                    //     widget.dropdownStyle.borderRadius ?? BorderRadius.zero,
                    // color: widget.dropdownStyle.color,
                    child: SizeTransition(
                      axisAlignment: 1,
                      sizeFactor: _expandAnimation,
                      child: ConstrainedBox(
                        constraints:
                            //  widget.dropdownStyle.constraints ??
                            BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height -
                              topOffset -
                              15,
                        ),
                        child: ListView.builder(
                            // padding:
                            //     widget.dropdownStyle.padding ?? EdgeInsets.zero,

                            shrinkWrap: true,
                            itemCount: widget.items.length,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  setState(() => _currentIndex = index);
                                  widget.onChange!(
                                      widget.items[index].value, index);
                                  _toggleDropdown();
                                },
                                child: widget.items[index],
                              );
                            }),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleDropdown({bool close = false}) async {
    if (_isOpen || close) {
      await _animationController.reverse();
      this._overlayEntry.remove();
      setState(() {
        _isOpen = false;
      });
    } else {
      this._overlayEntry = this._createOverlayEntry();
      Overlay.of(context).insert(this._overlayEntry);
      setState(() => _isOpen = true);
      _animationController.forward();
    }
  }
}

/// DropdownItem is just a wrapper for each child in the dropdown list.\n
/// It holds the value of the item.
class DropdownItem<T> extends StatelessWidget {
  final T value;
  final Widget child;

  const DropdownItem({Key? key, required this.value, required this.child})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return child;
  }
}

class DropdownButtonStyle {
  final MainAxisAlignment mainAxisAlignment;
  final ShapeBorder shape;
  final double elevation;
  final Color backgroundColor;
  final EdgeInsets padding;
  final BoxConstraints constraints;
  final double width;
  final double height;
  final Color primaryColor;
  const DropdownButtonStyle({
    required this.mainAxisAlignment,
    required this.backgroundColor,
    required this.primaryColor,
    required this.constraints,
    required this.height,
    required this.width,
    required this.elevation,
    required this.padding,
    required this.shape,
  });
}

class DropdownStyle {
  final BorderRadius borderRadius;
  final double elevation;
  final Color color;
  final EdgeInsets padding;
  final BoxConstraints constraints;

  /// position of the top left of the dropdown relative to the top left of the button
  final Offset offset;

  ///button width must be set for this to take effect
  final double width;

  const DropdownStyle({
    required this.constraints,
    required this.offset,
    required this.width,
    required this.elevation,
    required this.color,
    required this.padding,
    required this.borderRadius,
  });
}
