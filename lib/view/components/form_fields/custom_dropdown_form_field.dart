// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:tadars/utils/quran_ui_icons2.dart';

// class CustomDropDownFormField<T, V> extends StatefulWidget {
//   final List<T> items;
//   final void Function(V?) onChanged;
//   final V? value;

//   const CustomDropDownFormField({
//     Key? key,
//     required this.items,
//     required this.onChanged,
//     this.value,
//   }) : super(key: key);

//   @override
//   State<CustomDropDownFormField<T, V>> createState() =>
//       _CustomDropDownFormFieldState<T, V>();
// }

// class _CustomDropDownFormFieldState<T, V>
//     extends State<CustomDropDownFormField<T, V>> {
//   V? _value;

//   @override
//   void initState() {
//     _value = widget.value;
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       width: double.maxFinite,
//       child: DropdownButtonFormField<V>(
//         selectedItemBuilder: (context) {
//           return widget.items
//               .map(
//                 (e) => ListTile(
//                   title: Text((e as Text).data ?? ''),
//                 ),
//               )
//               .toList();
//         },
//         value: _value,
//         alignment: const Alignment(0, 0),
//         decoration: InputDecoration(
//           focusedBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(8),
//             borderSide: BorderSide(
//               color: Get.theme.dividerColor,
//               width: 1,
//             ),
//           ),
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(8),
//             borderSide: BorderSide(
//               color: Get.theme.dividerColor,
//               width: 1,
//             ),
//           ),
//           contentPadding:
//               const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
//           labelText: '',
//         ),
//         isExpanded: true,
//         items: widget.items ,
//         onChanged: (value) {
//           setState(() {
//             _value = value;
//             widget.onChanged(value);
//           });
//         },
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';

class CustomDropDownFormField<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final T? value;
  final void Function(T?)? onSaved;
  final void Function(T?)? onChanged;
  final String? hintText;
  final String? titleText;
  final double marginStart;
  final double marginTop;
  final double marginBottom;
  final String? Function(T?)? validator;
  final Widget? prefixIcon;
  final bool isExpanded;
  final TextStyle? style;
  const CustomDropDownFormField({
    Key? key,
    required this.items,
    this.value,
    this.onSaved,
    this.hintText,
    this.titleText,
    this.marginStart = 13,
    this.marginTop = 16,
    this.marginBottom = 6,
    this.validator,
    this.prefixIcon,
    this.onChanged,
    this.isExpanded = false,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget dropDownBottonFormFiled = DropdownButtonFormField<T>(
      value: value,
      isExpanded: isExpanded,
      style: style,
      onChanged: (value) {
        if (onChanged != null) onChanged!(value);
      },
      onSaved: onSaved,
      validator: validator,
      decoration: InputDecoration(prefixIcon: prefixIcon),
      items: items,
      hint: hintText != null ? Text(hintText!) : Container(),
    );
    return titleText != null
        ? Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsetsDirectional.only(
                    start: marginStart, bottom: marginBottom, top: marginTop),
                child: Text(titleText!),
              ),
              dropDownBottonFormFiled
            ],
          )
        : dropDownBottonFormFiled;
  }
}
