import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:tadars/view/components/buttons/custom_outlined_button.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';

import '../../../utils/quran_ui_icons.dart';

class ColorPickerFormField extends StatelessWidget {
  const ColorPickerFormField({
    super.key,
    required this.colors,
    this.onSaved,
    this.validator,
    this.margin,
    this.onChanged,
    required this.initialValue,
  });
  final List<Color> colors;
  final EdgeInsetsGeometry? margin;
  final void Function(Color?)? onSaved;
  final void Function(Color?)? onChanged;
  final String Function(Color?)? validator;
  final Color initialValue;

  @override
  Widget build(BuildContext context) {
    return FormField<Color>(
      onSaved: onSaved,
      validator: validator,
      initialValue: initialValue,
      builder: (FormFieldState<Color> field) {
        return Container(
          margin: margin,
          width: double.maxFinite,
          child: Row(
            children: List<Widget>.generate(colors.length, (index) {
                  bool isSelected = colors[index] == field.value &&
                      !HomeController.to.isCustomColor.value;
              return Expanded(
                child: Container(
                  margin: const EdgeInsetsDirectional.only(end: 10),
                  child: InkWell(
                    onTap: () {
                      field.didChange(colors[index]);
                      if (onChanged != null) onChanged!(colors[index]);
                    },
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: colors[index].withOpacity(0.6),
                              
                              borderRadius: CommonStyles.borderRadius,
                              border: isSelected
                                  ? Border.all(
                                      width: 2, color: CustomColors.accentColor)
                                  : Border.all(
                                      width: 1, color: Colors.grey.shade300),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
                }) +
                [
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(end: 10),
                      child: InkWell(
                        onTap: () {
                          Get.back();
                          Get.bottomSheet(
                            const CustomizeColorBottomsheet(),
                          );
                        },
                        child: AspectRatio(
                          aspectRatio: 1,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: double.maxFinite,
                                height: double.maxFinite,
                                decoration: BoxDecoration(
                                  // color: ,
                                  borderRadius: CommonStyles.borderRadius,
                                  border: HomeController.to.isCustomColor.value
                                      ? Border.all(
                                          width: 2,
                                          color: CustomColors.accentColor)
                                      : Border.all(
                                    width: 1,
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Icon(
                                  QuranUIIcons.settings,
                                  color: Get.theme.primaryColor,
                                  size: 18,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                ],
          ),
        );
      },
    );
  }
}

class CustomizeColorBottomsheet extends StatefulWidget {
  const CustomizeColorBottomsheet({
    super.key,
  });

  @override
  State<CustomizeColorBottomsheet> createState() =>
      _CustomizeColorBottomsheetState();
}

class _CustomizeColorBottomsheetState extends State<CustomizeColorBottomsheet> {
  Color textColor = HomeController.to.customTextColor.value ?? Colors.black;
  Color backgroundColor =
      HomeController.to.customBackgroundColor.value ?? Colors.white;
  @override
  Widget build(BuildContext context) {
    Future<Color> showColorPicker(Color selectedColor) async {

      return await showColorPickerDialog(
        context,
        selectedColor,
        pickersEnabled: const <ColorPickerType, bool>{
          ColorPickerType.both: false,
          ColorPickerType.primary: true,
          ColorPickerType.accent: true,
          ColorPickerType.bw: false,
          ColorPickerType.custom: false,
          ColorPickerType.wheel: true
        },
        pickerTypeLabels: const <ColorPickerType, String>{
          ColorPickerType.primary: 'اساسي',
          ColorPickerType.accent: 'ثانوي',
          ColorPickerType.wheel: 'عجلة الالوان'
        },
        title: const Text('اختر لون الخط'),
      );
    }

    return SafeArea(
      child: CustomBottomSheet(
        body: Column(
          children: [
            ListTile(
              onTap: () async {
                Color color = await showColorPicker(textColor);
                setState(() {
                  textColor = color;
                });
                
              },
              title: const Text('لون الخط'),
              trailing: ColorIndicator(
                width: 20,
                height: 20,
                borderRadius: 4,
                color: textColor,
                borderColor: Colors.grey,
                hasBorder: true,
              ),
            ),
            const Divider(
              height: 1,
            ),
            ListTile(
              title: const Text('لون الخلفية'),
              onTap: () async {
                Color color = await showColorPicker(backgroundColor);
                setState(() {
                  backgroundColor = color;
                });
              },
              trailing: ColorIndicator(
                width: 20,
                height: 20,
                borderRadius: 4,
                color: backgroundColor,
                borderColor: Colors.grey,
                hasBorder: true,
              ),
            ),
            const Divider(
              height: 1,
            ),
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: CustomOutlinedButton(
                      fontSize: 16,
                      color: CustomColors.primaryColor,
                      text: 'الغاء الامر',
                      onPressed: () {
                        Get.back();
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomFilledButton(
                      color: CustomColors.primaryColor,
                      fontSize: 16,
                      text: 'تطبيق الالوان',
                      onPressed: () {
                        HomeController.to.setCustomColor(
                          backgroundColor,
                          textColor,
                        );
                        Get.back();
                      },
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        title: 'تخصيص الالوان',
      ),
    );
  }
}
