import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextFormField extends StatefulWidget {
  final void Function(String?)? onSave;
  final String? Function(String?)? validator;
  final void Function(String?)? onChanged;
  final int? maxLine;

  final Widget? lable;

  final Widget? prefixIcon;

  final String? hintText;
  final String? titleText;
  final double marginStart;
  final double marginTop;
  final double marginBottom;
  final bool obscureText;
  final bool isDigital;
  final TextInputType keyboardType;
  final int? maxLength;
  final int? minLines;
  final int? initialValue;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final bool readOnly;
  const CustomTextFormField({
    Key? key,
    this.onSave,
    this.hintText,
    this.maxLine,
    this.lable,
    this.prefixIcon,
    this.titleText,
    this.marginStart = 13.0,
    this.marginTop = 16.0,
    this.marginBottom = 6.0,
    this.obscureText = false,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.isDigital = false,
    this.maxLength,
    this.initialValue,
    this.minLines,
    this.suffixIcon,
    this.controller,
    this.readOnly = false,
    this.onChanged,
  }) : super(key: key);

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool _obscureText = false;

  late int number;

  TextEditingController? controller;

  @override
  void initState() {
    _obscureText = widget.obscureText;
    number = widget.initialValue ?? 0;
    controller = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Widget textFormField = Container(
        child: TextFormField(
      maxLines: widget.maxLine,

      controller: widget.controller, readOnly: widget.readOnly,
      onSaved: widget.onSave,
      onChanged: widget.onChanged,
      initialValue: widget.initialValue.toString(),
      validator: widget.validator,
      obscureText: _obscureText,
      inputFormatters: widget.isDigital
          ? <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly]
          : [],
      keyboardType: widget.keyboardType,
      maxLength: widget.maxLength,
      // textInputAction: TextInputAction.next,
      minLines: widget.minLines,
      obscuringCharacter: "*",
      decoration: InputDecoration(
        hintText: widget.hintText,
        label: widget.lable,
        prefixIcon: widget.prefixIcon,
        focusColor: (widget.readOnly) ? Colors.grey : null,
        suffixIcon: const Column(
          children: [
            Icon(
              Icons.arrow_drop_down,
              size: 15,
            ),
            SizedBox(
              height: 3,
            ),
            Icon(
              Icons.arrow_upward,
              size: 15,
            ),
          ],
        ),
      ),
    ));

    return widget.titleText != null
        ? Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  margin: EdgeInsetsDirectional.only(
                      start: widget.marginStart,
                      top: widget.marginTop,
                      bottom: widget.marginBottom),
                  child: Text(widget.titleText!)),
              textFormField
            ],
          )
        : textFormField;
  }
}
