import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/constants/common_constants.dart';

class CustomOutlinedButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final Color? textColor;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final IconData? icon;
  final double? fontSize;
  const CustomOutlinedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.color,
    this.textColor,
    this.margin,
    this.padding,
    this.textStyle,
    this.icon,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Material(
        borderRadius: BorderRadius.circular(10),
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(10),
          onTap: onPressed,
          child: Container(
            padding: padding ??
                const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: color ?? Get.theme.primaryColor,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                DefaultTextStyle(
                  style: TextStyle(
                    color: color ?? Get.theme.primaryColor,
                    fontSize: fontSize ?? 12,
                    fontFamily: CommonConstants.samimFontFamily,
                  ),
                  child: Row(
                    children: [
                      if (icon != null)
                        Icon(
                          icon,
                          color: textColor ?? Get.theme.primaryColor,
                          size: 16,
                        ),
                      Text(
                        text,
                        style: textStyle ??
                            TextStyle(
                              color: textColor ?? Get.theme.primaryColor,
                              fontSize: fontSize ?? 12,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
