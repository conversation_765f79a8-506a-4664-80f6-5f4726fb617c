import 'package:flutter/material.dart';

import '../../../utils/constants/common_constants.dart';

class CustomFilledButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final Color? textColor;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final IconData? icon;
  final double? fontSize;
  final bool fullWidth;
  const CustomFilledButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.color,
    this.textColor,
    this.margin,
    this.padding,
    this.textStyle,
    this.icon,
    this.fontSize,
    this.fullWidth = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: fullWidth ? double.maxFinite : null,
      child: Material(
        borderRadius: BorderRadius.circular(10),
        color: color ?? Theme.of(context).primaryColor,
        child: InkWell(
          borderRadius: BorderRadius.circular(10),
          onTap: onPressed,
          child: Padding(
            padding: padding ??
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                DefaultTextStyle(
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: fontSize ?? 16,
                    fontFamily: CommonConstants.samimFontFamily,
                  ),
                  child: Row(
                    children: [
                      if (icon != null)
                        Padding(
                          padding: const EdgeInsetsDirectional.only(end: 8.0),
                          child: Icon(
                            icon,
                            color: textColor ?? Colors.white,
                            size: 16,
                          ),
                        ),
                      Text(
                        text,
                        style: textStyle ??
                            TextStyle(
                              color: textColor ?? Colors.white,
                              fontSize: fontSize ?? 12,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
