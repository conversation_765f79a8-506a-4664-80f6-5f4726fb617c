import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

import '../../../utils/common_functions.dart';
import '../../../utils/common_styles.dart';
import '../buttons/cutom_filled_button.dart';
import 'link_preview.dart';

class LinksView extends StatelessWidget {
  final List<Map<String, dynamic>> links;
  const LinksView({
    Key? key,
    required this.links,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return links.isEmpty
        ? const SizedBox()
        : Column(
            children: [
              SizedBox(
                width: double.maxFinite,
                child: Text(
                  "روابط ذات صلة:".tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: HomeController.to.pageColor.value == Colors.white
                        ? Get.theme.primaryColor
                        : HomeController.to.pageColor.value,
                  ),
                ),
              ),
              SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: links.map((link) {
                    bool isVideo = false;
                    bool isUrl = false;
                    String url = '';
                    // if (link['tdbr_id'] == 48158) {
                    print('url: ' + link['url']);
                    // }
                    print(link['url']);
                    if (CommonFunctions.isValidYoutubeUrl(link['url'])) {
                      isVideo = true;
                    } else if (CommonFunctions.isValidUrl(link['url']) &&
                        !CommonFunctions.isValidImageUrl(link['url'])) {
                      isUrl = true;
                      url = link['url'];
                    } else {
                      url = link['url'];
                    }
                    return Container(
                      margin: const EdgeInsets.only(top: 8),
                      width: double.maxFinite,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Get.isDarkMode
                            ? Get.theme.primaryColor.withOpacity(0.8)
                            : HomeController.to.pageColor.value == Colors.white
                            ? Get.theme.primaryColor.withOpacity(0.2)
                            : HomeController.to.pageColor.value
                                ?.withOpacity(0.2),
                        borderRadius: CommonStyles.borderRadius,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomFilledButton(
                            color: HomeController.to.pageColor.value ==
                                        Colors.white ||
                                    Get.isDarkMode
                                ? CustomColors.primaryColor
                                : HomeController.to.pageColor.value,
                            text: isVideo
                                      ? 'عرض مقطع الفيديو'.tr
                                : isUrl
                                      ? 'عرض الرابط'.tr
                                      : 'عرض الصورة'.tr,
                            // icon: isVideo ? Icons.video_file : Icons.image,
                            onPressed: () {
                              if (isUrl) {
                                CommonFunctions.launchURL(url);
                              } else {
                                Get.dialog(
                                    LinkPreview(
                                      url: link['url'],
                                      startAt: link['start_at'] ?? "",
                                      endAt: link['end_at'] ?? "",
                                      tdbrId: link['tdbr_id'],
                                      tdbrType: link['tdbr_type'],
                                    ),
                                    barrierDismissible: false);
                              }
                            },
                          ),
                          if (link['details']?.isNotEmpty ?? false)
                            Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Text(link['details'] ?? "",
                                  style: const TextStyle(
                                    fontSize: 12,
                                  )),
                            ),
                          if (isVideo)
                            Container(
                              padding: const EdgeInsets.all(4),
                              margin: const EdgeInsets.only(top: 4),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: CommonStyles.borderRadius,
                              ),
                              child: Text(
                                "من: ".tr +
                                    ((link['start_at']?.isEmpty ?? true)
                                        ? "بداية المقطع"
                                        : link['start_at']) +
                                    " إلى: ".tr +
                                    (link['end_at']?.isEmpty ?? true
                                        ? "نهاية المقطع"
                                        : link['end_at']),
                                style: TextStyle(
                                  fontSize: 9,
                                  color: HomeController.to.pageColor.value ==
                                              Colors.white ||
                                          Get.isDarkMode
                                      ? CustomColors.primaryColor
                                      : HomeController.to.pageColor.value,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              )
            ],
          );
  }
}
