import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutter_cache_manager/src/cache_managers/default_cache_manager.dart';

import '../../../utils/common_functions.dart';
import '../../../utils/constants/configs.dart';

class LinkPreview extends StatelessWidget {
  final String url;
  final String startAt;
  final String endAt;
  final String tdbrType;
  final int tdbrId;
  const LinkPreview(
      {Key? key,
      required this.url,
      required this.startAt,
      required this.endAt,
      required this.tdbrType,
      required this.tdbrId})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isVideo = false;
    String url = this.url;
    String? videoId;
    if (CommonFunctions.isValidYoutubeUrl(url)) {
      videoId = CommonFunctions.convertUrlToId(url);
      url = "https://i1.ytimg.com/vi/$videoId/default.jpg";
      isVideo = true;
    } else {
      if (!CommonFunctions.isValidUrl(url)) {
        url = Configs.baseUrl + url;
      }
    }
    return isVideo
        ? YoutubePlayerBuilder(
            builder: (BuildContext context, Widget player) {
              return Align(
                alignment: Alignment.center,
                child: Material(
                  color: Colors.transparent,
                  child: SingleChildScrollView(
                      child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: () {
                          Get.back();
                        },
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9),
                          color: Colors.white,
                        ),
                        width: double.maxFinite,
                        child: Center(
                          child: player,
                        ),
                      ),
                    ],
                  )),
                ),
              );
            },
            player: YoutubePlayer(
              controller: YoutubePlayerController(
                initialVideoId: videoId ?? "",
                flags: YoutubePlayerFlags(
                  autoPlay: true,
                  mute: false,
                  startAt: startAt.isEmpty
                      ? 0
                      : CommonFunctions.parseDuration(startAt).inSeconds,
                  endAt: endAt.isEmpty
                      ? null
                      : CommonFunctions.parseDuration(endAt).inSeconds,
                ),
              ),
            ),
          )
        : Align(
            alignment: Alignment.center,
            child: Center(
              child: SingleChildScrollView(
                child: Material(
                  color: Colors.transparent,
                  child: InteractiveViewer(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            Get.back();
                          },
                          icon: const Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                        ),
                        CachedNetworkImage(
                          imageUrl: url,
                          progressIndicatorBuilder: (context, url, progress) {
                            return Container(
                              color: Colors.white,
                              height: 300,
                              child: const Center(
                                  child: CircularProgressIndicator()),
                            );
                          },
                        ),
                        SizedBox(
                          width: double.maxFinite,
                          child: CustomFilledButton(
                            color: CustomColors.primaryColor,
                            margin: const EdgeInsets.only(
                              top: 16,
                            ),
                            fontSize: 16,
                            padding: const EdgeInsets.all(8),
                            text: 'مشاركة'.tr,
                            onPressed: () async {
                              var file = await DefaultCacheManager()
                                  .getSingleFile(url);
                              Share.shareXFiles([XFile(file.path)]);
                              CommonFunctions.shareItem(tdbrId, tdbrType);
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
  }
}
