import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/home_controller.dart';
import 'package:tadars/custom_packages/flutter_html/lib/flutter_html.dart';

class TadaborHtmlView extends StatelessWidget {
  const TadaborHtmlView({
    Key? key,
    required this.htmlText,
  }) : super(key: key);

  final String htmlText;

  @override
  Widget build(BuildContext context) {
    return Html(
      data: htmlText,
      style: {
        "*": Style(
          fontSize: FontSize(HomeController.to.pageFontSize.value * 0.8,
              units: "rem"),
          lineHeight: const LineHeight(1.5),
        ),
        "table": Style(
          border: Border.all(
            color: Get.theme.primaryColor,
            width: 0.2,
          ),
        ),
        "tr": Style(
          width: double.maxFinite,
          border: Border.all(
            color: Get.theme.primaryColor,
            width: 0.2,
          ),
        ),
        "tr:first-child": Style(
          width: double.maxFinite,
          border: Border.all(
            color: Get.theme.primaryColor,
            width: 0.2,
          ),
        ),
        "td:first-child": Style(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.topLeft,
        ),
        "td:nth-child(1)": Style(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.topRight,
        ),
      },
    );
  }
}
