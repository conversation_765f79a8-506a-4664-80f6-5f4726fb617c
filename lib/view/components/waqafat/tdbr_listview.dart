import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/view/components/waqafat/tdbr_view.dart';

class TdbrListView extends StatefulWidget {
  final TdbrType tdbrType;
  const TdbrListView({super.key, required this.tdbrType});

  @override
  State<TdbrListView> createState() => _TdbrListViewState();
}

class _TdbrListViewState extends State<TdbrListView> {
  int page = 1;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: SqlHelper.getAllTdbrs(widget.tdbrType, page),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text(snapshot.error.toString()));
        }
        var tdbrs = snapshot.data?['tdbrs'] ?? [];
        var count = snapshot.data?['count'] ?? 0;
        var pagesCount = snapshot.data?['pages_count'] ?? 0;

        return Column(
          children: [
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: tdbrs.length,
                itemBuilder: (BuildContext context, int index) {
                  return TdbrView(
                    tdbr: tdbrs[index],
                    tdbrType: widget.tdbrType,
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: Get.theme.primaryColor,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(10),
                ),
              ),
              width: double.maxFinite,
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        'إجمالي الوقفات: '.tr + count.toString(),
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        'عدد الصفحات: '.tr + pagesCount.toString(),
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          if (page > 1) {
                            setState(() {
                              page--;
                            });
                          }
                        },
                        child: const Text(
                          '< السابق',
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Builder(
                              builder: (context) {
                                var pageKey = GlobalKey();
                                // ensure visible
                                // SchedulerBinding.instance
                                //     .addPostFrameCallback((_) {
                                //   var cntx = pageKey.currentContext;

                                //   if (cntx != null) {
                                //     Scrollable.ensureVisible(
                                //       cntx,
                                //       alignment: 0.5,
                                //       duration:
                                //           const Duration(milliseconds: 400),
                                //       curve: Curves.easeInOut,
                                //     );
                                //   }
                                // });
                                return Row(
                                  children: List.generate(pagesCount, (index) {
                                    return InkWell(
                                      key: page == index + 1 ? pageKey : null,
                                      onTap: () {
                                        setState(() {
                                          page = index + 1;
                                        });
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color:
                                              page == index + 1
                                                  ? Colors.white
                                                  : Colors.white54,
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                        margin: const EdgeInsets.all(2.0),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 4,
                                          vertical: 2,
                                        ),
                                        child: Text(
                                          (index + 1).toString(),
                                          textAlign: TextAlign.center,
                                          maxLines: 1,
                                          style: TextStyle(
                                            fontSize: 9,
                                            height: 0,
                                            color: Get.theme.primaryColor,
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          if (page < pagesCount) {
                            setState(() {
                              page++;
                            });
                          }
                        },
                        child: const Text(
                          'التالي >',
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
