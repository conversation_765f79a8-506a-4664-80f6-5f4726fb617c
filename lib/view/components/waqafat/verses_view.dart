import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/common_styles.dart';

import '../../../controllers/home_controller.dart';
import '../../../helpers/boxes_helper.dart';
import '../../../utils/constants/common_constants.dart';
import '../../pages/home/<USER>/verse_marker.dart';

class VersesView extends StatelessWidget {
  final List<Map<String, dynamic>> verses;
  const VersesView({
    super.key,
    required this.verses,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: verses
              .map<Widget>(
                (e) => Container(
                  margin: const EdgeInsets.only(bottom: 4),
                  decoration: BoxDecoration(
                    color: Get.theme.primaryColor.withOpacity(0.1),
                    borderRadius: CommonStyles.borderRadius,
                  ),
                  padding: const EdgeInsets.all(4),
                  child: Column(
                    children: [
                      Text.rich(
                        TextSpan(
                          text: e['ayah_num'] == 0
                              ? "${'وقفات سورة'.tr} ${BoxesHelper.getSuraById(e['sora_num'] ?? 0)?.name ?? ''}"
                              : BoxesHelper.getVerse(e['sora_num'] ?? 0,
                                          e['ayah_num'] ?? 0)
                                      ?.verseWithDiac ??
                                  "",
                          children: e['ayah_num'] == 0
                              ? []
                              : [
                                  WidgetSpan(
                                      child: Text(
                                          ' [${BoxesHelper.getSuraById(e['sora_num'] ?? 0)?.nameWithDiac ?? ""} - ${e['ayah_num']}]')),
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: VerseMarker(
                                      size:
                                          HomeController.to.pageFontSize.value *
                                              1.2,
                                      verseNumber: e['ayah_num']?.toInt() ?? 0,
                                      suraNumber: e['sora_num']?.toInt() ?? 0,
                                    ),
                                  ),
                                ],
                        ),
                        style: TextStyle(
                          fontSize: HomeController.to.pageFontSize.value,
                          fontFamily: CommonConstants.hafsFontFamily,
                        ),
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ),
              )
              .toList() +
          <Widget>[const Divider()],
    );
  }
}
