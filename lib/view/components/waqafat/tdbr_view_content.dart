// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:shimmer/shimmer.dart';
// import 'package:tadars/enums/tdbr_type.dart';
// import 'package:tadars/helpers/boxes_helper.dart';
// import 'package:tadars/utils/constants/tadars_constants.dart';

// import '../../../../../controllers/tadars_controller.dart';
// import '../../../../../utils/common_styles.dart';
// import '../../../../../utils/constants/custom_colors.dart';
// import '../../../../components/buttons/custom_outlined_button.dart';
// import '../../../../components/buttons/cutom_filled_button.dart';
// import '../../../../components/dialogs/tadars_dialog.dart';
// import '../../../../components/form_fields/custom_text_form_field.dart';
// import '../../../books/components/quran_book_detail_dialog.dart';
// import 'links_view.dart';
// import 'video_preview.dart';

// class TdbrViewContent extends StatelessWidget {
//   const TdbrViewContent({
//     Key? key,
//     required this.verseNumber,
//     required this.suraNumber,
//     required this.instatnce,
//     required this.type,
//   }) : super(key: key);

//   final int verseNumber;
//   final int suraNumber;
//   final Future<dynamic>? instatnce;
//   final TdbrType type;

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<dynamic>(
//         future: instatnce,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return Container(
//                 padding: const EdgeInsets.all(16),
//                 width: double.maxFinite,
//                 margin: const EdgeInsets.symmetric(horizontal: 8),
//                 decoration: BoxDecoration(
//                   color: Get.theme.primaryColor.withOpacity(0.2),
//                   borderRadius: CommonStyles.borderRadius,
//                 ),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Shimmer.fromColors(
//                       baseColor: Colors.white,
//                       highlightColor: Colors.white54,
//                       direction: ShimmerDirection.rtl,
//                       // enabled: false,

//                       // period: const Duration(seconds: 3),
//                       child: Container(
//                         decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.circular(20)),
//                         height: 20,
//                         width: double.maxFinite,
//                       ),
//                     ),
//                   ],
//                 ));
//           }

//           // if (!snapshot.hasData) {
//           //   return Container(
//           //       padding: const EdgeInsets.all(16),
//           //       width: double.maxFinite,
//           //       margin: const EdgeInsets.symmetric(horizontal: 8),
//           //       decoration: BoxDecoration(
//           //         color: Get.theme.primaryColor.withOpacity(0.2),
//           //         borderRadius: CommonStyles.borderRadius,
//           //       ),
//           //       child: const Text(
//           //         "لايوجد تدبر لهذه الأية",
//           //         textAlign: TextAlign.center,
//           //       ));
//           // }

//           // return FutureBuilder<dynamic>(future: Future.microtask(() {

//           if (!snapshot.hasData) {
//             return Container(
//                 padding: const EdgeInsets.all(16),
//                 width: double.maxFinite,
//                 margin: const EdgeInsets.symmetric(horizontal: 8),
//                 decoration: BoxDecoration(
//                   color: Get.theme.primaryColor.withOpacity(0.2),
//                   borderRadius: CommonStyles.borderRadius,
//                 ),
//                 child: const Center(
//                   child: Text(
//                     "لايوجد تدبر لهذه الآية",
//                     textAlign: TextAlign.center,
//                   ),
//                 ));
//           }
//           var tdbr = (snapshot.data!
//               .where((el) =>
//                   el.tdbr.verseNumber == verseNumber &&
//                   el.tdbr.suraNumber == suraNumber)
//               .toList());
//           // print(tdbr);

//           // }), builder: (context, snpshot) {
//           //   if (snpshot.connectionState == ConnectionState.waiting) {
//           //     return Container(
//           //         padding: const EdgeInsets.all(16),
//           //         width: double.maxFinite,
//           //         margin: const EdgeInsets.symmetric(horizontal: 8),
//           //         decoration: BoxDecoration(
//           //           color: Get.theme.primaryColor.withOpacity(0.2),
//           //           borderRadius: CommonStyles.borderRadius,
//           //         ),
//           //         child: const Text(
//           //           "يتم جلب البيانات",
//           //           textAlign: TextAlign.center,
//           //         ));
//           //   }

//           return Container(
//             width: double.maxFinite,
//             margin: const EdgeInsets.symmetric(horizontal: 8),
//             child: tdbr.isEmpty
//                 ? Container(
//                     padding:
//                         const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
//                     width: double.maxFinite,
//                     decoration: BoxDecoration(
//                       color: Get.theme.primaryColor.withOpacity(0.2),
//                       borderRadius: CommonStyles.borderRadius,
//                     ),
//                     child: const Center(
//                       child: Text(
//                         "لايوجد تدبر لهذه الآية",
//                         textAlign: TextAlign.center,
//                       ),
//                     ),
//                   )
//                 : Column(
//                     mainAxisSize: MainAxisSize.max,
//                     children: tdbr!.map<Widget>((tdbr) {
//                       // print((tdbr).links.first.toJson());
//                       return Container(
//                         margin: const EdgeInsets.only(bottom: 8),
//                         padding: const EdgeInsets.symmetric(
//                             vertical: 8, horizontal: 16),
//                         width: double.maxFinite,
//                         decoration: BoxDecoration(
//                           color: Get.theme.primaryColor.withOpacity(0.2),
//                           borderRadius: CommonStyles.borderRadius,
//                         ),
//                         child: Column(
//                           children: [
//                             SizedBox(
//                               width: double.maxFinite,
//                               child: TadarsController
//                                           .instance.tdbrIndex.value ==
//                                       6
//                                   ? Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         VideoPreview(
//                                           url: tdbr.tdbr.url,
//                                           startAt: tdbr.tdbr.startAt,
//                                           endAt: tdbr.tdbr.endAt,
//                                         ),
//                                         if (tdbr.tdbr.details?.isNotEmpty ??
//                                             false)
//                                           Padding(
//                                             padding:
//                                                 const EdgeInsets.only(top: 4.0),
//                                             child: Text(tdbr.tdbr.details ?? "",
//                                                 style: const TextStyle(
//                                                   fontSize: 12,
//                                                 )),
//                                           ),
//                                         Container(
//                                           padding: const EdgeInsets.all(4),
//                                           margin: const EdgeInsets.only(top: 4),
//                                           decoration: BoxDecoration(
//                                             color: Colors.white,
//                                             borderRadius:
//                                                 CommonStyles.borderRadius,
//                                           ),
//                                           child: Text(
//                                               "من: ".tr +
//                                                   (tdbr.tdbr.startAt ??
//                                                       "بداية المقطع") +
//                                                   " إلى: ".tr +
//                                                   (tdbr.tdbr.endAt ??
//                                                       "نهاية المقطع"),
//                                               style: TextStyle(
//                                                 fontSize: 9,
//                                                 color: Get.theme.primaryColor,
//                                               )),
//                                         ),
//                                       ],
//                                     )
//                                   : Text(
//                                       tdbr.tdbr.fullText,
//                                     ),
//                             ),
//                             SizedBox(
//                               width: double.maxFinite,
//                               child: Text(
//                                 "المصدر: ".tr + (tdbr.sourceName ?? ""),
//                                 textAlign: TextAlign.left,
//                                 style: TextStyle(
//                                   fontSize: 10,
//                                   color: Get.theme.primaryColor,
//                                 ),
//                               ),
//                             ),
//                             LinksView(
//                               links: tdbr.links,
//                             ),
//                             const Divider(
//                               height: 16,
//                             ),

//                             //to add likes and shares in bottom of tadaboor
//                             Row(
//                               children: [
//                                 Expanded(
//                                   child: TdbrAction(
//                                     selected: BoxesHelper.isTdbrExistInBookmark(
//                                         tdbr.tdbr.id,
//                                         TadarsConstants.tdbrMapping[type] ??
//                                             ""),
//                                     selectedIcon: Icons.star,
//                                     icon: Icons.star_outline_rounded,
//                                     label: 'تفضيل',
//                                     selectedLabel: 'حذف',
//                                     onTap: () {
//                                       if (BoxesHelper.isTdbrExistInBookmark(
//                                           tdbr.tdbr.id,
//                                           TadarsConstants.tdbrMapping[type] ??
//                                               "")) {
//                                         BoxesHelper.removeTdbrFromBookmark(
//                                             tdbr.tdbr.id,
//                                             TadarsConstants.tdbrMapping[type] ??
//                                                 "");
//                                       } else {
//                                         BoxesHelper.addTdbrToBookmark(
//                                             tdbr.tdbr.id,
//                                             TadarsConstants.tdbrMapping[type] ??
//                                                 "");
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 Expanded(
//                                   child: TdbrAction(
//                                     icon: Icons.warning_amber_rounded,
//                                     label: 'إبلاغ',
//                                     onTap: () async {
//                                       SystemChrome.setEnabledSystemUIMode(
//                                           SystemUiMode.manual,
//                                           overlays: []);
//                                       var formKey = GlobalKey<FormState>();
//                                       String reason = "";
//                                       await Get.dialog(
//                                         TadarsDialog(
//                                           title: 'الإبلاغ عن محتوى مخالف!',
//                                           content: Form(
//                                             key: formKey,
//                                             child: Column(
//                                               children: [
//                                                 const Text(
//                                                   'يرجى كتابة توضيح عن سبب تصنيفك لهذه المادة بإنها مخالفة!',
//                                                   textAlign: TextAlign.center,
//                                                 ),
//                                                 Container(
//                                                   margin: const EdgeInsets.only(
//                                                       top: 4),
//                                                   child: CustomTextFormField(
//                                                     // titleText: "السبب".tr,
//                                                     hintText:
//                                                         "اكتب توضيح سبب المخالفة"
//                                                             .tr,
//                                                     maxLine: 2,
//                                                     validator: (value) {
//                                                       return null;
//                                                     },
//                                                     onSave: (value) {
//                                                       reason = value ?? "";
//                                                     },
//                                                   ),
//                                                 ),
//                                                 const SizedBox(
//                                                   height: 16,
//                                                 ),
//                                                 Row(
//                                                   children: [
//                                                     Expanded(
//                                                       child: CustomFilledButton(
//                                                         onPressed: () async {
//                                                           if (formKey
//                                                               .currentState!
//                                                               .validate()) {
//                                                             formKey
//                                                                 .currentState!
//                                                                 .save();
//                                                             await TadarsController
//                                                                 .instance
//                                                                 .reportTdbr(
//                                                                     tdbr.tdbr
//                                                                         .id,
//                                                                     TadarsConstants
//                                                                             .tdbrMappingForDb[type] ??
//                                                                         "",
//                                                                     reason);
//                                                           }
//                                                         },
//                                                         text: "إبلاغ".tr,
//                                                       ),
//                                                     ),
//                                                     const SizedBox(
//                                                       width: 16,
//                                                     ),
//                                                     Expanded(
//                                                         child:
//                                                             CustomOutlinedButton(
//                                                       onPressed: () {
//                                                         Get.back();
//                                                       },
//                                                       text: 'الغاء الأمر',
//                                                     ))
//                                                   ],
//                                                 )
//                                               ],
//                                             ),
//                                           ),
//                                         ),
//                                         barrierDismissible: false,
//                                         useSafeArea: false,
//                                       );
//                                       SystemChrome.setEnabledSystemUIMode(
//                                           SystemUiMode.manual,
//                                           overlays: []);
//                                     },
//                                   ),
//                                 ),
//                                 Expanded(
//                                   child: TdbrAction(
//                                     icon: Icons.share_outlined,
//                                     label: tdbr.shares.toString(),
//                                     onTap: () {
//                                       var versesText = BoxesHelper.getVerse(
//                                                   suraNumber, verseNumber)
//                                               ?.verseWithDiac ??
//                                           "";
//                                       var text = type == TdbrType.media
//                                           ? tdbr.tdbr.details +
//                                               "\n" +
//                                               tdbr.tdbr.url
//                                           : tdbr.tdbr.fullText;
//                                       Share.share("""﴿$versesText﴾ 

// $text

// https://tadars.com/tdbr/${TadarsConstants.tdbrMapping[type]}/${tdbr.tdbr.id}""");
//                                     },
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       );
//                     }).toList(),
//                   ),
//           );
//         });
//   }
// }

