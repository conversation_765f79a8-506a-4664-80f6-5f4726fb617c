import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/common_functions.dart';
import 'link_preview.dart';

class VideoPreview extends StatelessWidget {
  final String url;
  final int tdbrId;
  final String tdbrType;
  const VideoPreview(
      {Key? key,
      required this.url,
      this.startAt,
      this.endAt,
      required this.tdbrId,
      required this.tdbrType})
      : super(key: key);
  final String? startAt;
  final String? endAt;
  @override
  Widget build(BuildContext context) {
    String? videoId;
    videoId = CommonFunctions.convertUrlToId(url);
    print('from video preview $url');
    return InkWell(
      onTap: () {
        Get.dialog(
            LinkPreview(
              url: url,
              startAt: startAt ?? "",
              endAt: endAt ?? "",
              tdbrId: tdbrId,
              tdbrType: tdbrType,
            ),
            barrierDismissible: false);
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(9),
            child: CachedNetworkImage(
              imageUrl: "https://i1.ytimg.com/vi/$videoId/hqdefault.jpg",
              width: double.maxFinite,
              fit: BoxFit.cover,
            ),
          ),
          const Icon(
            Icons.play_arrow_rounded,
            color: Colors.white,
            size: 70,
          ),
        ],
      ),
    );
  }
}
