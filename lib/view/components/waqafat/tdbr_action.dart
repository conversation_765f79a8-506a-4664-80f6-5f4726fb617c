import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';

import '../../../utils/constants/custom_colors.dart';

class TdbrAction extends StatefulWidget {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final String? selectedLabel;
  final bool selected;
  final Function()? onTap;
  const TdbrAction({
    super.key,
    required this.icon,
    required this.label,
    this.onTap,
    this.selectedIcon,
    this.selected = false,
    this.selectedLabel,
  });

  @override
  State<TdbrAction> createState() => _TdbrActionState();
}

class _TdbrActionState extends State<TdbrAction> {
  bool selected = false;
  @override
  void initState() {
    selected = widget.selected;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
          setState(() {
            selected = !selected;
          });
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              selected ? widget.selectedIcon ?? widget.icon : widget.icon,
              color: HomeController.to.pageColor.value == Colors.white
                  ? Get.theme.primaryColor
                  : HomeController.to.pageColor.value,
              size: 18,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              selected ? widget.selectedLabel ?? widget.label : widget.label,
              style: TextStyle(
                color: HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
