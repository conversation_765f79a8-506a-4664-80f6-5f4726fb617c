import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';
import 'package:tadars/view/components/custom_slider/pages_slider.dart';

import '../../controllers/setting_controller.dart';
import '../../services/sync_service.dart';
import '../../utils/constants/boxes.dart';
import '../../utils/constants/custom_colors.dart';
import '../../utils/enums.dart';

class PageSliderWrapper extends StatelessWidget {
  const PageSliderWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = HomeController.to;
    return ValueListenableBuilder(
        valueListenable: Hive.box(Boxes.settings)
            .listenable(keys: [SettingsConstants.navVisibilityKey]),
        builder: (BuildContext context, box, Widget? child) {
          if (!box.get(SettingsConstants.navVisibilityKey,
              defaultValue: true)) {
            return const SizedBox.shrink();
          }

          return OrientationBuilder(builder: (context, orientation) {
            return Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      spreadRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                  color: Get.theme.scaffoldBackgroundColor,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(8),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Obx(() {
                      if (SyncService.instance.isSyncing.value) {
                        return LinearProgressIndicator(
                          backgroundColor: CustomColors.primaryColor,
                          valueColor:
                              const AlwaysStoppedAnimation<Color>(Colors.white),
                        );
                      }
                      return const SizedBox.shrink();
                    }),
                    if (SettingController.instance.quranBook.value.id == 0)
                      Obx(() {
                        return Column(
                          children: [
                            if (controller.viewMode.value == ViewMode.quran)
                              Builder(builder: (context) {
                                return Row(
                                  mainAxisSize: MainAxisSize.max,
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    if (controller.prevVisitedPage.value !=
                                        null)
                                      InkWell(
                                        onTap: () {
                                          debugPrint(
                                              "Go to prev visited Page  ${HomeController.to.prevVisitedPage.value}");
                                          HomeController.to.quranPageController
                                              .jumpToPage(HomeController
                                                  .to.prevVisitedPage.value!);
                                        },
                                        child: Container(
                                          padding:
                                              const EdgeInsetsDirectional.only(
                                                  top: 8, start: 9),
                                          child: Row(
                                            children: [
                                              const Icon(
                                                Icons.arrow_back,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                controller
                                                    .prevVisitedPage.value!
                                                    .toString(),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    const Spacer(),
                                      Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          onTap: () {
                                            controller.quranPageController
                                                .startAutoScroll();
                                            controller.showOverlay.value =
                                                false;
                                            controller.showAutoScrollPagesTimer
                                                    .value =
                                                AutoScrollPagesStatus.active;
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: const BoxDecoration(
                                                shape: BoxShape.circle),
                                            child: Transform.rotate(
                                              angle: pi / 2,
                                              child: Icon(
                                                Icons.double_arrow_outlined,
                                                color: controller
                                                            .pageColor.value ==
                                                        Colors.white
                                                    ? Get.theme.primaryColor
                                                    : HomeController
                                                        .to.pageColor.value,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                );
                              }),
                            PageSlider(
                              onChanged: (page) {
                                //                   if (controller.viewMode.value == ViewMode.quran) {
                                //     return const QuranPageViewWrapper();
                                //   } else if (controller.viewMode.value == ViewMode.tafseer) {
                                //     return TafseerPageView(
                                //       onTap: () {},
                                //     );
                                //   } else {
                                //     return WaqafatPageView(
                                //       onTap: () {},
                                //     );
                                //   }
                                // }
                                // if (controller.viewMode.value ==
                                //     ViewMode.quran) {
                                  controller.quranPageController
                                      .jumpToPage(page.toInt());
                                // } else {
                                //   controller.pageController.value
                                //       .jumpToPage(page.toInt());
                                // } 
                              },
                              max: 604,
                              min: 1,
                              value: HomeController.to.currentPageNumber.value,
                            ),
                            Divider(
                              color: (HomeController.to.pageColor.value ==
                                          Colors.white
                                      ? Get.theme.primaryColor
                                      : HomeController.to.pageColor.value)
                                  ?.withOpacity(0.5),
                              height: 1,
                            ),
                          ],
                        );
                      }),
                    SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: Obx(
                                () => Text(
                                  "تتصفح الآن: ".tr +
                                      SettingController
                                          .instance.quranBook.value.title,
                                  style: TextStyle(
                                    color: HomeController.to.pageColor.value ==
                                            Colors.white
                                        ? Get.theme.primaryColor
                                        : HomeController.to.pageColor.value,
                                  ),
                                ),
                              ),
                            ),
                            CustomFilledButton(
                              color: HomeController.to.pageColor.value ==
                                      Colors.white
                                  ? Get.theme.primaryColor
                                  : HomeController.to.pageColor.value,
                              textColor: Colors.white,
                              text: "تغيير".tr,
                              onPressed: () {
                                SettingController.instance.showBooksDialog();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }
}
