import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/view/components/custom_slider/custom_track_shape.dart';
import 'custom_slider_thumb_circle.dart';

class PageSlider extends StatefulWidget {
  final double sliderHeight;
  final int min;
  final int max;
  final int value;
  final bool fullWidth;
  final EdgeInsetsGeometry? margin;
  final Function(double val) onChanged;

  const PageSlider({super.key, 
    this.sliderHeight = 42,
    this.max = 10,
    this.min = 0,
    this.value = 0,
    required this.onChanged,
    this.fullWidth = false,
    this.margin,
  });

  @override
  _PageSliderState createState() => _PageSliderState();
}

class _PageSliderState extends State<PageSlider> {
  double _value = 0;
  @override
  void initState() {
    _value = widget.value.toDouble();
    HomeController.to.currentPageNumber.listen((value) {
      if (mounted) {
        setState(
          () {
            _value = value.toDouble();
          },
        );
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double paddingFactor = .3;

    if (widget.fullWidth) paddingFactor = .3;
    var suraNumber =
        Hive.box<QuranPage>(Boxes.quranPages).get(_value.toInt())?.suraNumber;
    print(suraNumber);
    var suraName =
        Hive.box<QuranSura>(Boxes.quranSuar).get(suraNumber)?.name ?? "";
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Container(
          //   padding: EdgeInsets.symmetric(horizontal: 16),
          //   decoration: BoxDecoration(
          //     color: Get.theme.scaffoldBackgroundColor,
          //     boxShadow: [
          //       BoxShadow(
          //         color: Colors.grey.shade300,
          //         blurRadius: 1,
          //         spreadRadius: 0.1,
          //       ),
          //     ],
          //     borderRadius: BorderRadius.all(
          //       Radius.circular(8),
          //     ),
          //   ),
          //   child: Text(
          //     suraName.trim().tr +
          //         "\n" +
          //         "الصفحة".tr +
          //         " " +
          //         _value.toInt().toLocaleNumber(),
          //     textAlign: TextAlign.center,
          //   ),
          // ),
          // SizedBox(
          //   height: 5,
          // ),
          Container(
            // margin: EdgeInsets.only(bottom: 3),
            padding: EdgeInsets.zero,
            height: (widget.sliderHeight),

            child: Padding(
              padding: EdgeInsets.fromLTRB(widget.sliderHeight * paddingFactor,
                  6, widget.sliderHeight * paddingFactor, 6),
              child: Row(
                children: <Widget>[
                  Text(
                    widget.min.toString(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: widget.sliderHeight * .25,
                      fontWeight: FontWeight.w700,
                      color: HomeController.to.pageColor.value == Colors.white
                          ? Get.theme.primaryColor
                          : HomeController.to.pageColor.value,
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor:
                                HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value,
                            inactiveTrackColor:
                                HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value,
                            trackHeight: 4.0,
                            thumbColor: Colors.white,
                            thumbShape: CustomSliderThumbCircle(
                              thumbRadius: widget.sliderHeight * .3,
                              min: widget.min,
                              max: widget.max,
                            ),
                            overlayColor:
                                (HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value)
                                ?.withOpacity(0.5),
                            valueIndicatorColor:
                                HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value,
                            activeTickMarkColor:
                                HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value,
                            inactiveTickMarkColor: Colors.red.withOpacity(.7),
                            trackShape: CustomTrackShape(),
                          ),
                          child: Slider(
                              divisions: 604,
                              max: widget.max.toDouble(),
                              min: widget.min.toDouble(),
                              value: _value,
                              onChangeEnd: (value) {
                                widget.onChanged(value);
                              },
                              onChanged: (value) {
                                setState(() {
                                  _value = value;
                                });
                              }),
                        ),
                      ),
                    ),
                  ),
                  Text(
                    widget.max.toString(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: widget.sliderHeight * .25,
                      fontWeight: FontWeight.w700,
                      color: HomeController.to.pageColor.value == Colors.white
                          ? Get.theme.primaryColor
                          : HomeController.to.pageColor.value,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
