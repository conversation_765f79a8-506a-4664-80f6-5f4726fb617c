import 'package:flutter/material.dart';
import 'package:quran_core/quran_core.dart';

import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/view/components/custom_slider/custom_track_shape.dart';
import 'custom_slider_thumb_rect.dart';
import 'package:rxdart/src/subjects/behavior_subject.dart';

class AudioPlayerSlider extends StatefulWidget {
  final double sliderHeight;
  final int value;
  final bool fullWidth;

  final EdgeInsetsGeometry? margin;
  final Function(double val) onChanged;

  const AudioPlayerSlider({
    super.key,
    this.sliderHeight = 42,
    this.value = 0,
    required this.onChanged,
    this.fullWidth = false,
    this.margin,
  });

  @override
  _AudioPlayerSliderState createState() => _AudioPlayerSliderState();
}

class _AudioPlayerSliderState extends State<AudioPlayerSlider> {
  double _value = 0;
  Duration currentPostion = Duration.zero;
  Duration totalDuration = Duration.zero;

  @override
  void initState() {
    _value = widget.value.toDouble();

    QuranAudioService.position.first
        .then((value) => _value = value.inSeconds.toDouble());

    QuranAudioService.position.listen(
      (value) {
        currentPostion = (value);
        _value = value.inSeconds.toDouble();

        if (mounted) {
          setState(() {});
        }
      },
    );

    QuranAudioService.currentSurahChangedEvent.listen(
      (value) {
        totalDuration = (value?.duration ?? Duration.zero);
        if (mounted) {
          setState(() {});
        }
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double paddingFactor = .05;

    if (widget.fullWidth) paddingFactor = .3;

    return Directionality(
      textDirection: TextDirection.ltr,
      child: SizedBox(
          //margin: widget.margin,
          width: double.infinity,

          // height: (this.widget.sliderHeight),
          // decoration: new BoxDecoration(
          //   borderRadius: new BorderRadius.all(
          //     Radius.circular(5),
          //   ),
          //   gradient: new LinearGradient(
          //       colors: [
          //         CustomColors.accentColor,
          //         CustomColors.accentColor.withOpacity(0.6),
          //       ],
          //       begin: const FractionalOffset(0.0, 0.0),
          //       end: const FractionalOffset(1.0, 1.00),
          //       stops: [0.0, 1.0],
          //       tileMode: TileMode.clamp),
          // ),
          child: Column(
            children: [
              Row(
                children: [
                  Text(currentPostion.toStringTime()),
                  const Spacer(),
                  Text(totalDuration.toStringTime()),
                ],
              ),
              Padding(
                padding: const EdgeInsets.all(0),
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: CustomColors.primaryColor,
                    inactiveTrackColor:
                        CustomColors.primaryColor.withOpacity(.5),
                    trackHeight: 1,
                    thumbShape: CustomSliderThumbRect(
                      thumbRadius: widget.sliderHeight * .5,
                      min: 0,
                      max: totalDuration.inSeconds.toInt(),
                      thumbHeight: widget.sliderHeight * .6,
                    ),
                    overlayColor: CustomColors.primaryColor.withOpacity(.4),
                    valueIndicatorColor: Colors.white,
                    activeTickMarkColor: CustomColors.primaryColor,
                    inactiveTickMarkColor: Colors.red.withOpacity(.7),
                    trackShape: CustomTrackShape(),
                  ),
                  child: Slider(
                      min: 0,
                      max: totalDuration.inSeconds.toDouble() == 0
                          ? 1
                          : totalDuration.inSeconds.toDouble(),
                      value: totalDuration.inSeconds.toDouble() == 0?0: _value,
                      onChangeEnd: (value) {
                        widget.onChanged(value);
                      },
                      onChanged: (value) {
                        QuranAudioService.seek(
                            Duration(seconds: value.toInt()));
                        setState(() {
                          _value = value;
                        });
                      }),
                ),
              ),
            ],
          )),
    );
  }
}
