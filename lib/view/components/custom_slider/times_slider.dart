import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';

import 'package:tadars/view/components/custom_slider/custom_track_shape.dart';
import '../../../utils/enums.dart';
import 'custom_slider_thumb_circle.dart';

class TimeSlider extends StatefulWidget {
  final double sliderHeight;
  final int min;
  final int max;
  final int value;
  final bool fullWidth;
  final EdgeInsetsGeometry? margin;
  final Function(double val) onChanged;

  const TimeSlider({
    super.key,
    this.sliderHeight = 42,
    this.max = 10,
    this.min = 0,
    this.value = 1,
    required this.onChanged,
    this.fullWidth = false,
    this.margin,
  });

  @override
  _TimeSliderState createState() => _TimeSliderState();
}

class _TimeSliderState extends State<TimeSlider> {
  double _value = 0;

  @override
  void initState() {
    _value = widget.value.toDouble();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double paddingFactor = .3;

    if (widget.fullWidth) paddingFactor = .3;

    return Container(
      margin: EdgeInsets.all(10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
        color: Get.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(
          8,
        ),
      ),
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.zero,
              height: (widget.sliderHeight),
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                    widget.sliderHeight * paddingFactor,
                    6,
                    widget.sliderHeight * paddingFactor,
                    6),
                child: Row(
                  children: <Widget>[
                    Material(
                      color: Colors.transparent,
                      child: Center(
                        child: InkWell(
                          borderRadius: BorderRadius.circular(9),
                          onTap: () async {
                            HomeController.to.quranPageController
                                .stopAutoScroll();
                            HomeController.to.showAutoScrollPagesTimer.value =
                                AutoScrollPagesStatus.stop;

                            await Future.delayed(
                                const Duration(milliseconds: 200));

                            HomeController.to.quranPageController
                                .scrollDirection = (Axis.horizontal);
                            await Future.delayed(
                                const Duration(milliseconds: 200));
                            HomeController.to.quranPageController.jumpToPage(
                                HomeController.to.quranPageController.page);
                          },
                          child: Center(
                            child: Icon(Icons.cancel_outlined,
                                color: HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value),
                          ),
                        ),
                      ),
                    ),
                    // Text(
                    //   widget.min.toString(),
                    //   textAlign: TextAlign.center,
                    //   style: TextStyle(
                    //     fontSize: widget.sliderHeight * .25,
                    //     fontWeight: FontWeight.w700,
                    //     color: HomeController.to.pageColor.value == Colors.white
                    //         ? Get.theme.primaryColor
                    //         : HomeController.to.pageColor.value,
                    //   ),
                    // ),
                    Expanded(
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: SliderTheme(
                            data: SliderTheme.of(context).copyWith(
                              activeTrackColor:
                                  HomeController.to.pageColor.value ==
                                          Colors.white
                                      ? Get.theme.primaryColor
                                      : HomeController.to.pageColor.value,
                              inactiveTrackColor:
                                  HomeController.to.pageColor.value ==
                                          Colors.white
                                      ? Get.theme.primaryColor
                                      : HomeController.to.pageColor.value,
                              trackHeight: 4.0,
                              thumbColor: Colors.white,
                              thumbShape: CustomSliderThumbCircle(
                                thumbRadius: widget.sliderHeight * .3,
                                min: widget.min,
                                max: widget.max,
                              ),
                              overlayColor:
                                  (HomeController.to.pageColor.value ==
                                              Colors.white
                                          ? Get.theme.primaryColor
                                          : HomeController.to.pageColor.value)
                                      ?.withOpacity(0.5),
                              valueIndicatorColor:
                                  HomeController.to.pageColor.value ==
                                          Colors.white
                                      ? Get.theme.primaryColor
                                      : HomeController.to.pageColor.value,
                              activeTickMarkColor:
                                  HomeController.to.pageColor.value ==
                                          Colors.white
                                      ? Get.theme.primaryColor
                                      : HomeController.to.pageColor.value,
                              inactiveTickMarkColor: Colors.red.withOpacity(.7),
                              trackShape: CustomTrackShape(),
                            ),
                            child: Slider(
                                divisions: 604,
                                max: widget.max.toDouble(),
                                min: widget.min.toDouble(),
                                value: _value,
                                thumbColor:
                                    Get.isDarkMode ? Colors.black : null,
                                onChangeEnd: (value) {
                                  widget.onChanged(value);

                                  HomeController.to.quranPageController
                                      .autoScrollOffset = value;
                                },
                                onChanged: (value) {
                                  setState(() {
                                    _value = value;
                                  });
                                }),
                          ),
                        ),
                      ),
                    ),

                    Obx(() {
                      return Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              HomeController.to.quranPageController
                                  .toggleAutoScroll();

                              if (HomeController
                                  .to.quranPageController.isAutoScrolling) {
                                HomeController.to.showAutoScrollPagesTimer
                                    .value = AutoScrollPagesStatus.active;
                              } else {
                                HomeController.to.showAutoScrollPagesTimer
                                    .value = AutoScrollPagesStatus.puse;
                              }

                              HomeController.to.showAutoScrollPagesTimer
                                  .refresh();

                              // isPuse = !HomeController
                              //     .to.quranPageController.isAutoScrolling;
                              // isActive =
                              //     HomeController.to.quranPageController.isAutoScrolling;
                            });
                          },
                          child: Container(
                            // padding: const EdgeInsets.symmetric(
                            //     vertical: 8, horizontal: 4),
                            decoration:
                                const BoxDecoration(shape: BoxShape.circle),
                            child: Icon(
                                (HomeController.to.showAutoScrollPagesTimer
                                            .value ==
                                        AutoScrollPagesStatus.puse)
                                    ? Icons.stop_circle_outlined
                                    : Icons.pause_circle_outline_rounded,
                                color: HomeController.to.pageColor.value ==
                                        Colors.white
                                    ? Get.theme.primaryColor
                                    : HomeController.to.pageColor.value),
                          ),
                        ),
                      );
                    }),
                    // Text(
                    //   widget.max.toString(),
                    //   textAlign: TextAlign.center,
                    //   style: TextStyle(
                    //     fontSize: widget.sliderHeight * .25,
                    //     fontWeight: FontWeight.w700,
                    //     color: HomeController.to.pageColor.value == Colors.white
                    //         ? Get.theme.primaryColor
                    //         : HomeController.to.pageColor.value,
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
