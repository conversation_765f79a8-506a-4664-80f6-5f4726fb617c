import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/components/custom_slider/custom_track_shape.dart';
import 'custom_slider_thumb_rect.dart';

class CustomSlider extends StatefulWidget {
  final double sliderHeight;
  final int min;
  final int max;
  final int value;
  final bool fullWidth;
  final EdgeInsetsGeometry? margin;
  final Function(double val) onChanged;

  const CustomSlider({
    super.key,
    this.sliderHeight = 42,
    this.max = 10,
    this.min = 0,
    this.value = 0,
    required this.onChanged,
    this.fullWidth = false,
    this.margin,
  });

  @override
  CustomSliderState createState() => CustomSliderState();
}

class CustomSliderState extends State<CustomSlider> {
  double _value = 0;
  @override
  void initState() {
    _value = widget.value.toDouble();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double paddingFactor = .05;

    if (widget.fullWidth) paddingFactor = .3;

    return Container(
      margin: widget.margin,
      width: widget.fullWidth ? double.infinity : (widget.sliderHeight) * 5.5,
      height: (widget.sliderHeight),
      child: Padding(
        padding: EdgeInsets.fromLTRB(widget.sliderHeight * paddingFactor, 2,
            widget.sliderHeight * paddingFactor, 2),
        child: Row(
          children: <Widget>[
            Text(
              'نص'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: widget.sliderHeight * .3,
                fontWeight: FontWeight.w700,
              ),
            ),
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: CustomColors.accentColor,
                      inactiveTrackColor:
                          CustomColors.accentColor.withOpacity(.5),
                      trackHeight: 4.0,
                      thumbShape: CustomSliderThumbRect(
                        thumbRadius: widget.sliderHeight * .4,
                        min: widget.min,
                        max: widget.max,
                        thumbHeight: widget.sliderHeight * .6,
                      ),
                      overlayColor: CustomColors.accentColor.withOpacity(.4),
                      valueIndicatorColor: Colors.white,
                      activeTickMarkColor: CustomColors.accentColor,
                      inactiveTickMarkColor: Colors.red.withOpacity(.7),
                      trackShape: CustomTrackShape(),
                    ),
                    child: Slider(
                      max: widget.max.toDouble(),
                      min: widget.min.toDouble(),
                      value: _value,
                      onChangeEnd: (value) {
                        widget.onChanged(value);
                      },
                      onChanged: (value) {
                        setState(
                          () {
                            _value = value;
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            Text(
              'نص'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: widget.sliderHeight * .5,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
