import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/services/audio_player_service.dart';
import 'package:tadars/services/network_service.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/audio_player/player_bottom_sheet.dart';

import '../../../helpers/boxes_helper.dart';
import 'download_action.dart';
import 'player_action.dart';

class Player extends StatelessWidget {
  const Player({super.key});
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        decoration: BoxDecoration(
          color: Get.isDarkMode
              ? Get.theme.primaryColor
              : Get.theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Get.isDarkMode ? Colors.white10 : Colors.grey.shade300,
              blurRadius: 15,
              spreadRadius: 1,
            ),
          ],
        ),
        margin: const EdgeInsets.only(bottom: 3),
        constraints: const BoxConstraints(maxWidth: 300),
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                PlayerAction(
                  color: CustomColors.primaryColor,
                  icon: QuranUIIcons.maximize,
                  onTap: () {
                    Get.bottomSheet(const PlayerBottomSheet());
                  },
                ),
                Expanded(
                  child: 
                  
                  ValueListenableBuilder(
                    valueListenable: QuranAudioService.processingState,
                    builder: (context, state, child) {
                      bool isLoading = state == AudioProcessingState.loading;
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Spacer(),
                          StreamBuilder(
                              stream: QuranAudioService.verseLoopCount.stream,
                              builder: (context, snapshot) {
                                return PlayerAction(
                                  icon: CommonConstants
                                          .loopIcons[snapshot.data] ??
                                      QuranUIIcons.loop,
                                  onTap: isLoading
                                      ? null
                                      : () {
                                          var loopCount =
                                              BoxesHelper.getLoopCount();
                                          loopCount++;

                                          if (loopCount > 5) {
                                            loopCount = 1;
                                          }
                                          BoxesHelper.setLoopCount(loopCount);

                                          QuranAudioService.setVerseLoopCount(
                                              loopCount);
                                        },
                                );
                              }),

                          const Spacer(),
                          PlayerAction(
                            icon: QuranUIIcons.stop,
                            color: Colors.redAccent,
                            onTap: isLoading
                                ? null
                                : () {
                                    QuranAudioService.stop();
                                  },
                          ),
                          // PlayerAction(
                          //   icon: QuranUIIcons.next,
                          //   onTap: isLoading
                          //       ? null
                          //       : () {
                          //           controller.goToPreviousSura();
                          //         },
                          // ),
                          const Spacer(),
                          PlayerAction(
                            icon: QuranUIIcons.fastForward,
                            onTap: isLoading
                                ? null
                                : () {
                                    QuranAudioService.seekToPreviousVerse();
                                  },
                          ),
                          const Spacer(),

                          if (isLoading)
                            Container(
                              padding: const EdgeInsets.all(4),
                              child: const Center(
                                child: SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                            )
                          else
                            StreamBuilder(
                                stream: QuranAudioService.playing,
                                builder: (context, snapshot) {
                                  if (snapshot.data != true) {
                                    return PlayerAction(
                                      icon: QuranUIIcons.play,
                                      onTap: isLoading
                                          ? null
                                          : () {
                                              QuranAudioService.play();
                                            },
                                    );
                                  } else {
                                    return PlayerAction(
                                      icon: QuranUIIcons.pause,
                                      onTap: isLoading
                                          ? null
                                          : () {
                                              QuranAudioService.pause();
                                            },
                                    );
                                  }
                                }),
                          
                          
                          const Spacer(),
                          PlayerAction(
                            icon: QuranUIIcons.rewind,
                            onTap: isLoading
                                ? null
                                : () {
                                    QuranAudioService.seekToNextVerse();
                                  },
                          ),
                          // PlayerAction(
                          //   icon: QuranUIIcons.previous,
                          //   onTap: isLoading
                          //       ? null
                          //       : () {
                          //           controller.goToNextSura();
                          //         },
                          // ),
                          const Spacer(),
                        ],
                      );
                    },
                  ),
               
               
               
                ),
                const DownloadAction(),
              ],
            ),
            // if (isLoading)
            //   Container(
            //     child: Center(
            //       child: SizedBox(
            //         child: CircularProgressIndicator(
            //           strokeWidth: 2,
            //         ),
            //         height: 18,
            //         width: 18,
            //       ),
            //     ),
            //   )
          ],
        ),
      ),
    );
  }
}
