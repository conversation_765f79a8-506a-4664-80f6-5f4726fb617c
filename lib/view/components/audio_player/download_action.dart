import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:quran_core/quran_core.dart';

import '../../../services/network_service.dart';
import '../../../utils/quran_ui_icons.dart';
import 'player_action.dart';

class DownloadAction extends StatelessWidget {
  const DownloadAction({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: QuranAudioService.currentSurahDownloadProgress,
        builder: (context, snap) {
          return StreamBuilder(
            stream: QuranAudioService.currentSurahChangedEvent,
            builder: (ctx, snapshot) {
              return snap.data != null
                  ? Builder(builder: (context) {
                      var percent = snap.data!.progress / snap.data!.total;

                      return CircularPercentIndicator(
                        radius: 10,
                        percent: percent,
                        lineWidth: 2,
                        center: Center(
                          child: Text(
                            (percent * 100).toInt().toString(),
                            maxLines: 1,
                            style: const TextStyle(
                              fontSize: 7,
                            ),
                          ),
                        ),
                      );
                    })
                  : !(snapshot.data?.downloaded ?? false)
                      ? PlayerAction(
                          icon: QuranUIIcons.downloadCloud,
                          color: Get.theme.colorScheme.secondary,
                          onTap: NetworkServices.instance.connected.value
                              ? () {
                                  QuranAudioService.downloadCurrentSurah();
                                }
                              : null,
                        )
                      : PlayerAction(
                          icon: QuranUIIcons.cloud,
                          color: Get.theme.colorScheme.secondary,
                          onTap: null,
                        );
            },
          );
        });
  }
}
