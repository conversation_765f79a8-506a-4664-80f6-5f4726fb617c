import 'package:flutter/material.dart';

class PlayerAction extends StatelessWidget {
  const PlayerAction({
    Key? key,
    required this.icon,
    required this.onTap,
    this.color,
    this.size = 18,
  }) : super(key: key);
  final IconData icon;
  final Color? color;
  final double size;
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all((size) * 0.24),
          child: Icon(
            icon,
            size: size,
            color: onTap == null ? Colors.grey : color ?? Colors.grey.shade700,
          ),
        ),
      ),
    );
  }
}
