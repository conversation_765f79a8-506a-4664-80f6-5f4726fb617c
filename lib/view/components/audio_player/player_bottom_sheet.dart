import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/view/components/audio_player/download_action.dart';
import 'package:tadars/view/components/audio_player/player_action.dart';

import '../../../helpers/boxes_helper.dart';
import '../../../utils/common.dart';
import '../../../utils/constants/common_constants.dart';
import '../../../utils/quran_ui_icons.dart';
import '../custom_slider/audio_player_slider.dart';

class PlayerBottomSheet extends StatelessWidget {
  const PlayerBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        padding: const EdgeInsets.all(16),
        width: double.maxFinite,
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                Common.showChangeReciterBottomSheet();
                // AudioPlayerService.to.changeReciter(5);
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    StreamBuilder(
                        stream: QuranAudioService.currentReciter,
                        builder: (context, snapshot) {
                          return Text(
                            snapshot.data?.name.tr ?? "",
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        }),
                    const Icon(
                      Icons.arrow_drop_down,
                      size: 30,
                    )
                  ],
                ),
              ),
            ),
            StreamBuilder(
              stream: QuranAudioService.currentSurah,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return Container();
                }
                var surah = snapshot.data;
                return StreamBuilder(
                    stream: QuranAudioService.currentVerse,
                    builder: (context, verseSnapshot) {
                      var verse = verseSnapshot.data;
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                            "${surah?.searchableName ?? ""} : [${verse?.number ?? "1"}]"),
                      );
                    });
              },
            ),
            Row(
              children: [
                Expanded(
                  child: PlayerAction(
                    size: 25,
                    icon: QuranUIIcons.next,
                    onTap: () {
                      QuranAudioService.seekToPreviousSurah();
                    },
                  ),
                ),
                Expanded(
                  child: PlayerAction(
                    size: 25,
                    icon: QuranUIIcons.fastForward,
                    onTap: () {
                      QuranAudioService.seekToPreviousVerse();
                    },
                  ),
                ),
                ValueListenableBuilder(
                    valueListenable: QuranAudioService.processingState,
                    builder: (context, value, child) {
                      var isLoading = value == AudioProcessingState.loading;
                      if (isLoading) {
                        return Container(
                          padding: const EdgeInsets.all(4),
                          child: Center(
                            child: SizedBox(
                              height: 50,
                              width: 50,
                              child: Theme(
                                data: ThemeData(
                                  colorScheme: Get.theme.colorScheme.copyWith(
                                    secondary: Get.theme.primaryColor,
                                  ),
                                ),
                                child: const CircularProgressIndicator(
                                  strokeWidth: 4,
                                ),
                              ),
                            ),
                          ),
                        );
                      }
                      return StreamBuilder(
                        stream: QuranAudioService.playing,
                        builder: (context, snapshot) {
                          var playing = snapshot.data ?? false;
                          if (playing) {
                            return Material(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(100),
                              ),
                              color: Get.theme.primaryColor,
                              child: InkWell(
                                onTap: isLoading
                                    ? null
                                    : () {
                                        QuranAudioService.pause();
                                      },
                                borderRadius: BorderRadius.circular(100),
                                child: const Padding(
                                  padding: EdgeInsets.all(32.0),
                                  child: Icon(
                                    QuranUIIcons.pause,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            );
                          } else {
                            return Material(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(100),
                              ),
                              color: Get.theme.primaryColor,
                              child: InkWell(
                                onTap: isLoading
                                    ? null
                                    : () {
                                        QuranAudioService.play();
                                      },
                                borderRadius: BorderRadius.circular(100),
                                child: const Padding(
                                  padding: EdgeInsets.all(32.0),
                                  child: Icon(
                                    QuranUIIcons.play,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            );
                          }
                        },
                      );
                    }),
                Expanded(
                  child: PlayerAction(
                    size: 25,
                    icon: QuranUIIcons.rewind,
                    onTap: () {
                      QuranAudioService.seekToNextVerse();
                    },
                  ),
                ),
                Expanded(
                  child: PlayerAction(
                    size: 25,
                    icon: QuranUIIcons.previous,
                    onTap: () {
                      QuranAudioService.seekToNextSurah();
                    },
                  ),
                ),
              ],
            ),
            AudioPlayerSlider(onChanged: (value) {}),
            Row(
              children: [
                ValueListenableBuilder(
                    valueListenable: QuranAudioService.processingState,
                    builder: (context, state, child) {
                      bool isLoading = state == AudioProcessingState.loading;
                      return StreamBuilder(
                          stream: QuranAudioService.verseLoopCount.stream,
                          builder: (context, snapshot) {
                            return PlayerAction(
                              icon: CommonConstants
                                              .loopIcons[snapshot.data] ??
                                  QuranUIIcons.loop,
                              onTap: isLoading
                                  ? null
                                  : () {
                                      var loopCount =
                                          BoxesHelper.getLoopCount();
                                      loopCount++;

                                      if (loopCount > 5) {
                                        loopCount = 1;
                                      }
                                      BoxesHelper.setLoopCount(loopCount);

                                      QuranAudioService.setVerseLoopCount(
                                          loopCount);
                                    },
                            );
                          });
                    }),
                const Spacer(),
                const DownloadAction()
              ],
            ),
          ],
        ),
      ),
    );
  }
}
