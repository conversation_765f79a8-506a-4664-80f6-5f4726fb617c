import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class CustomBottomSheet extends StatelessWidget {
  final String? title;
  final Widget body;
  final bool withExpaned;
  const CustomBottomSheet({
    super.key,
    this.title,
    required this.body,
    this.withExpaned = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: Get.size.height * 0.9,
        minHeight: 50,
      ),
      decoration: BoxDecoration(
        color: Get.theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // title
            if (title != null) 
            Container(
              padding: const EdgeInsets.all(16),
              width: double.maxFinite,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (title != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      title!,
                      style: TextStyle(
                        fontSize: 18,
                        color: CustomColors.primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Divider(
              height: 1,
              indent: 16,
              endIndent: 16,
            ),
            if (withExpaned)
              Expanded(
                child: body,
              )
            else
              body
          ],
        ),
      ),
    );
  }
}
