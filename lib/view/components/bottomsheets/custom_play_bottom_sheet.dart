import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/view/components/buttons/cutom_filled_button.dart';

import '../../../utils/constants/common_constants.dart';
import 'custom_bottom_sheet.dart';

class CustomPlayBottomSheet extends StatefulWidget {
  const CustomPlayBottomSheet(
      {super.key, required this.surahNumber, required this.verseNumber});

  final int surahNumber;
  final int verseNumber;

  @override
  State<CustomPlayBottomSheet> createState() => _CustomPlayBottomSheetState();
}

class _CustomPlayBottomSheetState extends State<CustomPlayBottomSheet> {
  late int fromVerse;
  late int toVerse;
  int loopCount = 1;
  double speed = 1.0;
  bool repeateAll = false;

  @override
  void initState() {
    fromVerse = widget.verseNumber;
    toVerse = widget.verseNumber;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: "تشغيل مخصص".tr,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FutureBuilder(
              future: QuranDatabaseProvider.getSurahVerses(widget.surahNumber),
              builder: (context, snapshot) {
                if (snapshot.data == null) {
                  return const SizedBox.shrink();
                }
                return Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomDropdown<Verse>(
                          hintText: "من الاية".tr,
                          // menuMaxHeight: Get.height * 0.5,
                          // isExpanded: true,
                          // isDense: true,
                          initialItem: snapshot.data!.firstWhereOrNull(
                              (verse) => verse.number == widget.verseNumber),
                          // style: TextStyle(overflow: TextOverflow.ellipsis),
                          decoration: CustomDropdownDecoration(
                            expandedFillColor:
                                Get.isDarkMode ? Colors.black : null,
                            closedFillColor:
                                Get.isDarkMode ? Colors.black : null,
                          ),
                          listItemBuilder:
                              (ctx, verse, isSelected, onItemSelect) => Text(
                                "${verse.number.toArabicNumber()} - ${verse.text}",
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,

                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                      fontSize: 18,

                                    ),
                                  ),
                          headerBuilder: (context, verse, s) => Text(
                                "${verse.number.toArabicNumber()} - ${verse.text}",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                  fontSize: 18,
                                ),
                              )
                            ,
                          items: snapshot.data!,
                          onChanged: (value) {
                            fromVerse = value?.number ?? widget.verseNumber;
                            setState(() {});
                          }),
                      const SizedBox(
                        height: 10,
                      ),
                      CustomDropdown(
                          hintText: "الى الاية".tr,
                          initialItem: snapshot.data!.firstWhereOrNull(
                              (verse) => verse.number == fromVerse),

                          // style: TextStyle(overflow: TextOverflow.ellipsis),
                          decoration: CustomDropdownDecoration(
                            expandedFillColor:
                                Get.isDarkMode ? Colors.black : null,
                            closedFillColor:
                                Get.isDarkMode ? Colors.black : null,
                          ),
                          listItemBuilder:
                              (ctx, verse, isSelected, onItemSelect) => Text(
                                "${verse.number.toArabicNumber()} - ${verse.text}",
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                      fontSize: 18,
                                    ),
                                  ),
                          headerBuilder: (context, verse, s) => Text(
                                "${verse.number.toArabicNumber()} - ${verse.text}",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                  fontSize: 18,
                                ),
                              ),
                          items: snapshot.data!
                              .where((e) => e.number >= fromVerse)
                              .toList(),
                          onChanged: (value) {
                            toVerse = value?.number ?? widget.verseNumber;


                          }),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField(
                                hint: Text("التكرارات".tr),
                                value: 1,
                            items:
                                CommonConstants.getLoopStrings().keys
                                    .map<DropdownMenuItem>(
                                        (key) => DropdownMenuItem(
                                              value: key,
                                              child: Text(CommonConstants
                                                      .getLoopStrings()[key] ??
                                                  ""),
                                            ))
                                    .toList(),
                                onChanged: (value) {
                              if (value != null) {
                                loopCount = value as int;
                              }
                                }),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: DropdownButtonFormField(
                                hint: Text("سرعة الصوت".tr),
                                value: 1,
                                items: [1, 1.25, 1.5, 1.75]
                                    .map((e) => DropdownMenuItem(
                                          value: e,
                                          child: Text(
                                            "${e}x",
                                          ),
                                        ))
                                    .toList(),
                                onChanged: (value) {
                                  speed = value?.toDouble() ?? 1;
                                }),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      CheckboxListTile(
                          contentPadding: EdgeInsets.zero,
                          controlAffinity: ListTileControlAffinity.leading,
                          title: Text(
                            "تكرار المقطع بالكامل".tr,
                            style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                                fontWeight: FontWeight.normal),
                          ),
                          value: repeateAll,
                          onChanged: (value) {
                            setState(() {
                              repeateAll = value ?? false;
                            });
                          }),
                      const SizedBox(
                        height: 30,
                      ),
                      CustomFilledButton(
                          fullWidth: true,
                      text: "تشغيل".tr,
                          onPressed: () async {
                            Get.back();
                            if (repeateAll) {
                              await QuranAudioService.setVerseLoopCount(1);

                              QuranAudioService.setVersesRangeLoopCount(
                                  loopCount);
                            } else {
                              QuranAudioService.setVerseLoopCount(loopCount);
                            }
                            QuranAudioService.setSpeed(speed);
                            QuranAudioService.playFromUntilVerse(
                                widget.surahNumber, fromVerse, toVerse);
                          })
                    ],
                  ),
                );
              })
        ],
      ),
    );
  }
}
