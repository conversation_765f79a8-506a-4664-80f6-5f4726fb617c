

// import 'package:flutter/material.dart';

// class CustomMenuOptions extends StatelessWidget {
//   final String title;
//   final String image;
//   final void Function()? onTap;
//   final bool haveBorder;

//   const CustomMenuOptions({
//     super.key,
//     required this.title,
//     required this.image,
//     required this.onTap,
//     this.haveBorder = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Material(
//           color: Colors.transparent,
//           child: InkWell(
//             onTap: onTap,
//             child: Padding(
//               padding: const EdgeInsets.only(bottom: 10, top: 10),
//               child: Row(
//                 children: [
//                   Image.asset(image, height: 36, width: 36),
//                   Padding(
//                     padding: const EdgeInsets.only(left: 14, right: 10),
//                     child: Container(
//                         height: 28,
//                         width: 1,
//                         color:
//                             CustomColors.kGreyTextColor.withValues(alpha: 0.3)),
//                   ),
//                   CustomText(
//                     title,
//                     style: TextStyle(
//                       fontWeight: FontWeight.bold,
//                       color: CustomColors.kRedColor,
//                       fontSize: 15,
//                       shadows: [
//                         // Shadow(
//                         //   color: Colors.black.withValues(alpha: 0.2),
//                         //   offset: const Offset(2.0, 3),
//                         //   blurRadius: 10,
//                         // ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         if (haveBorder)
//           Container(
//               // margin: const EdgeInsets.only(bottom: 10),
//               height: 1,
//               color: CustomColors.kGreyTextColor.withValues(alpha: 0.1))
//         else
//           const Padding(
//             padding: EdgeInsets.symmetric(vertical: 16),
//           ),
//       ],
//     );
//   }
// }
