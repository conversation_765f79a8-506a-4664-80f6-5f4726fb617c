import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:tadars/controllers/language_selection_controller.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class LanguageLoadingDialog extends StatelessWidget {
  final LanguageSelectionController controller;

  const LanguageLoadingDialog({
    super.key,
    required this.controller,
  });

  /// Shows the language loading dialog
  static void show(LanguageSelectionController controller) {
    if (Get.isDialogOpen ?? false) return;

    Get.dialog(
      LanguageLoadingDialog(controller: controller),
      barrierDismissible: false,
      useSafeArea: true,
    );
  }

  /// Closes the dialog if it's open
  static void close() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!controller.isLoading.value) {
        // Close the dialog if loading is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          close();
        });
      }

      return Container(
        color: Colors.transparent,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Center(
            child: Card(
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              color: Colors.white.withAlpha(230),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildProgressIndicator(),
                    const SizedBox(height: 24),
                    Text(
                      controller.currentStepTextArabic,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: CustomColors.primaryColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.currentStepText,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 16,
                      ),
                    ),
                    if (controller.isProcessing.value && controller.currentFile.value.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: CustomColors.primaryColor.withAlpha(30),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            controller.currentFile.value,
                            style: TextStyle(
                              color: CustomColors.primaryColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    _buildProgressText(),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildProgressIndicator() {
    return Obx(() {
      if (controller.isDownloading.value) {
        // Download progress
        return Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 120,
              height: 120,
              child: CircularPercentIndicator(
                radius: 55.0,
                lineWidth: 12.0,
                percent: controller.downloadProgress.value,
                circularStrokeCap: CircularStrokeCap.round,
                backgroundColor: Colors.grey[200]!,
                progressColor: CustomColors.primaryColor,
                animation: true,
                animateFromLastPercent: true,
              ),
            ),
            Column(
              children: [
                Text(
                  controller.downloadProgressText,
                  style: TextStyle(
                    color: CustomColors.primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 24.0,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  controller.downloadSizeText,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12.0,
                  ),
                ),
              
              ],
            ),
          ],
        );
      } else if (controller.isExtracting.value) {
        // Extraction progress
        return Container(
          width: 120,
          height: 120,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: CustomColors.primaryColor.withAlpha(20),
            shape: BoxShape.circle,
          ),
          child: CircularProgressIndicator(
            color: CustomColors.primaryColor,
            strokeWidth: 6,
          ),
        );
      } else if (controller.isProcessing.value) {
        // Processing progress
        return Column(
          children: [
            Container(
              width: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: CustomColors.primaryColor.withAlpha(20),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  LinearPercentIndicator(
                    width: 218.0,
                    lineHeight: 16.0,
                    percent: controller.totalFiles.value > 0
                        ? controller.processedFiles.value / controller.totalFiles.value
                        : 0.0,
                    backgroundColor: Colors.grey[200],
                    progressColor: CustomColors.primaryColor,
                    barRadius: const Radius.circular(8),
                    padding: EdgeInsets.zero,
                    animation: true,
                    animateFromLastPercent: true,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "${controller.processedFiles.value}/${controller.totalFiles.value}",
                        style: TextStyle(
                          color: CustomColors.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        controller.processingProgressText,
                        style: TextStyle(
                          color: CustomColors.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      } else {
        // Default loading
        return SizedBox(
          width: 80,
          height: 80,
          child: CircularProgressIndicator(
            color: CustomColors.primaryColor,
            strokeWidth: 6,
          ),
        );
      }
    });
  }

  Widget _buildProgressText() {
    return Obx(() {
      String message = "";

      if (controller.isDownloading.value) {
        message = "Downloading language files from server...";
      } else if (controller.isExtracting.value) {
        message = "Extracting files, please wait...";
      } else if (controller.isProcessing.value) {
        message = "Importing language data to database...";
      } else {
        message = "Preparing...";
      }

      return Text(
        message,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 14,
          fontStyle: FontStyle.italic,
        ),
      );
    });
  }
}
