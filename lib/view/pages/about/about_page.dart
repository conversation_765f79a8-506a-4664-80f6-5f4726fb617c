
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutPage extends StatelessWidget {
  final bool withAlmaalemLogo;
  final bool withAppbar;
  final bool withLoading;
  const AboutPage({
    super.key,
    this.withAlmaalemLogo = true,
    this.withAppbar = true,
      this.withLoading = false
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: withAppbar
            ? AppBar(
                iconTheme: IconThemeData(
                  color: Get.theme.primaryColor,
                ),
                backgroundColor: Colors.transparent,
                elevation: 0,
              )
            : null,
        extendBodyBehindAppBar: true,
        // backgroundColor: Get.theme.scaffoldBackgroundColor,
        body: Stack(
          children: [
            AnimatedOpacity(
              opacity: 0.1,
              duration: const Duration(seconds: 0),
              child: SvgPicture.asset(
                "assets/svgs/new_pattern.svg",
                fit: BoxFit.cover,
                width: double.maxFinite,
                height: double.maxFinite,
                colorFilter: ColorFilter.mode(
                    CustomColors.primaryColor, BlendMode.srcIn),
              ),
            ),
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    "assets/images/new_logo.png",
                    height: 180,
                    fit: BoxFit.contain,
                  ),
                  if (withLoading)
                    Container(
                        width: 150,
                        padding: EdgeInsets.only(top: 30),
                        child: LinearProgressIndicator()),



                  // Padding(
                  //   padding: const EdgeInsets.only(
                  //     top: 32,
                  //   ),
                  //   child: SizedBox(
                  //     width: 250,
                  //     child: Text(
                  //       "تدارس القرآن الكريم",
                  //       textAlign: TextAlign.center,
                  //       style: TextStyle(
                  //         fontSize: 38,
                  //         color: Get.theme.primaryColor,
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  
                ],
              ),
            ),
            
          
    
              Align(
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    // alignment: Alignment.bottomCenter,
                    child: SvgPicture.asset(
                      "assets/svgs/alnaba.svg",
                      height: 120,
                    ),
                  ),
                    // Container(
                    //   child: Text(
                    //     "طور بواسطة",
                    //     style: TextStyle(fontSize: 18),
                    //   ),
                    // ),
                  if (withAlmaalemLogo)
                    Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AboutAction(
                                icon: const Icon(
                                  Icons.language,
                                  size: 25,
                                  color: Colors.white,
                                ),
                                onTap: () {
                                  launchUrl(Uri.parse('https://tadars.com'));
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              AboutAction(
                                icon: const Icon(
                                  Icons.mail_outline,
                                  size: 25,
                                  color: Colors.white,
                                ),
                                onTap: () async {
                                  CommonFunctions.contact();
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              AboutAction(
                                icon: SizedBox(
                                  width: 25,
                                  height: 25,
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: SvgPicture.asset(
                                      'assets/svgs/x.svg',
                                      fit: BoxFit.contain,
                                      colorFilter: const ColorFilter.mode(
                                          Colors.white, BlendMode.srcIn),
                                    ),
                                  ),
                                ),
                                onTap: () {
                                  launchUrl(
                                    Uri.parse("https://twitter.com/tadars_q"),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                      width: 200,
                      child: InkWell(
                        onTap: () {
                          launchUrl(Uri.parse("https://wa.me/967773095254"));
                        },
                        child: Image.asset("assets/images/almaalem.png"),
                      ),
                    ),
                      ],
                    ),
                   

                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class AboutAction extends StatelessWidget {
  const AboutAction({
    super.key,
    required this.icon,
    required this.onTap,
  });
  final Widget icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(8),
      color: Get.theme.primaryColor,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(12.0),
          child: icon,
        ),
      ),
    );
  }
}
