import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_book.dart';

import '../../../utils/constants/custom_colors.dart';
import 'components/book_card.dart';

class BooksPage extends StatelessWidget {
  const BooksPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المصاحف والترجمات'),
      ),
      body: Container(
        height: double.maxFinite,
        color: CustomColors.primaryColor.withOpacity(0.1),
        child: ValueListenableBuilder<Box<QuranBook>>(
            valueListenable: BoxesHelper.getListenableQuranBooks(),
            builder: (context, box, child) {
              var books = box.values.toList();
              books.sort((a, b) => a.order.compareTo(b.order));
              return ListView.builder(
                padding: const EdgeInsets.only(top: 16),
                itemCount: books.length,
                itemBuilder: (context, index) {
                  return BookCard(item: books[index]);
                },
              );
            }),
      ),
    );
  }
}
