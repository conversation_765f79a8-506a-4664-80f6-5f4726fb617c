import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:tadars/controllers/setting_controller.dart';
import 'package:tadars/models/hive/quran_book.dart';
import 'package:tadars/utils/extensions.dart';

import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/quran_ui_icons.dart';
import 'quran_book_detail_dialog.dart';

class BookCard extends StatelessWidget {
  const BookCard({
    Key? key,
    required this.item,
    this.margin,
  }) : super(key: key);
  final QuranBook item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(right: 16, left: 16, bottom: 12),
      
      child: Slidable(
        endActionPane: (!item.isDownloaded)
            ? null
            : ActionPane(
                motion: const ScrollMotion(),
                extentRatio: 0.25,
                children: <Widget>[
                  SlidableAction(
                    label: 'حذف'.tr,
                    backgroundColor: Colors.redAccent,
                    icon: Icons.remove,
                    onPressed: (context) {
                      SettingController.instance.deleteQuranBook(item.id); 

                    },
                  ),
                ],
              ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: CommonStyles.borderRadius,
            color: Get.theme.scaffoldBackgroundColor,
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: CommonStyles.borderRadius,
            child: ListTile(
              shape: RoundedRectangleBorder(
                borderRadius: CommonStyles.borderRadius,
              ),
              contentPadding: const EdgeInsetsDirectional.only(
                start: 16,
                bottom: 2,
                top: 2,
                end: 8,
              ),
              onTap: () {
                Get.dialog(
                  QuranBookDetailDialog(id: item.id),
                );
              },
              // leading: Icon(
              //   QuranUIIcons2.book,
              //   color: CustomColors.primaryColor,
              // ),
              trailing: Obx(() {
                var downloading =
                    SettingController.instance.downloadingItems[item.id];
                if (item.isDownloaded) return const SizedBox();

                return downloading != null
                    ? Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CircularPercentIndicator(
                          radius: 11,
                          lineWidth: 3,
                          progressColor: CustomColors.accentColor,
                          percent: downloading.progress / downloading.total,
                        ),
                      )
                    : InkWell(
                        borderRadius: CommonStyles.borderRadius,
                        onTap: () {
                          // download
                          SettingController.instance
                              .downloadQuranBook(item.id, item.path);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: item.isDownloaded
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                )
                              : Icon(
                                  QuranUIIcons.download,
                                  color: CustomColors.accentColor,
                                  size: 20,
                                ),
                        ),
                      );
              }),
              title: Text(
                item.title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
              ),
              subtitle: Text(
                "${"حجم المصحف ".tr}${item.size.toInt().toArabicNumber()} م.ب",
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: CustomColors.primaryColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
