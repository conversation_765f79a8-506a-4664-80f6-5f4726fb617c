import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:tadars/custom_packages/flutter_html/lib/flutter_html.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/quran_ui_icons.dart';

import '../../../../controllers/setting_controller.dart';
import '../../../../models/hive/quran_book.dart';
import '../../../../utils/constants/configs.dart';

class QuranBookDetailDialog extends StatelessWidget {
  final int id;
  const QuranBookDetailDialog({
    Key? key,
    required this.id,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: ValueListenableBuilder<Box<QuranBook>>(
          valueListenable: BoxesHelper.getListenableQuranBook(id),
          builder: (context, box, child) {
            var item = BoxesHelper.getQuranBookById(id);
            return Material(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: SizedBox(
                width: Get.width * 0.9,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(
                        8,
                      ),
                      decoration: BoxDecoration(
                        color: CustomColors.primaryColor.withOpacity(0.3),
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(10),
                        ),
                      ),
                      width: double.maxFinite,
                      child: SizedBox(
                        height: 40,
                        child: NavigationToolbar(
                          trailing: IconButton(
                            icon: Icon(
                              Icons.close,
                              color: CustomColors.primaryColor,
                            ),
                            onPressed: () => Get.back(),
                          ),
                          middle: Text(
                            item!.title,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: CustomColors.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      constraints: BoxConstraints(
                        minHeight: Get.height * 0.3,
                        maxHeight: Get.height * 0.8,
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            QuranBooKPreviewCarouselSlider(
                              path: item.path,
                            ),
                            Html(
                              data: item.detail,
                              style: {
                                '*': Style(
                                  textAlign: TextAlign.center,
                                ),
                              },
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  right: 16.0, left: 16, bottom: 16),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'حجم الكتاب: ${item.size
                                                  .toInt()
                                                  .toArabicNumber()} م.ب',
                                          style: TextStyle(
                                            color: CustomColors.primaryColor,
                                          ),
                                        ),
                                        Text(
                                          'عدد الصفحات: ${item.pagesCount
                                                  .toInt()
                                                  .toArabicNumber()}',
                                          style: TextStyle(
                                            color: CustomColors.primaryColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Obx(() {
                                    var downloading = SettingController
                                        .instance.downloadingItems[item.id];
                                    if (downloading != null) {
                                      return Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: CircularPercentIndicator(
                                          radius: 11,
                                          lineWidth: 3,
                                          progressColor:
                                              CustomColors.accentColor,
                                          percent: downloading.progress /
                                              downloading.total,
                                        ),
                                      );
                                    } else {
                                      return (item.isDownloaded)
                                          ? IconCustomFilledButton(
                                              icon: Icons.remove,
                                              text: 'حذف',
                                              color: Colors.redAccent,
                                              onPressed: () async {
                                                await SettingController.instance
                                                    .deleteQuranBook(item.id);
                                                SettingController
                                                    .instance.downloadingItems
                                                    .refresh();
                                              },
                                            )
                                          : IconCustomFilledButton(
                                              icon: QuranUIIcons.download,
                                              text: 'تحميل',
                                              onPressed: () {
                                                SettingController.instance
                                                    .downloadQuranBook(
                                                        item.id, item.path);
                                              },
                                            );
                                    }
                                  }),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
    );
  }
}

class IconCustomFilledButton extends StatelessWidget {
  final IconData icon;
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  const IconCustomFilledButton({
    Key? key,
    required this.icon,
    required this.text,
    required this.onPressed,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(10),
      color: color ?? CustomColors.primaryColor,
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(
                width: 8,
              ),
              Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}



class QuranBooKPreviewCarouselSlider extends StatelessWidget {
  final String path;
  const QuranBooKPreviewCarouselSlider({
    Key? key,
    required this.path,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: CarouselSlider.builder(
        itemCount: 5,
        itemBuilder: (context, index, i) {
          return Container(
            width: double.maxFinite,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade500,
                width: 1,
              ),
              borderRadius: const BorderRadius.all(
                Radius.circular(10),
              ),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(
                Radius.circular(10),
              ),
              child: CachedNetworkImage(
                imageUrl: Configs.getQuranBooksPreviewUrl(path, index + 1),
                fit: BoxFit.fitWidth,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          );
        },
        options: CarouselOptions(
          autoPlay: true,
          enlargeCenterPage: true,
          aspectRatio: 1,
          viewportFraction: 0.7,
          initialPage: 0,
          enableInfiniteScroll: true,
          autoPlayInterval: const Duration(seconds: 7),
          autoPlayAnimationDuration: const Duration(milliseconds: 800),
          autoPlayCurve: Curves.fastOutSlowIn,
          scrollDirection: Axis.horizontal,
        ),
      ),
    );
  }
}
