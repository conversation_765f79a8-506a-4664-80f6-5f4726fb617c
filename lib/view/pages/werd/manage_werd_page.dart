import 'package:flutter/material.dart' hide CustomFilledButton;
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/werd_controller.dart';
import 'package:tadars/models/hive/werd.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

import '../../../controllers/setting_controller.dart';
import '../../../models/view_models/toggle_item.dart';
import '../../components/buttons/cutom_filled_button.dart';
import 'package:tadars/utils/extensions.dart';

class ManageWerdPage extends GetView<WerdController> {
  const ManageWerdPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // form key
    final formKey = GlobalKey<FormState>();
    // listenable werd page type
    ValueNotifier<int> werdPageType = ValueNotifier<int>(0);
    // listenable werd daily page type
    ValueNotifier<int> werdDailyPageType = ValueNotifier<int>(0);
    // listenable alarm
    ValueNotifier<bool> alarm = ValueNotifier<bool>(false);
    Werd? werd = Get.arguments;
    Werd werdToSave = Werd.empty();
    int werdPageStart = 0;

    //
    if (werd != null) {
      werdPageType.value = werd.startPage == 1 && werd.endPage == 604 ? 0 : 1;

      if (werd.perPage == 2) {
        werdDailyPageType.value = 0;
      } else if (werd.perPage == 1) {
        werdDailyPageType.value = 1;
      } else {
        werdDailyPageType.value = 2;
      }
      alarm.value = werd.withAlarm;
      werdToSave.id = werd.id;
    }
    return Scaffold(
        appBar: AppBar(
          title: Text(werd == null ? 'اضافة ورد جديد' : 'تعديل ورد'),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                FilledTextFormField(
                  textAlign: TextAlign.right,
                  hintText: werd?.name,
                  labelText: 'اسم الورد',
                  validator: (value) {
                    if (value == null || value.trim() == "") {
                      return 'اسم الورد مطلوب';
                    } else {
                      return null;
                    }
                  },
                  onSaved: (value) {
                    werdToSave.name = value?.trim() ?? "";
                  },
                ),
                ToggleFormField<int>(
                  margin: const EdgeInsets.only(top: 8),
                  labelText: 'نوع الورد',
                  onSave: (value) {
                    werdToSave.type = value ?? 0;
                  },
                  items: [
                    ToggleItem(label: 'ورد تلاوة', value: 0),
                    ToggleItem(label: 'ورد حفظ', value: 1),
                  ],
                  initialValue: werd?.type ?? 0,
                ),
                ToggleFormField<int>(
                  labelText: 'صفحات الورد',
                  margin: const EdgeInsets.only(top: 8),
                  items: [
                    ToggleItem(label: 'المصحف كامل', value: 0),
                    ToggleItem(label: 'مخصص', value: 1),
                  ],
                  onChanged: (value) {
                    werdPageType.value = value;
                  },
                  initialValue: werdPageType.value,
                  onSave: (value) {
                    if (werdPageType.value == 0) {
                      werdToSave.startPage = 1;
                      werdToSave.endPage = 604;
                    }
                  },
                ),
                ValueListenableBuilder<int>(
                    valueListenable: werdPageType,
                    builder: (context, value, child) {
                      if (value == 1) {
                        return Row(
                          children: [
                            Expanded(
                              child: FilledTextFormField(
                                margin: const EdgeInsets.only(top: 8),
                                hintText: 'من الصفحة',
                                labelText: 'من',
                                initialValue: werd?.startPage.toString(),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  NumericalRangeFormatter(max: 604, min: 1)
                                ],
                                validator: (value) {
                                  if (value == null || value.trim() == "") {
                                    return 'هذا الحقل مطلوب';
                                  } else {
                                    return null;
                                  }
                                },
                                onChanged: (value) {
                                  werdPageStart = int.parse(value);
                                },
                                onSaved: (value) {
                                  werdToSave.startPage =
                                      int.tryParse(value ?? "") ?? 1;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: FilledTextFormField(
                                margin: const EdgeInsets.only(top: 8),
                                hintText: 'إلى الصفحة',
                                labelText: 'إلى',
                                keyboardType: TextInputType.number,
                                initialValue: werd?.endPage.toString(),
                                inputFormatters: [
                                  NumericalRangeFormatter(max: 604, min: 1)
                                ],
                                validator: (value) {
                                  if (value == null || value.trim() == "") {
                                    return 'هذا الحقل مطلوب';
                                  } else if (werdPageStart > int.parse(value)) {
                                    return "الصفحة الأولى يجب أن تكون أقل من الصفحة الثانية";
                                  } else {
                                    return null;
                                  }
                                },
                                onSaved: (value) {
                                  werdToSave.endPage =
                                      int.tryParse(value ?? "") ?? 604;
                                },
                              ),
                            ),
                          ],
                        );
                      } else {
                        return const SizedBox();
                      }
                    }),
                ToggleFormField<int>(
                  labelText: 'المعدل اليومي',
                  margin: const EdgeInsets.only(top: 8),
                  items: [
                    ToggleItem(label: 'صفحة', value: 0),
                    ToggleItem(label: 'ورقة', value: 1),
                    ToggleItem(label: 'مخصص', value: 2),
                  ],
                  onChanged: (value) {
                    werdDailyPageType.value = value;
                  },
                  onSave: (value) {
                    if (werdDailyPageType.value != 2) {
                      werdToSave.perPage = value == 0 ? 2 : 1;
                    }
                  },
                  initialValue: werdDailyPageType.value,
                ),
                ValueListenableBuilder<int>(
                    valueListenable: werdDailyPageType,
                    builder: (context, value, child) {
                      if (value == 2) {
                        return CounterFormField(
                          labelText: 'عدد الصفحات يومياً:',
                          initialValue: werd?.perPage,
                          margin: const EdgeInsets.only(top: 16, bottom: 16),
                          max: 604,
                          min: 1,
                          onSave: (value) {
                            werdToSave.perPage = value ?? 1;
                          },
                        );
                      } else {
                        return const SizedBox();
                      }
                    }),
                const Divider(
                  height: 1,
                ),
                CustomSwitchFormField(
                  margin: const EdgeInsets.only(top: 16, bottom: 16),
                  labelText: 'تفعيل التنبهات',
                  onChanged: (value) {
                    alarm.value = value;
                  },
                  onSaved: (value) {
                    werdToSave.withAlarm = value ?? false;
                  },
                  initialValue: alarm.value,
                ),
                ValueListenableBuilder<bool>(
                    valueListenable: alarm,
                    builder: (context, value, child) {
                      if (value) {
                        return TimePickerFormField(
                          margin: const EdgeInsets.only(bottom: 16),
                          labelText: 'وقت التنبه',
                          initialValue: werd?.alarmTime,
                          onSaved: (value) {
                            werdToSave.alarmTime =
                                value?.add(const Duration(seconds: 3));
                          },
                        );
                      } else {
                        return const SizedBox();
                      }
                    }),
                const Divider(
                  height: 1,
                ),
                SizedBox(
                  width: double.maxFinite,
                  child: CustomFilledButton(
                    textStyle: const TextStyle(
                      fontSize: 16,
                    ),
                    margin: const EdgeInsets.only(top: 16),
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 32,
                    ),
                    text: "حفظ",
                    color: Get.theme.primaryColor,
                    onPressed: () {
                      if (formKey.currentState!.validate()) {
                        formKey.currentState?.save();

                        werdToSave.currentPage = werdToSave.startPage;

                        werdToSave.startDate = DateTime.now();
                        werdToSave.endDate = DateTime.now()
                            .add(Duration(days: (604 ~/ werdToSave.perPage)));

                        controller.manageWerd(werdToSave);
                        Get.back();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

class TimePickerFormField extends StatelessWidget {
  final String? labelText;
  final DateTime? initialValue;
  final Function(DateTime?)? onSaved;
  final Function(DateTime?)? onChanged;
  final EdgeInsetsGeometry? margin;
  const TimePickerFormField({
    Key? key,
    this.labelText,
    this.initialValue,
    this.onSaved,
    this.margin,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (labelText != null)
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsetsDirectional.only(start: 8.0, bottom: 4),
                child: Text(
                  labelText!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          const SizedBox(
            width: 8,
          ),
          Container(
            decoration: BoxDecoration(
              color: Get.theme.primaryColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: FormField<DateTime>(
              onSaved: onSaved,
              initialValue: initialValue ?? DateTime.now(),
              builder: (field) {
                return InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    // show time picker
                    showTimePicker(
                      context: context,
                      initialTime:
                          field.value?.toTimeOfDay() ?? TimeOfDay.now(),
                    ).then((value) {
                      if (value != null) {
                        field.didChange(value.toDateTime());
                        onChanged?.call(value.toDateTime());
                      }
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 8.0, horizontal: 16),
                    child: Text(
                      field.value?.format("KK:mm a") ?? '',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CustomSwitchFormField extends StatelessWidget {
  final String? labelText;
  final bool? initialValue;
  final Function(bool?)? onSaved;
  final Function(bool)? onChanged;
  final EdgeInsetsGeometry? margin;
  final TextStyle? style;
  final double? iconWidth;
  final double? iconheight;
  const CustomSwitchFormField({
    Key? key,
    this.labelText,
    this.initialValue,
    this.onSaved,
    this.margin,
    this.onChanged,
    this.style,
    this.iconWidth,
    this.iconheight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (labelText != null)
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsetsDirectional.only(start: 8.0, bottom: 4),
                child: Text(
                  labelText!,
                  style: style ??
                      const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ),
          const SizedBox(
            width: 8,
          ),
          FormField<bool>(
            onSaved: onSaved,
            initialValue: initialValue ?? false,
            builder: (field) {
              return FlutterSwitch(
                  activeColor: CustomColors.accentColor,
                  value: field.value ?? false,
                  height: iconheight ?? 35.0,
                  width: iconheight ?? 70.0,
                  onToggle: (value) {
                    field.didChange(value);
                    onChanged?.call(value);
                  });
            },
          ),
        ],
      ),
    );
  }
}

class CounterFormField extends StatelessWidget {
  final String? labelText;
  final int? initialValue;
  final int max;
  final int min;
  final Function(int?)? onSave;
  final EdgeInsetsGeometry? margin;

  const CounterFormField({
    Key? key,
    this.initialValue,
    required this.max,
    required this.min,
    this.onSave,
    this.margin,
    this.labelText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (labelText != null)
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsetsDirectional.only(start: 8.0, bottom: 4),
                child: Text(
                  labelText!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Get.theme.primaryColor.withOpacity(0.15),
              ),
              child: FormField<int>(
                initialValue: initialValue ?? min,
                onSaved: onSave,
                builder: (field) {
                  return Row(
                    children: [
                      InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: field.value == max
                            ? null
                            : () {
                                var value = field.value ?? max;
                                if (value < max) {
                                  field.didChange(value + 1);
                                }
                              },
                        child: const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Icon(
                            Icons.add_rounded,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          field.value.toString(),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: field.value == min
                            ? null
                            : () {
                                var value = field.value ?? min;
                                if (value > min) {
                                  field.didChange(value - 1);
                                }
                              },
                        child: const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Icon(
                            Icons.remove_rounded,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
