import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/werd_controller.dart';
import 'package:tadars/helpers/function_helper.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/routes.dart';

import '../../../models/hive/werd.dart';

class WerdsPage extends GetView<WerdController> {
  const WerdsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<WerdController>(WerdController());

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الورود اليومية'),
          bottom: const TabBar(
            tabs: [
              Tab(
                text: 'ورد التلاوة',
              ),
              Tab(
                text: 'ورد الحفظ',
              ),
            ],
          ),
        ),
        body: ValueListenableBuilder<Box<Werd>>(
          valueListenable: Hive.box<Werd>(Boxes.werds).listenable(),
          builder: (context, value, child) => TabBarView(
            physics: const NeverScrollableScrollPhysics(),
            children: [
              WerdListView(
                items:
                    value.values.where((element) => element.type == 0).toList(),
              ),
              WerdListView(
                items:
                    value.values.where((element) => element.type == 1).toList(),
              ),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Get.toNamed(Routes.manageWerdPage);
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}

class WerdListView extends StatelessWidget {
  final List<Werd> items;
  const WerdListView({
    Key? key,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return items.isNotEmpty
        ? ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return ListTile(
                title: Text(item.name),
                subtitle: Text(
                    "من ${FunctionHelper.getDateAsString(item.startDate)} إلى ${FunctionHelper.getDateAsString(item.endDate)} بمعدل ${item.perPage} يوميا"),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Get.toNamed(Routes.manageWerdPage, arguments: item);
                },
                onLongPress: () {
                  WerdController.to.deleteWrd(item.id);
                },
              );
            },
          )
        : const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('لايوجد أي ورد حاليا'),
                // const SizedBox(height: 16),
                // IconCustomFilledButton(
                //   color: Get.theme.colorScheme.secondary,
                //   text: "اضافة جديد",
                //   onPressed: () {},
                //   icon: Icons.add,
                // ),
              ],
            ),
          );
  }
}
