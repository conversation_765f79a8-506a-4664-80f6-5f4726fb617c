import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/dialogs/show_tafseer_dialog.dart';
import '../../../../../utils/extensions.dart';
import '../../../controllers/tafseer_search_controller.dart';
import '../../../models/hive/tafseer.dart';
import '../home/<USER>/drawers/search_text_field.dart';

class TafseerSearchPage extends GetView<TafseerSearchController> {
  const TafseerSearchPage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    Get.put(TafseerSearchController());
    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: context.theme.scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(8),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.arrow_back,
                        color: CustomColors.primaryColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Obx(
                      () => SearchTextField(
                        controller: TextEditingController.fromValue(
                          TextEditingValue(
                            text: controller.searchKey.value,
                          ),
                        ),
                        margin: EdgeInsets.zero,
                        hintText: "البحث في التفاسير المحملة".tr,
                        fillColor: CustomColors.primaryColor.withOpacity(0.2),
                        onSubmitted: (value) {
                          controller.searchKey.value = value.trim();
                          if (value.trim().isNotEmpty) {
                            BoxesHelper.addToTafseerSearchHistory(value.trim());
                            controller.search(value.trim().arabicNormalize);
                          }
                        },
                        onChanged: (value) {
                          if (value.trim().isEmpty) {
                            controller.tafseers.value = null;

                            controller.tafseers.refresh();
                          }
                        },
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        "الغاء".tr,
                        style: TextStyle(
                          color: CustomColors.primaryColor,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: Obx(
                () {
                  var downloadedTafseers = controller.downloadedTafseers;

                  if (controller.searchKey.value.isNotEmpty) {
                    return DefaultTabController(
                      length: downloadedTafseers.length,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        color: Get.theme.primaryColor.withOpacity(0.005),
                        child: Column(
                          children: [
                            TabBar(
                              onTap: (value) {},
                              isScrollable: downloadedTafseers.length > 1,
                              tabs: downloadedTafseers.map((e) {
                                var tafseerBook =
                                    BoxesHelper.getTafseerByName(e);
                                return Tab(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(tafseerBook?.title ?? ""),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ),
                            Expanded(
                              child: TabBarView(
                                physics: const NeverScrollableScrollPhysics(),
                                children: downloadedTafseers.map((e) {
                                  return FutureBuilder<List<Tafseer>>(
                                      future: controller.searchInTafseer(e),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                            ConnectionState.waiting) {
                                          return const Center(
                                            child: CircularProgressIndicator(),
                                          );
                                        }
                                        List<Tafseer> tafseer =
                                            snapshot.data ?? [];
                                        print(tafseer.length);
                                        return Container(
                                          child: ListView.builder(
                                            itemCount: tafseer.length,
                                            // prototypeItem: ListTile(
                                            //   title:
                                            //       Text(tafseers.first.text),
                                            // ),
                                            itemBuilder: (context, index) {
                                              // Get.log(CommonFunctions
                                              //         .parseHtmlString(
                                              //             tafseer[index].text)
                                              //     .replaceAll('\n', '')
                                              //     .replaceAll('  ', ' '));
                                              return Column(
                                                children: [
                                                  ListTile(
                                                    onTap: () {
                                                      Get.dialog(ShowTafseerDialog(
                                                          tafseer:
                                                              tafseer[index],
                                                          tafseerBook: BoxesHelper
                                                              .getTafseerByName(
                                                                  e)));
                                                    },
                                                    trailing: Icon(
                                                      Icons.chevron_right,
                                                      color: Get
                                                          .theme
                                                          .colorScheme
                                                          .secondary,
                                                    ),
                                                    title: Text(
                                                      CommonFunctions
                                                          .stripeHtmlTags(
                                                              tafseer[index]
                                                            .text
                                                            .replaceAll(
                                                                '\n', '')
                                                            .replaceAll(
                                                                '  ', ' ')
                                                            .trim(),
                                                      ),
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                          
                                                    ),
                                                    subtitle: Text(
                                                        '[${BoxesHelper.getSuraById(tafseer[index].suraNumber)?.nameWithDiac} - ${tafseer[index].verseNumber.toArabicNumber()}]'),
                                                  ),
                                                  const Divider(),
                                                ],
                                              );
                                            },
                                          ),
                                        );
                                      });
                                }).toList(),
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  } else {
                    
                    return Container(
                      margin: const EdgeInsets.only(top: 8),
                      color: Colors.transparent,
                      child: ValueListenableBuilder(
                        valueListenable:
                            Hive.box<String>(Boxes.tafseerSearchHistory)
                                .listenable(),
                        builder: (context, Box<String> box, widget) {
                          var values = box.values.toList().reversed.toList();
                          return ListView.builder(
                            shrinkWrap: true,
                            itemCount: box.length,
                            itemBuilder: (context, i) {
                              return Column(
                                children: [
                                  ListTile(
                                    onTap: () {
                                      if (values[i].trim().isNotEmpty) {
                                        controller.searchKey.value =
                                            values[i].trim();
                                        controller.search(
                                            values[i].trim().arabicNormalize);
                                      }
                                    },
                                    leading: const Icon(
                                      QuranUIIcons.clock,
                                    ),
                                    title: Text(values[i]),
                                    trailing: InkWell(
                                      onTap: () {
                                        Get.defaultDialog(
                                          title: "تأكيد الحذف".tr,
                                          middleText:
                                              "هل أنت متأكد من الحذف؟".tr,
                                          textConfirm: "نعم".tr,
                                          textCancel: "لا".tr,
                                          onConfirm: () {
                                            Get.back();
                                            if (kDebugMode) {
                                              print((values.length - i) - 1);
                                            }
                                            box.deleteAt(
                                                (values.length - i) - 1);
                                          },
                                          confirmTextColor: Colors.white,
                                        );
                                      },
                                      child: const Icon(
                                        Icons.close,
                                        // color: Colors.redAccent,
                                      ),
                                    ),
                                  ),
                                  if (i + 1 < values.length)
                                    const Divider(
                                      height: 1,
                                    ),
                                ],
                              );
                            },
                          );
                        },
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class VerseCard extends StatelessWidget {
  const VerseCard({
    Key? key,
    required this.item,
    this.margin,
  }) : super(key: key);
  final QuranVerse item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var suraName = Get.locale?.languageCode == "ar"
        ? Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.name
        : Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.nameEn;

    return Container(
      height: 70,
      margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: CommonStyles.borderRadius,
        color: Get.theme.scaffoldBackgroundColor,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: CommonStyles.borderRadius,
        child: ListTile(
          shape: RoundedRectangleBorder(
            borderRadius: CommonStyles.borderRadius,
          ),
          onTap: () async {
            Get.back();
            var homeController = Get.find<HomeController>();
            homeController.selectedSuraNumber.value = item.suraNumber;
            homeController.selectedVerseNumber.value = item.verseNumber;
            await Future.delayed(const Duration(milliseconds: 300));

            homeController.goToPage(item.pageNumber - 1);
          },
          dense: true,
          trailing: Container(
            height: 28,
            width: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CustomColors.accentColor.withOpacity(0.1),
            ),
            child: Text(
              item.pageNumber.toLocaleNumber(),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontFamily: CommonConstants.numbersFontFamily,
                height: 1.5,
                color: CustomColors.accentColor,
                fontSize: 16,
              ),
            ),
          ),
          title: Text(
            "${suraName ?? ""} - ${"الاية رقم".tr} ${Get.locale?.languageCode == "ar" ? item.verseNumber.toArabicNumber() : item.verseNumber}",
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            item.verseWithDiac,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
