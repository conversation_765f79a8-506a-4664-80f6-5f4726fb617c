import 'package:flutter/material.dart' hide SearchController;
import 'package:flutter/foundation.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/search_controller.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import '../../../../../utils/extensions.dart';
import '../home/<USER>/drawers/search_text_field.dart';
import '../home/<USER>/drawers/sura_card.dart';

class SearchPage extends GetView<SearchController> {
  const SearchPage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    Get.put(SearchController());
    return DefaultStickyHeaderController(
      child: Scaffold(
        backgroundColor: Get.theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: context.theme.scaffoldBackgroundColor,
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(8),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      spreadRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.arrow_back,
                          color: CustomColors.primaryColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Obx(
                        () => SearchTextField(
                          controller: TextEditingController.fromValue(
                            TextEditingValue(
                              text: controller.searchKey.value,
                            ),
                          ),
                          margin: EdgeInsets.zero,
                          hintText: "البحث في نصوص الآيات وأسماء السور".tr,
                          fillColor: CustomColors.primaryColor.withOpacity(0.2),
                          onSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              BoxesHelper.addToSearchHistory(value.trim());
                              controller.search(value.trim().arabicNormalize);
                            }
                          },
                          onChanged: (value) {
                            if (value.trim().isEmpty) {
                              controller.verses.value = null;
                              controller.suars.value = null;
                              controller.verses.refresh();
                              controller.suars.refresh();
                            }
                          },
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          "الغاء".tr,
                          style: TextStyle(
                            color: CustomColors.primaryColor,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                child: Obx(
                  () {
                    var verses = controller.verses.value ?? [];
                    var suar = controller.suars.value ?? [];
                    if (controller.suars.value != null &&
                        controller.verses.value != null) {
                      return Container(
                        // padding: const EdgeInsets.symmetric(vertical: 8),
                        color: Get.theme.primaryColor.withOpacity(0.005),
                        child: CustomScrollView(
                          slivers: [
                            if (suar.isNotEmpty)
                              SliverStickyHeader(
                                header: Container(
                                  color: Get.theme.scaffoldBackgroundColor,
                                  margin: const EdgeInsets.only(bottom: 8),
                                  child: Container(
                                    color:
                                        Get.theme.primaryColor.withOpacity(0.5),
                                    padding: const EdgeInsets.all(4),
                                    width: double.maxFinite,
                                    child: Text(
                                      "${"السور".tr} : ${(suar.length).toLocaleNumber()} ${"من النتائج".tr}",
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                                sliver: SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                    (BuildContext context, int index) {
                                      // return SizedBox();
                                      return SuraCard(
                                        index: index,
                                        item: Surah(
                                          number: suar[index].pageNumber,
                                          name: suar[index].nameWithDiac,
                                          searchableName: suar[index].name,
                                          unicode: suar[index].unicode,
                                          surahType: suar[index].isMakkiah == 1
                                              ? SurahType.makki
                                              : SurahType.madani,
                                          versesCount: suar[index].versesCount,
                                          pageNumber: suar[index].pageNumber,
                                          // partNumber:  suar[index].
                                        ),
                                        // pageNumber: suar[index].pageNumber,
                                        // title: suar[index].name,

                                        // subTitle:
                                        //     "${"رقمها".tr} ${suar[index].id.toLocaleNumber()} - ${"آياتها".tr} ${suar[index].versesCount.toLocaleNumber()} - ${suar[index].isMakkiah == 1 ? "مكيّة".tr : "مدنيّة".tr}",
                                        // suraNumber: suar[index].id,
                                      );
                                    },
                                    childCount: suar.length,
                                  ),
                                ),
                              ),
                            if (verses.isNotEmpty)
                              SliverStickyHeader(
                                header: Container(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  color: Get.theme.scaffoldBackgroundColor,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    color:
                                        Get.theme.primaryColor.withOpacity(0.3),
                                    width: double.maxFinite,
                                    child: Text(
                                      "${"النصوص القرآنية".tr} : ${(controller.verses.value?.length ?? 0).toLocaleNumber()} ${"من النتائج".tr}",
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                                sliver: SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                    (BuildContext context, int index) {
                                      return VerseCard(
                                        item: verses[index],
                                      );
                                    },
                                    childCount: verses.length,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    } else {
                      return Container(
                        color: Colors.transparent,
                        child: ValueListenableBuilder(
                          valueListenable: Hive.box<String>(Boxes.searchHistory)
                              .listenable(),
                          builder: (context, Box<String> box, widget) {
                            var values = box.values.toList().reversed.toList();
                            return ListView.builder(
                              shrinkWrap: true,
                              itemCount: box.length,
                              itemBuilder: (context, i) {
                                return Column(
                                  children: [
                                    ListTile(
                                      onTap: () {
                                        if (values[i].trim().isNotEmpty) {
                                          controller.searchKey.value =
                                              values[i].trim();
                                          controller.search(
                                              values[i].trim().arabicNormalize);
                                        }
                                      },
                                      leading: const Icon(
                                        QuranUIIcons.clock,
                                      ),
                                      title: Text(values[i]),
                                      trailing: InkWell(
                                        onTap: () {
                                          Get.defaultDialog(
                                            title: "تأكيد الحذف".tr,
                                            middleText:
                                                "هل أنت متأكد من الحذف؟".tr,
                                            textConfirm: "نعم".tr,
                                            textCancel: "لا".tr,
                                            onConfirm: () {
                                              Get.back();
                                              if (kDebugMode) {
                                                print((values.length - i) - 1);
                                              }
                                              box.deleteAt(
                                                  (values.length - i) - 1);
                                            },
                                            confirmTextColor: Colors.white,
                                          );
                                        },
                                        child: const Icon(
                                          Icons.close,
                                          // color: Colors.redAccent,
                                        ),
                                      ),
                                    ),
                                    if (i + 1 < values.length)
                                      const Divider(
                                        height: 1,
                                      ),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class VerseCard extends StatelessWidget {
  const VerseCard({
    Key? key,
    required this.item,
    this.margin,
  }) : super(key: key);
  final QuranVerse item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var suraName = Get.locale?.languageCode == "ar"
        ? Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.name
        : Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.nameEn;

    return Container(
      height: 70,
      margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: CommonStyles.borderRadius,
        color: Get.theme.scaffoldBackgroundColor,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: CommonStyles.borderRadius,
        child: ListTile(
          shape: RoundedRectangleBorder(
            borderRadius: CommonStyles.borderRadius,
          ),
          onTap: () async {
            Get.back();
            var homeController = Get.find<HomeController>();
            homeController.selectedSuraNumber.value = item.suraNumber;
            homeController.selectedVerseNumber.value = item.verseNumber;
            await Future.delayed(const Duration(milliseconds: 300));

            homeController.goToVerse(item.suraNumber, item.verseNumber);
          },
          dense: true,
          trailing: Container(
            height: 28,
            width: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CustomColors.accentColor.withOpacity(0.1),
            ),
            child: Text(
              item.pageNumber.toLocaleNumber(),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontFamily: CommonConstants.numbersFontFamily,
                height: 1.5,
                color: CustomColors.accentColor,
                fontSize: 16,
              ),
            ),
          ),
          title: Text(
            "${suraName ?? ""} - ${"الاية رقم".tr} ${Get.locale?.languageCode == "ar" ? item.verseNumber.toArabicNumber() : item.verseNumber}",
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            item.verseWithDiac,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
