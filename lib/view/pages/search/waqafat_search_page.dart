import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/waqafat_search_controller.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/dialogs/show_tdbr_dialog.dart';
import '../../../../../utils/extensions.dart';
import '../home/<USER>/drawers/search_text_field.dart';

class WaqafatSearchPage extends GetView<WaqafatSearchController> {
  const WaqafatSearchPage({super.key});
  @override
  Widget build(BuildContext context) {
    Get.put(WaqafatSearchController());
    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: context.theme.scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(8),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.arrow_back,
                        color: CustomColors.primaryColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Obx(
                      () => SearchTextField(
                        controller: TextEditingController.fromValue(
                          TextEditingValue(
                            text: controller.searchKey.value,
                          ),
                        ),
                        margin: EdgeInsets.zero,
                        hintText: "البحث في الوقفات".tr,
                        fillColor: CustomColors.primaryColor.withOpacity(0.2),
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty) {
                            BoxesHelper.addToWaqafatSearchHistory(value.trim());
                            controller.search(value.trim());
                          }
                        },
                        onChanged: (value) {
                          if (value.trim().isEmpty) {
                            controller.tdbrs.value = null;
                            controller.tdbrs.refresh();
                          }
                        },
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        "الغاء".tr,
                        style: TextStyle(
                          color: CustomColors.primaryColor,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: Obx(
                () {
                  if (controller.loading.value) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  if (controller.tdbrs.value != null) {
                    var tdbrs = controller.tdbrs.value;
                    var filteredTypes = tdbrs?.keys
                            .where((key) => (tdbrs[key]['count'] ?? 0) > 0)
                            .toList() ??
                        [];
                    print(filteredTypes);
                    return DefaultTabController(
                      length: filteredTypes.length,
                      child: Container(
                        margin: const EdgeInsets.only(top: 4),
                        child: Column(children: [
                          TabBar(
                            isScrollable: filteredTypes.length > 3,
                            tabs: filteredTypes.map((e) {
                              return Tab(
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(e.name.tr),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                      Container(
                                        height: 20,
                                        alignment: AlignmentDirectional.center,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        decoration: BoxDecoration(
                                            color:
                                                Get.theme.colorScheme.secondary,
                                            borderRadius:
                                                BorderRadius.circular(16)),
                                        child: Text(
                                          tdbrs?[e]['count'].toString() ?? '',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 9,
                                            height: 1,
                                          ),
                                        ),
                                      ),
                                    ]),
                              );
                            }).toList(),
                          ),
                          Expanded(
                            child: TabBarView(
                                physics: const NeverScrollableScrollPhysics(),
                                children: filteredTypes.map((key) {
                                  return ListView.builder(
                                      padding: const EdgeInsets.only(top: 4),
                                      itemCount:
                                          tdbrs?[key]['result'].length ?? 0,
                                      itemBuilder: (context, index) {
                                        return Column(
                                          children: [
                                            ListTile(
                                              onTap: () {
                                                Get.dialog(ShowTdbrDialog(
                                                  tdbrId: tdbrs![key]['result']
                                                      [index]['id'],
                                                  tdbrType: key,
                                                ));
                                              },
                                              trailing: Icon(
                                                Icons.chevron_right,
                                                color: Get.theme.colorScheme
                                                    .secondary,
                                              ),
                                              title: Text(
                                                key == TdbrType.media
                                                    ? tdbrs![key]['result']
                                                        [index]['title']
                                                    : tdbrs?[key]['result']
                                                                [index]
                                                            ['fulltext'] ??
                                                        "",
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            const Divider()
                                          ],
                                        );
                                      });
                                }).toList()),
                          ),
                        ]),
                      ),
                    );
                    return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        color: Get.theme.primaryColor.withOpacity(0.005),
                        height: double.maxFinite,
                        child: SingleChildScrollView(
                          child: ExpansionPanelList(
                            children: tdbrs?.keys
                                    .map((key) => ExpansionPanel(
                                        isExpanded: false,
                                        headerBuilder: (context, bool) {
                                          return Text(
                                              "${key.name.tr} (${tdbrs[key]['count']})");
                                        },
                                        body: ListView.builder(
                                            shrinkWrap: true,
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            itemCount:
                                                tdbrs[key]['result'].length,
                                            itemBuilder: (context, index) {
                                              return Column(
                                                children: [
                                                  ListTile(
                                                    trailing: Icon(
                                                      Icons.chevron_right,
                                                      color: Get
                                                          .theme
                                                          .colorScheme
                                                          .secondary,
                                                    ),
                                                    title: Text(
                                                      key == TdbrType.media
                                                          ? tdbrs[key]['result']
                                                              [index]['details']
                                                          : tdbrs[key]['result']
                                                                      [index][
                                                                  'fulltext'] ??
                                                              "",
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                  const Divider()
                                                ],
                                              );
                                            })))
                                    .toList() ??
                                [],
                          ),
                        )

                        // ListView(
                        //   children: tdbrs?.keys.map(
                        //         (e) {
                        //           if (tdbrs[e]['count'] > 0) {
                        //             var result = (tdbrs[e]['result'] as List);
                        //             return ExpansionTile(
                        //                 title: Text(
                        //                     '${e.name.tr} (${tdbrs[e]['count']})'),
                        //                 children: <Widget>[
                        //                   ListView.builder(
                        //                       shrinkWrap: true,
                        //                       physics:
                        //                           const NeverScrollableScrollPhysics(),
                        //                       itemCount: result.length,
                        //                       itemBuilder: (context, index) {
                        //                         return Column(
                        //                           children: [
                        //                             ListTile(
                        //                               trailing: Icon(
                        //                                 Icons.chevron_right,
                        //                                 color: Get
                        //                                     .theme
                        //                                     .colorScheme
                        //                                     .secondary,
                        //                               ),
                        //                               title: Text(
                        //                                 e == TdbrType.media
                        //                                     ? result[index]
                        //                                         ['details']
                        //                                     : result[index][
                        //                                             'fulltext'] ??
                        //                                         "",
                        //                                 maxLines: 2,
                        //                                 overflow:
                        //                                     TextOverflow.ellipsis,
                        //                               ),
                        //                             ),
                        //                             const Divider()
                        //                           ],
                        //                         );
                        //                       })
                        //                 ]);
                        //           } else {
                        //             return const SizedBox(
                        //               height: 0,
                        //             );
                        //           }
                        //         },
                        //       ).toList() ??
                        //       [],
                        // ),

                        );
                  } else {
                    return Container(
                      margin: const EdgeInsets.only(top: 8),
                      color: Colors.transparent,
                      child: ValueListenableBuilder(
                        valueListenable:
                            Hive.box<String>(Boxes.waqafatSearchHistory)
                                .listenable(),
                        builder: (context, Box<String> box, widget) {
                          var values = box.values.toList().reversed.toList();
                          return ListView.builder(
                            shrinkWrap: true,
                            itemCount: box.length,
                            itemBuilder: (context, i) {
                              return Column(
                                children: [
                                  ListTile(
                                    onTap: () {
                                      if (values[i].trim().isNotEmpty) {
                                        controller.searchKey.value =
                                            values[i].trim();
                                        controller.search(
                                            values[i].trim().arabicNormalize);
                                      }
                                    },
                                    leading: const Icon(
                                      QuranUIIcons.clock,
                                    ),
                                    title: Text(values[i]),
                                    trailing: InkWell(
                                      onTap: () {
                                        Get.defaultDialog(
                                          title: "تأكيد الحذف".tr,
                                          middleText:
                                              "هل أنت متأكد من الحذف؟".tr,
                                          textConfirm: "نعم".tr,
                                          textCancel: "لا".tr,
                                          onConfirm: () {
                                            Get.back();
                                            if (kDebugMode) {
                                              print((values.length - i) - 1);
                                            }
                                            box.deleteAt(
                                                (values.length - i) - 1);
                                          },
                                          confirmTextColor: Colors.white,
                                        );
                                      },
                                      child: const Icon(
                                        Icons.close,
                                        // color: Colors.redAccent,
                                      ),
                                    ),
                                  ),
                                  if (i + 1 < values.length)
                                    const Divider(
                                      height: 1,
                                    ),
                                ],
                              );
                            },
                          );
                        },
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class VerseCard extends StatelessWidget {
  const VerseCard({
    Key? key,
    required this.item,
    this.margin,
  }) : super(key: key);
  final QuranVerse item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var suraName = Get.locale?.languageCode == "ar"
        ? Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.name
        : Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.nameEn;

    return Container(
      height: 70,
      margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: CommonStyles.borderRadius,
        color: Get.theme.scaffoldBackgroundColor,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: CommonStyles.borderRadius,
        child: ListTile(
          shape: RoundedRectangleBorder(
            borderRadius: CommonStyles.borderRadius,
          ),
          onTap: () async {
            Get.back();
            var homeController = Get.find<HomeController>();
            homeController.selectedSuraNumber.value = item.suraNumber;
            homeController.selectedVerseNumber.value = item.verseNumber;
            await Future.delayed(const Duration(milliseconds: 300));

            homeController.goToPage(item.pageNumber - 1);
          },
          dense: true,
          trailing: Container(
            height: 28,
            width: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: CustomColors.accentColor.withOpacity(0.1),
            ),
            child: Text(
              item.pageNumber.toLocaleNumber(),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontFamily: CommonConstants.numbersFontFamily,
                height: 1.5,
                color: CustomColors.accentColor,
                fontSize: 16,
              ),
            ),
          ),
          title: Text(
            "${suraName ?? ""} - ${"الاية رقم".tr} ${Get.locale?.languageCode == "ar" ? item.verseNumber.toArabicNumber() : item.verseNumber}",
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            item.verseWithDiac,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
