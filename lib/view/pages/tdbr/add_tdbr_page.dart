import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:tadars/controllers/add_tdbr_controller.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/tadars_constants.dart';
import 'package:tadars/utils/quran_ui_icons2.dart';
import 'package:tadars/view/components/form_fields/custom_dropdown_form_field.dart';
import 'package:tadars/view/components/form_fields/custom_text_form_field.dart';

class AddTdbrPage extends StatefulWidget {
  const AddTdbrPage({Key? key}) : super(key: key);

  @override
  State<AddTdbrPage> createState() => _AddTdbrPageState();
}

class _AddTdbrPageState extends State<AddTdbrPage> {
  final formKey = GlobalKey<FormState>();

  // String text = "";
  Rx<Map<String, Object?>> selectedSource =
      Rx<Map<String, Object?>>({'id': 1, "name": "بدون مصدر"});

  var saveInSoraName = Rx<bool>(false);

  final Map<String, dynamic> data = {};

  final Map<String, dynamic> soraAyahInfo = Get.arguments;
  QuranVerse? verse;

  @override
  void initState() {
    Get.put(AddTdbrController());
    verse = Hive.box<QuranVerse>(Boxes.quranVerses).get(
      "${soraAyahInfo["suraNumber"].toString().padLeft(3, "0")}_${soraAyahInfo["verseNumber"].toString().padLeft(3, "0")}",
    );
    AddTdbrController.to.ayats.value.add({
      'sora_num': verse?.suraNumber,
      'ayah_num': verse?.verseNumber,
      'ayah_id': verse?.id
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        AddTdbrController.to.ayats.value.clear();
        return true;
      },
      child: Scaffold(
          appBar: AppBar(
            title: Text("إضافة وقفة جديدة".tr),
            centerTitle: true,
          ),
          body: SingleChildScrollView(
              child: Form(
                  key: formKey,
                  child: Container(
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                          borderRadius: CommonStyles.borderRadius),
                      margin: const EdgeInsets.symmetric(horizontal: 30),
                      child: Column(mainAxisSize: MainAxisSize.min, children: [
                        const SizedBox(
                          height: 10,
                        ),

                        Obx(() => saveInSoraName.value
                            ? const SizedBox.shrink()
                            : Column(
                                children: [
                                  CustomTextFormField(
                                    initialValue: verse?.verseWithDiac,
                                    readOnly: true,
                                    // onSave: (value) {
                                    //   data.addAll({"name": value?.trim()});
                                    // },
                                  ),

                                  // Row(

                                  const SizedBox(
                                    height: 7,
                                  ),

                                  SizedBox(
                                    // padding: const EdgeInsets.all(13),
                                    // decoration: BoxDecoration(
                                    //     borderRadius: CommonStyles.borderRadius,
                                    //     border:
                                    //         Border.all(color: Colors.grey, width: 0.1)),
                                    width: double.maxFinite,
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 5),
                                          child: const Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "عدد الايات",
                                                style: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                              Text(
                                                "إضافة اية",
                                                style: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                              Text(
                                                "إدارة الايات",
                                                style: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 3,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 16),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                                color: Colors.grey, width: 0.1),
                                            borderRadius:
                                                CommonStyles.borderRadius,
                                          ),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 20),
                                            child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Obx(() => Text(
                                                        AddTdbrController.to
                                                            .ayats.value.length
                                                            .toString(),
                                                        style: const TextStyle(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 18),
                                                      )),

                                                  // Text("إضافة روابط"),
                                                  InkWell(
                                                    onTap: () {
                                                      CommonFunctions
                                                          .showAddAyahDailog(
                                                              soraAyahInfo[
                                                                  "suraNumber"],
                                                              soraAyahInfo[
                                                                  "verseNumber"]);
                                                    },
                                                    child: Icon(
                                                      Icons.add,
                                                      size: 30,
                                                      color: CustomColors
                                                          .primaryColor,
                                                    ),
                                                  ),

                                                  InkWell(
                                                    onTap: () {
                                                      CommonFunctions
                                                          .showManageAyatDailog();
                                                    },
                                                    child: Icon(
                                                      QuranUIIcons2.bookOpened,
                                                      color: CustomColors
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )),

                        CustomDropDownFormField<String>(
                            titleText: "نوع الوقفة",
                            hintText: "نوع الوقفة",
                            validator: (value) {
                              if (value == null) {
                                return "يجب إختيار نوع الوقفة";
                              }
                              return null;
                            },
                            items: TadarsConstants.tdbrTranlation.entries
                                .map((e) => DropdownMenuItem(
                                    value: e.key, child: Text(e.value)))
                                .toList(),
                            onSaved: (value) {
                              data.addAll({'type': value});
                            }),
                        CustomTextFormField(
                          titleText: "نص الوقفة".tr,
                          hintText: "نص الوقفة".tr,
                          minLines: 4,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return "هذا الحقل مطلوب";
                            }
                            return null;
                          },
                          onSave: (value) {
                            data.addAll({"fulltext": value?.trim()});
                          },
                        ),

                        // CustomDropDownFormField(
                        //   value: 1,
                        //   items: Hive.box<TdbrSource>(Boxes.tdbrSources)
                        //       .values
                        //       .map((e) => DropdownMenuItem(
                        //             value: e.id,
                        //             child: Text(e.name),
                        //           ))
                        //       .toList(),
                        //   titleText: "مصدر الوقفة",
                        // ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                margin: const EdgeInsetsDirectional.only(
                                    start: 13.0, top: 16, bottom: 6),
                                child: const Text("مصدر الوقفة")),
                            Container(
                              padding: const EdgeInsets.all(13),
                              decoration: BoxDecoration(
                                  borderRadius: CommonStyles.borderRadius,
                                  border: Border.all(
                                      color: Colors.grey, width: 0.1)),
                              width: double.maxFinite,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Obx(() => Text(
                                      selectedSource.value["name"] as String)),
                                  InkWell(
                                      onTap: () async {
                                        var result = await CommonFunctions
                                            .showSourcesDailog();
                                        selectedSource.value = result ??
                                            {'id': 1, "name": "بدون مصدر"};
                                      },
                                      child: Icon(
                                        Icons.edit,
                                        color: CustomColors.primaryColor,
                                      ))
                                ],
                              ),
                            )
                          ],
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                margin: const EdgeInsetsDirectional.only(
                                    start: 13.0, top: 16, bottom: 6),
                                child: const Text("روابط المادة")),
                            SizedBox(
                              // padding: const EdgeInsets.all(13),
                              // decoration: BoxDecoration(
                              //     borderRadius: CommonStyles.borderRadius,
                              //     border:
                              //         Border.all(color: Colors.grey, width: 0.1)),
                              width: double.maxFinite,
                              child: Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                              child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                    "عدد الروابط".tr,
                                          style: TextStyle(
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                    "إضافة روابط".tr,
                                          style: TextStyle(
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                    "إدارة الروابط".tr,
                                          style: TextStyle(
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 3,
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey, width: 0.1),
                                      borderRadius: CommonStyles.borderRadius,
                                    ),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Obx(() => Text(
                                                  AddTdbrController
                                                      .to.links.value.length
                                                      .toString(),
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16),
                                                )),

                                            // Text("إضافة روابط"),
                                            InkWell(
                                              onTap: () {
                                                CommonFunctions
                                                    .showAddLinkDailog();
                                              },
                                              child: Icon(
                                                Icons.add_link_sharp,
                                                color:
                                                    CustomColors.primaryColor,
                                              ),
                                            ),

                                            InkWell(
                                              onTap: () {
                                                if (AddTdbrController.to.links
                                                    .value.isNotEmpty) {
                                                  CommonFunctions
                                                      .showManageLinksDailog();
                                                }
                                              },
                                              child: Icon(
                                                Icons.link,
                                                color:
                                                    CustomColors.primaryColor,
                                              ),
                                            ),
                                          ]),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        // CustomSwitchFormField(
                        //   initialValue: false,
                        //   iconheight: 30.0,
                        //   iconWidth: 40,
                        //   style: TextStyle(),
                        //   labelText: "حفظ الوقفة باسم سورة وليس اياتها",
                        // ),
                        Obx(() => SwitchListTile(
                            onChanged: (value) {
                              saveInSoraName.value = value;
                            },
                            value: saveInSoraName.value,
                            contentPadding: EdgeInsets.zero,
                            selectedTileColor: CustomColors.primaryColor,
                            activeColor: CustomColors.primaryColor,
                      title: Text("حفظ الوقفة باسم سورة وليس اياتها".tr),
                    ),
                  ),

                        const SizedBox(
                          height: 10,
                        ),

                        MaterialButton(
                          onPressed: () {
                            if (!formKey.currentState!.validate()) {
                              return;
                            }
                            formKey.currentState!.save();

                            if (!saveInSoraName.value) {
                              data.addAll(
                                  {"ayat": AddTdbrController.to.ayats.value});
                            }
                            if (AddTdbrController.to.links.value.isNotEmpty) {
                              data.addAll(
                                  {"links": AddTdbrController.to.links.value});
                            }
                            data.addAll({
                              "user": {
                                'uid': Hive.box(Boxes.settings).get("uid"),
                                'token':
                                    Hive.box(Boxes.settings).get("user_token"),
                              }
                            });
                            if (saveInSoraName.value) {
                              data.addAll(
                                  {"save_in_sora_name": verse?.suraNumber});
                            }
                            data.addAll(
                                {"source": selectedSource.value["id"] as int});
                            AddTdbrController.to.addTdbr(data);
                          },
                          shape: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: CommonStyles.borderRadius),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          minWidth: double.maxFinite,
                          color: CustomColors.primaryColor,
                    child: Text(
                      "ارسال الوقفة الى الإدارة لمراجعتها".tr,
                            style: TextStyle(color: Colors.white),
                          ),
                        ),

                        const SizedBox(
                          height: 20,
                        )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
