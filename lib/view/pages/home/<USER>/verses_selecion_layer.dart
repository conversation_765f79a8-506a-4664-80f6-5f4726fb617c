import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/hive/quran_page_line.dart';

class VersesSelectionLayer extends StatelessWidget {
  const VersesSelectionLayer({
    Key? key,
    required this.lines,
  }) : super(key: key);
  final List<QuranPageLine> lines;
  @override
  Widget build(BuildContext context) {
    print(lines.length);
    return Stack(
      children: List.generate(lines.length, (j) {
        return Positioned(
          left: HomeController.to.pageWidth * lines[j].x,
          top: HomeController.to.pageHeight * lines[j].y,
          child: Obx(
            () => Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: (lines[j].verseNumber ==
                            HomeController.to.selectedVerseNumber.value &&
                        lines[j].suraNumber ==
                            HomeController.to.selectedSuraNumber.value)
                    ? Get.isDarkMode
                        ? Get.theme.primaryColor.withOpacity(0.2)
                        : (HomeController.to.pageColor.value?.value ==
                                    Colors.white.value
                                ? Get.theme.primaryColor.withOpacity(0.2)
                            : HomeController.to.pageColor.value)
                        ?.withOpacity(0.3)
                    : null,
              ),
              width: HomeController.to.pageWidth * lines[j].width,
              height: HomeController.to.pageHeight * lines[j].height,
            ),
          ),
        );
      }).toList(),
    );
  }
}
