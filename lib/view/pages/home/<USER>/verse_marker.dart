import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/bookmark.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/quran_suar_icons.dart';
import 'package:tadars/utils/quran_ui_icons.dart';


class VerseMarker extends StatelessWidget {
  const VerseMarker({
    super.key,
    required this.verseNumber,
    this.size = 62,
    required this.suraNumber,
  });

  final int verseNumber;
  final int suraNumber;
  final double size;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: BoxesHelper.getListenableNotes(),
      builder: (context, box, child) {
        bool hasNote = BoxesHelper.hasNote(suraNumber, verseNumber);
        return ValueListenableBuilder(
          valueListenable: Hive.box<Bookmark>(Boxes.bookmarks).listenable(),
          builder: (context, box, child) {
            var bookmark = HomeController.to
                .getBookmark(suraNumber: suraNumber, verseNumber: verseNumber);
            return SizedBox(
              //color: Colors.green,
              height: size,
              width: size,
              child: Stack(
                clipBehavior: Clip.none,
                fit: StackFit.loose,
                children: [
                  Icon(
                    QuranSuarIcons.verseMarker,
                    size: size,
                    color: hasNote
                        ? CustomColors.primaryColor
                        : (bookmark != null
                                ? CustomColors.bookmarksColors[bookmark
                                    .colorIndex!]
                            : HomeController.to.isCustomColor.value
                                ? HomeController.to.customTextColor.value
                                : HomeController.to.pageColor.value ==
                                        Colors.white
                                ? null
                                : HomeController.to.pageColor.value),
                  ),
                  if (hasNote)
                    Center(
                      child: Icon(
                        QuranUIIcons.edit,
                        size: size * 0.35,
                        color: CustomColors.primaryColor,
                      ),
                    )
                  else
                    Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: 0.12 * size),
                        child: Text(
                          verseNumber.toArabicNumber(),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          style: TextStyle(
                            fontSize: size * 0.46,
                            fontFamily: CommonConstants.numbersFontFamily,
                            fontWeight: FontWeight.bold,
                            color: hasNote
                                ? CustomColors.accentColor
                                : (bookmark != null
                                    ? CustomColors
                                        .bookmarksColors[bookmark
                                            .colorIndex!]
                                    : HomeController.to.isCustomColor.value
                                        ? HomeController
                                            .to.customTextColor.value
                                        : HomeController.to.pageColor.value ==
                                            Colors.white
                                        ? null
                                        : HomeController.to.pageColor.value),
                          ),
                        ),
                      ),
                    ),
                  // ValueListenableBuilder(
                  //   valueListenable: BoxesHelper.getListenableShowWagafatCount(),
                  //   builder: (BuildContext context, Box box, Widget? child) {
                  //     if(!box.get(SettingsConstants.showWaqfatCountKey,
                  //         defaultValue: false)) {
                  //       return const SizedBox.shrink();
                  //     }
                  //     return  Obx(() {
                  //       int count = 0;
                  //       if (HomeController.to.waqafatCounts.value
                  //           .isNotEmpty) {
                  //         count = HomeController.to.waqafatCounts.value["${suraNumber.toString().padLeft(3, "0")}_${verseNumber.toString().padLeft(3, "0")}"]
                  //             ;
                  //       }
                  //       return Positioned(
                  //         top: -size * 0.50,
                  //         left: -5,
                  //         child: Container(
                  //           padding: EdgeInsets.all( 0.12 * size),
                  //           decoration: BoxDecoration(
                  //               color: HomeController.to.pageColor.value
                  //                   ?.withOpacity(0.1),
                  //               border: Border.all(
                  //                 color: CustomColors.primaryColor,
                  //               ),
                  //               shape: BoxShape.circle),
                  //           child: Center(
                  //             child: Text(
                  //               count.toArabicNumber(),
                  //               textAlign: TextAlign.center,
                  //               maxLines: 1,
                  //               style: TextStyle(
                  //                   fontSize: size * 0.35,
                  //                   fontFamily: CommonConstants.numbersFontFamily,
                  //                   fontWeight: FontWeight.bold,
                  //                   color: CustomColors.primaryColor),
                  //             ),
                  //           ),
                  //         ),
                  //       );

                  //     });
                  //   },
                  // ),
                
                ],
              ),
            );
          },
        );
      },
    );
  }
}
