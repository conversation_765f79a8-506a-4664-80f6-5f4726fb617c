import 'dart:math';

import 'package:flutter/material.dart';

import '../../../../controllers/home_controller.dart';

class AutoScrollActions extends StatefulWidget {
  const AutoScrollActions({super.key});

  @override
  State<AutoScrollActions> createState() => _AutoScrollActionsState();
}

class _AutoScrollActionsState extends State<AutoScrollActions> {
  bool isActive = false;
  bool isPuse = false;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isActive)
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  HomeController.to.quranPageController.startAutoScroll();

                  isActive =
                      HomeController.to.quranPageController.isAutoScrolling;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(shape: BoxShape.circle),
                child: Transform.rotate(
                  angle: pi / 2,
                  child: const Icon(Icons.double_arrow_outlined),
                ),
              ),
            ),
          ),
        if (isActive)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      HomeController.to.quranPageController.toggleAutoScroll();

                      isPuse = !HomeController
                          .to.quranPageController.isAutoScrolling;
                      // isActive =
                      //     HomeController.to.quranPageController.isAutoScrolling;
                    });
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                    child: Icon(isPuse
                        ? Icons.stop_circle_outlined
                        : Icons.pause_circle_outline_rounded),
                  ),
                ),
              ),
              // const SizedBox(
              //   width: 4,
              // ),
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    HomeController.to.quranPageController.stopAutoScroll();

                    setState(() {
                      HomeController.to.quranPageController.scrollDirection =
                          (Axis.horizontal);
                      // HomeController.to.quranPageController
                      //     .toggleScrollDirection();

                      isActive =
                          HomeController.to.quranPageController.isAutoScrolling;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                    child: const Icon(Icons.cancel_outlined),
                  ),
                ),
              ),
            ],
          )
      ],
    );
  }
}
