import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/bookmark.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/enums.dart';
import 'package:tadars/utils/quran_ui_icons.dart';

import 'custom_toggle_button.dart';

class PageHeader extends StatelessWidget {
  const PageHeader({
    super.key,
    required this.suraName,
    required this.pageNumber,
  });
  final String suraName;
  final int pageNumber;
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection:
          Get.locale!.languageCode == "ar"
              ? TextDirection.rtl
              : TextDirection.ltr,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              margin: const EdgeInsetsDirectional.only(start: 8),
              child: InkWell(
                onTap: () {
                  Scaffold.of(context).openDrawer();
                },
                child: Row(
                  children: [
                    Text(
                      suraName,
                      style: TextStyle(
                        fontFamily: CommonConstants.hafsFontFamily,
                        color:
                            HomeController.to.pageColor.value == Colors.white
                                ? Get.theme.primaryColor
                                : HomeController.to.pageColor.value,
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 4),
                      decoration: BoxDecoration(
                        color: (HomeController.to.pageColor.value ==
                                    Colors.white
                                ? Get.theme.primaryColor
                                : HomeController.to.pageColor.value)
                            ?.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Icon(
                        Icons.chevron_right_rounded,
                        color:
                            HomeController.to.pageColor.value == Colors.white
                                ? Get.theme.primaryColor
                                : HomeController.to.pageColor.value,
                        size: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Flexible(
              // flex: 5,
              child: Obx(() {
                return Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    borderRadius: CommonStyles.borderRadius,
                    color:
                        Get.isDarkMode
                            ? Get.theme.primaryColor
                            : HomeController.to.pageColor.value == Colors.white
                            ? Get.theme.primaryColor
                            : HomeController.to.pageColor.value,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomToggleButton(
                        key: const Key("quran"),
                        label: 'المصحف'.tr,
                        selected:
                            HomeController.to.viewMode.value == ViewMode.quran,
                        onTap: () {
                          HomeController.to.viewMode.value = ViewMode.quran;
                        },
                      ),
                      CustomToggleButton(
                        key: const Key("t"),
                        label: "المعاني".tr,
                        selected:
                            HomeController.to.viewMode.value ==
                            ViewMode.meanings,
                        onTap: () {
                          HomeController.to.viewMode.value = ViewMode.meanings;
                        },
                      ),
                      //   CustomToggleButton(
                      //   key: const Key("translation"),
                      //   label: 'الترجمة'.tr,
                      //   selected:
                      //       HomeController.to.viewMode.value ==
                      //       ViewMode.translation,
                      //   onTap: () {
                      //     HomeController.to.viewMode.value =
                      //         ViewMode.translation;
                      //   },
                      // ),
                      CustomToggleButton(
                        key: const Key("tadars"),
                        label: 'الوقفات'.tr,
                        selected:
                            HomeController.to.viewMode.value == ViewMode.tadars,
                        onTap: () {
                          HomeController.to.viewMode.value = ViewMode.tadars;
                        },
                      ),
                    ],
                  ),
                );
              }),
            ),
            Obx(
              () {
                // var hasBookmark = BoxesHelper.hasPageBookmark(pageNumber);

                var hasBookmark = UserSyncService.instance.bookmarks
                    .containsKey(UserSyncService.createPageKey(pageNumber));
                return Container(
                  alignment: AlignmentDirectional.centerEnd,
                  child: InkWell(
                    onTap: () {
                      if (kDebugMode) {
                        print("clicked");
                      }
                      !hasBookmark
                          ? UserSyncService.instance.addPageToBookmarks(
                              HomeController.to.currentPageNumber.value,
                          )
                          : UserSyncService.instance.deletePageFromBookmarks(
                              HomeController.to.currentPageNumber.value);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        hasBookmark
                            ? QuranUIIcons.bookmarkFilled
                            : QuranUIIcons.bookmark,
                        color:
                            HomeController.to.pageColor.value == Colors.white
                                ? Get.theme.primaryColor
                                : HomeController.to.pageColor.value,
                        size: 20,
                      ),
                    ),
                  ),
                );
              },
              // valueListenable: Hive.box<Bookmark>(Boxes.bookmarks).listenable(),
            ),
          ],
        ),
      ),
    );
  }
}
