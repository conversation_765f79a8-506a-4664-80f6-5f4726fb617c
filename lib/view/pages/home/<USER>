import 'dart:io';

import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
// import 'package:quran_core/quran_core.dart';
import 'package:tadars/controllers/tafseer_controller.dart';
import 'package:tadars/features/translation/translation.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/utils/constants/configs.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/enums.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/audio_player/player.dart';
import 'package:tadars/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:tadars/view/components/page_slider_wrapper.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/custom_drawer.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/custom_end_drawer.dart';
import 'package:tadars/view/pages/home/<USER>/page_views/meanings_page_view.dart';
import 'package:tadars/view/pages/home/<USER>/page_views/quran_page_view_wrapper.dart';
import '../../../controllers/home_controller.dart';
import '../../../controllers/setting_controller.dart';
import '../../components/custom_slider/times_slider.dart';
import 'components/page_views/tafseer_page_view.dart';
import 'components/page_views/waqafat_page_view.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});
  @override
  Widget build(BuildContext context) {
    final scaffoldKey = GlobalKey<ScaffoldState>();
    bool drawerOpened = false;
    bool endDrawerOpened = false;
    // Get.put(HomeController());
    Get.put(TafseerController());
    if (!Get.isRegistered<SettingController>()) {
      Get.put<SettingController>(SettingController());
    }
    return WillPopScope(
      onWillPop: () async {
        if (drawerOpened || endDrawerOpened) {
          Get.back(
            closeOverlays: false,
          );
          return false;
        } else {
          var result = await Get.defaultDialog<bool?>(
              title: "تأكيد الخروج".tr,
              middleText: "هل أنت متأكد من الخروج من التطبيق؟".tr,
              textConfirm: "نعم".tr,
              textCancel: "لا".tr,
              buttonColor: CustomColors.primaryColor,
              confirmTextColor: Colors.white,
              cancelTextColor: CustomColors.primaryColor,
              onConfirm: () {
                Get.back(result: true);
              },
              onCancel: () {
                Get.back(result: false);
              });
          return result ?? false;
        }
      },
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
        ),
        key: scaffoldKey,
        onDrawerChanged: (isOpened) {
          drawerOpened = isOpened;
        },
        onEndDrawerChanged: (isOpened) {
          endDrawerOpened = isOpened;
        },
        endDrawer: const CustomEndDrawer(),
        drawer: const CustomDrawer(),
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Stack(
            children: [
              Obx(() {
                if (SettingController.instance.quranBook.value.id == 0) {
                  if (controller.viewMode.value == ViewMode.quran) {
                    return const QuranPageViewWrapper();
                  } else if (controller.viewMode.value == ViewMode.meanings) {
                    return MeaningsPageView(
                    
                    );
                  } else {
                    return WaqafatPageView(
                      onTap: () {},
                    );
                  }
                } else {
                  TransformationController transformationController =
                      TransformationController();
                  return GestureDetector(
                    onTap: () {
                      HomeController.to.toggleShowOverlay();
                    },
                    child: Obx(
                      () {
                        var lastPage = HomeController.to.getLastPage();
                        PageController pageController =
                            PageController(initialPage: lastPage - 1);
                        HomeController.to.currentPage.value = lastPage;

                        HomeController.to.pageController.value = pageController;
                        return HomeController.to.documentDirectory.value != null
                            ? SizedBox(
                                height: double.maxFinite,
                                width: double.maxFinite,
                                child: InteractiveViewer(
                                  transformationController:
                                      transformationController,
                                  onInteractionStart: (__) {
                                    HomeController.to.canChangePage.value =
                                        false;
                                  },
                                  onInteractionEnd: (__) {
                                    if (transformationController.value
                                            .getMaxScaleOnAxis() ==
                                        1) {
                                      HomeController.to.canChangePage.value =
                                          true;
                                    }
                                  },
                                  maxScale: 5,
                                  minScale: 1,
                                  child: PageView.builder(
                                    controller:
                                        HomeController.to.pageController.value,
                                    itemCount: SettingController
                                        .instance.quranBook.value.pagesCount,
                                    onPageChanged: (i) {
                                      HomeController.to.currentPage.value =
                                          i + 1;
                                      HomeController
                                          .to.currentPageNumber.value = i + 1;
                                      HomeController.to.saveLastPage(i + 1);
                                    },
                                    physics: HomeController
                                            .to.canChangePage.value
                                        ? const BouncingScrollPhysics()
                                        : const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      return Center(
                                        child: SingleChildScrollView(
                                          child: ColorFiltered(
                                            colorFilter: ColorFilter.mode(
                                              Theme.of(context).brightness ==
                                                      Brightness.dark
                                                  ? Colors.white
                                                      .withOpacity(0.939)
                                                  : Colors.white,
                                              Theme.of(context).brightness ==
                                                      Brightness.dark
                                                  ? BlendMode.difference
                                                  : BlendMode.modulate,
                                            ),
                                            child: Image.file(
                                              File(
                                                Configs
                                                    .getQuranBookPageFilePath(
                                                  index + 1,
                                                  HomeController.to
                                                      .documentDirectory.value!,
                                                  SettingController.instance
                                                      .quranBook.value.id,
                                                ),
                                              ),
                                              filterQuality: FilterQuality.high,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              )
                            : const Center(
                                child: CircularProgressIndicator(),
                              );
                      },
                    ),
                  );
                }
              }),
              // const Player(),
              ValueListenableBuilder(
                valueListenable: QuranAudioService.processingState,
                builder: (BuildContext context, state, child) {
                  return !(state == AudioProcessingState.idle ||
                          state == AudioProcessingState.completed)
                      ? const Align(
                          alignment: Alignment.bottomCenter,
                          child: Player(),
                        )
                      : Container();
                },
              ),
              // top bar
              Obx(
                () => AnimatedPositioned(
                  top: controller.showOverlay.value ? 0 : -100,
                  left: 0,
                  right: 0,
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.theme.scaffoldBackgroundColor,
                      borderRadius: const BorderRadius.vertical(
                        bottom: Radius.circular(8),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Row(
                      children: [
                        InkWell(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              QuranUIIcons.menu,
                              color: HomeController.to.pageColor.value ==
                                      Colors.white
                                  ? Get.theme.primaryColor
                                  : HomeController.to.pageColor.value,
                              size: 20,
                            ),
                          ),
                          onTap: () {
                            scaffoldKey.currentState?.openDrawer();
                          },
                        ),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Get.isDarkMode
                                  ? Get.theme.primaryColor.withOpacity(0.5)
                                  : (HomeController.to.pageColor.value ==
                                              Colors.white
                                          ? Get.theme.primaryColor
                                          : HomeController.to.pageColor.value)
                                      ?.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: InkWell(
                              onTap: () {
                                Get.bottomSheet(const SearchTypeBottomsheet());
                              },
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.all(6.0),
                                    child: Icon(
                                      Icons.search,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  Text(
                                    "البحث".tr,
                                    style: const TextStyle(
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              QuranUIIcons.settings,
                              color: HomeController.to.pageColor.value ==
                                      Colors.white
                                  ? Get.theme.primaryColor
                                  : HomeController.to.pageColor.value,
                              size: 20,
                            ),
                          ),
                          onTap: () {
                            scaffoldKey.currentState?.openEndDrawer();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
             
              Obx(() {
                if (controller.showOverlay.value &&
                    (controller.showAutoScrollPagesTimer.value ==
                            AutoScrollPagesStatus.active ||
                        controller.showAutoScrollPagesTimer.value ==
                            AutoScrollPagesStatus.puse)) {
                  return Align(
                      alignment: Alignment.bottomCenter,
                      child: TimeSlider(
                        onChanged: (val) {
                          BoxesHelper.setSettingAutoScrollValue(val);
                        },
                        min: 1,
                        max: 15,
                        value: BoxesHelper.getSettingAutoScrollValue().toInt(),
                      ));
                } else if (controller.showOverlay.value &&
                    controller.showAutoScrollPagesTimer.value ==
                        AutoScrollPagesStatus.stop) {
                  return const PageSliderWrapper();
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
        ),
      ),
    );
  }
}

class SearchTypeBottomsheet extends StatelessWidget {
  const SearchTypeBottomsheet({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: 'البحث في',
      body: Column(
        children: [
          InkWell(
            onTap: () {
              Get.back();
              Get.toNamed(Routes.searchPage);
            },
            child: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'المصحف',
                style: TextStyle(
                  fontSize: 18,
                ),
              ),
            ),
          ),
          const Divider(),
          InkWell(
            onTap: () {
              Get.back();
              Get.toNamed(Routes.tafseerSearchPage);
            },
            child: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'التفسير',
                style: TextStyle(
                  fontSize: 18,
                ),
              ),
            ),
          ),
          const Divider(),
          InkWell(
            onTap: () {
              Get.back();
              Get.toNamed(Routes.waqafatSearchPage);
            },
            child: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'الوقفات',
                style: TextStyle(
                  fontSize: 18,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
