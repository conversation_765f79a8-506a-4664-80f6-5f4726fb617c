import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_page_line.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_sura_header.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/view/pages/home/<USER>/page_views/quran_page_view.dart';

class ScaledQuranPage extends StatelessWidget {
  const ScaledQuranPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var page = Hive.box<QuranPage>(Boxes.quranPages)
          .values
          .toList()
          .firstWhere((element) =>
              element.pageNumber == HomeController.to.prevVisitedPage.value!);

      var lines = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
          .values
          .where((element) => element.pageNumber == page.pageNumber)
          .toList();
      var headers = Hive.box<QuranSuraHeader>(Boxes.quranSuarHeaders)
          .values
          .where((element) => element.pageNumber == page.pageNumber)
          .toList();
      String suraName = Get.locale?.languageCode == "ar"
          ? (Hive.box<QuranSura>(Boxes.quranSuar).get(page.suraNumber)?.name ??
              "")
          : (Hive.box<QuranSura>(Boxes.quranSuar)
                  .get(page.suraNumber)
                  ?.nameEn ??
              "");

      return Transform.scale(
        scale: 0.19,
        alignment: AlignmentDirectional.bottomStart,
        child: Container(
          margin: const EdgeInsetsDirectional.only(start: 25),
          decoration: BoxDecoration(
              border: Border.all(),
              color: Colors.white,
              boxShadow: CommonStyles.boxShadow),
          child: InkWell(
            onTap: () {
              debugPrint(
                  "Go to prev visited Page  ${HomeController.to.prevVisitedPage.value}");
              HomeController.to.pageController.value
                  .jumpToPage(HomeController.to.prevVisitedPage.value! - 1);
            },
            child: IgnorePointer(
              child: QuranView(
                headers: headers,
                index: 0,
                lines: lines,
                pages: [page],
                suraName: suraName,
              ),
            ),
          ),
        ),
      );
    });
  }
}
