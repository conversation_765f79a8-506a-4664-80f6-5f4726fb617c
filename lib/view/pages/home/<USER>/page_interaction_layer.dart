import 'package:flutter/material.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/view/pages/home/<USER>/verse_marker.dart';
import '../../../../controllers/home_controller.dart';
import '../../../../models/hive/quran_page_line.dart';

class PageInteractionLayer extends StatelessWidget {
  const PageInteractionLayer(
      {super.key, required this.lines, required this.onLongPress});
  final List<QuranPageLine> lines;
  final Function(QuranPageLine) onLongPress;
  @override
  Widget build(BuildContext context) {
    return Stack(
        children: List<Widget>.generate(
      lines.length,
      (i) {
        return Positioned(
          left: HomeController.to.pageWidth * lines[i].x,
          top: HomeController.to.pageHeight * lines[i].y,
          child: SizedBox(
            width: HomeController.to.pageWidth * lines[i].width,
            height: HomeController.to.pageHeight * lines[i].height,
            child: Row(
              // crossAxisAlignment: CrossAxisAlignment.end,
              // mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Semantics(
                    onLongPress: () {},
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      excludeFromSemantics: true,
                      onTap: () {
                        HomeController.to.toggleShowOverlay();
                      },
                      onLongPress: () {
                        onLongPress(lines[i]);
                      },
                      onTapDown: (detail) {
                        // if (!AudioPlayerService.to.isPlaying.value) {
                        HomeController.to.selectedVerseNumber.value =
                            lines[i].verseNumber;
                        HomeController.to.selectedSuraNumber.value =
                            lines[i].suraNumber;
                        // }
                      },
                      onTapUp: (detail) {
                        if (!QuranAudioService.playing.value) {
                          HomeController.to.selectedVerseNumber.value = 0;
                          HomeController.to.selectedSuraNumber.value = 0;
                        } else {
                          HomeController.to.selectedVerseNumber.value =
                              QuranAudioService.currentVerse.value?.number ?? 0;
                          HomeController.to.selectedSuraNumber.value =
                              QuranAudioService.currentSurah.value?.number ?? 0;
                        }
                      },
                      // onHighlightChanged: (t) {
                      //   print("onChage");
                      //   if (HomeController.to.selectedVerseNumber.value ==
                      //           lines[i].verseNumber &&
                      //       HomeController.to.selectedSuraNumber.value ==
                      //           lines[i].suraNumber) {
                      //     HomeController.to.selectedVerseNumber.value = 0;
                      //     HomeController.to.selectedSuraNumber.value = 0;
                      //   } else {
                      //     HomeController.to.selectedVerseNumber.value =
                      //         lines[i].verseNumber;
                      //     HomeController.to.selectedSuraNumber.value =
                      //         lines[i].suraNumber;
                      //   }
                      // },
                    ),
                  ),
                ),
                if (canCreateVerseIcon(i, lines))
                  Container(
                    alignment: Alignment.center,
                    height: HomeController.to.pageHeight * lines[i].height,
                    child: VerseMarker(
                      verseNumber: lines[i].verseNumber,
                      suraNumber: lines[i].suraNumber,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    )
        //  +
        // <Widget>[
        //   Obx(
        //     () {
        //       if (!HomeController.to
        //               .isPopup
        //               .value ||
        //           HomeController.to.currentSura.value !=
        //               HomeController.to.selectedSuraNumber.value)
        //         return Container();
        //       // var position =
        //       //     lines
        //       //         .lastWhere(
        //       //   (element) =>
        //       //       element.verseNumber ==
        //       //           HomeController.to
        //       //               .selectedVerseNumber.value &&
        //       //       element.suraNumber ==
        //       //           controller.selectedSuraNumber.value,
        //       // );
        //       return VerseOptionsPopup(
        //           // isOnTop:
        //           //     position.y >
        //           //         0.5,
        //           );
        //     },
        //   ),
        // ],
        );
  }

  bool canCreateVerseIcon(int index, List<QuranPageLine> lines) {
    if (index <= lines.length - 1) if (index == lines.length - 1) return true;
    if (lines[index + 1].verseNumber != lines[index].verseNumber) return true;
    return false;
  }
}
