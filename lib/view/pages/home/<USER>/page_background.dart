import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class PageBackground extends StatelessWidget {
  const PageBackground({super.key, this.isLeft = true});
  final bool isLeft;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Stack(
        fit: StackFit.passthrough,
        children: [
          Positioned.fill(
            right: isLeft ? -20 : 0,
            left: isLeft ? 0 : -20,
            top: 0,
            bottom: 0,
            child: Image.asset(
              isLeft
                  ? CommonConstants.pageLeftBackgroundAsset
                  : CommonConstants.pageRightBackgroundAsset,
              fit: BoxFit.fill,
              width: double.maxFinite,
              color: HomeController.to.pageColor.value ??
                  Get.theme.scaffoldBackgroundColor,
              colorBlendMode: BlendMode.modulate,
            ),
          ),
          Positioned.fill(
            right: isLeft ? -16 : 0,
            left: isLeft ? 0 : -16,
            top: 0,
            bottom: 0,
            child: Image.asset(
              isLeft
                  ? CommonConstants.pageLeftBackgroundAsset
                  : CommonConstants.pageRightBackgroundAsset,
              fit: BoxFit.fill,
              color: HomeController.to.pageColor.value ??
                  CustomColors.primaryColor,
              colorBlendMode: BlendMode.modulate,
            ),
          ),
        ],
      ),
    );
  }
}
