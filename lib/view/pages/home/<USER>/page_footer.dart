import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import '../../../../utils/extensions.dart';

class PageFooter extends StatelessWidget {
  const PageFooter({
    Key? key,
    required this.partNumber,
    required this.quarter,
    required this.quarterNumber,
    required this.pageNumber,
    required this.hizbNumber,
  }) : super(key: key);
  final int partNumber;
  final int quarter;
  final int quarterNumber;
  final int pageNumber;
  final int hizbNumber;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 0,
        vertical: 0,
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 8),
              child: Row(
                children: [
                  Text(
                    "${"الجزء".tr} ${Get.locale?.languageCode == "ar"
                            ? partNumber.toArabicNumber()
                            : partNumber.toString()}",
                    style: TextStyle(
                      color: HomeController.to.pageColor.value == Colors.white
                          ? Get.theme.primaryColor
                          : HomeController.to.pageColor.value,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Text(
              Get.locale?.languageCode == "ar"
                  ? pageNumber.toArabicNumber()
                  : pageNumber.toString(),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          Expanded(
            child: Text(
              "${getQuarter(quarter)} ${"الحزب".tr} ${Get.locale?.languageCode == "ar" ? hizbNumber.toArabicNumber() : hizbNumber}",
              textAlign: TextAlign.end,
              style: TextStyle(
                locale: const Locale("en"),
                color: HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String getQuarter(int quarter) {
    var text = "";
    var locleCode = Get.locale!.languageCode;
    switch (quarter) {
      case 1:
        text = "";
        break;
      case 2:
        text = locleCode == "ar"
            ? "${1.toArabicNumber()}/${4.toArabicNumber()}"
            : "1/4";
        break;
      case 3:
        text = locleCode == "ar" ? "نصف".tr : "1/2";
        break;
      case 4:
        text = locleCode == "ar"
            ? "${3.toArabicNumber()}/${4.toArabicNumber()}"
            : "3/4";
        break;
      default:
    }
    return text;
  }
}
