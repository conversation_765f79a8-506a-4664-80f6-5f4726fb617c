import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/hive/quran_sura_header.dart';
import 'package:tadars/view/components/dialogs/sura_waqafat_dialog.dart';


class PageHeaderLayer extends StatelessWidget {
  const PageHeaderLayer({
    Key? key,
    required this.headers,
  }) : super(key: key);
  final List<QuranSuraHeader> headers;
  @override
  Widget build(BuildContext context) {
    final pageWidth = HomeController.to.pageWidth;
    final pageHeight = HomeController.to.pageHeight;
    return Stack(
      children: List.generate(
        headers.length,
        (index) {
          
          return Positioned(
          left: pageWidth * headers[index].x,
          top: pageHeight * headers[index].y,
          
          child: InkWell(
            onTap: () {
                // HomeController.to.toggleShowOverlay();
                Get.dialog(SuraWaqafatDialog(
                  suraNumber: headers[index].id,
                ));
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                width: pageWidth * headers[index].width,
                height: pageHeight * headers[index].height,
              child: Image.asset(
                "assets/images/header.png",
                  color: 
                          Get.isDarkMode
                      ? null
                      : HomeController.to.isCustomColor.value
                          ? HomeController.to.customTextColor.value
                          : HomeController.to.pageColor.value == Colors.white
                              ? null
                              : HomeController.to.pageColor.value,
                colorBlendMode: BlendMode.srcATop,
                fit: BoxFit.fill,
                ),
            ),
          ),
          );
        },
      ),
    );
  }
}
