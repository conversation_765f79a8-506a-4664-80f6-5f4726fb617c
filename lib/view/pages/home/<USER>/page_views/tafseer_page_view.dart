import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart' hide QuranPage;
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/tafseer_controller.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_page_line.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/quran_suar_icons.dart';
import 'package:tadars/view/pages/home/<USER>/page_background.dart';
import 'package:tadars/view/pages/home/<USER>/page_footer.dart';
import 'package:tadars/view/pages/home/<USER>/page_header.dart';
import 'package:tadars/view/pages/home/<USER>/verse_marker.dart';

import 'package:tadars/custom_packages/flutter_html/lib/flutter_html.dart';

class TafseerPageView extends GetView<TafseerController> {
  final VoidCallback onTap;
  const TafseerPageView( {super.key, required this.onTap,this.tapBar});

  final Widget? tapBar;

  @override
  Widget build(BuildContext context) {
    var pages = Hive.box<QuranPage>(Boxes.quranPages).values.toList();
    var height = (MediaQuery.of(context).size.height);
    double newheight =
        height -
        Get.mediaQuery.viewPadding.top -
        Get.mediaQuery.viewPadding.bottom;
    if (kDebugMode) {
      print(Get.mediaQuery.viewPadding.top);
    }

    var lastPage = HomeController.to.getLastPage();
    HomeController.to.quranPageController.pageController = PageController(
      initialPage: lastPage - 1,
    );

    Get.put(TafseerController());
    var isDarkColor =
        HomeController.to.pageColor.value == const Color(0xFF181A1F);

    return PageView.builder(
      onPageChanged: (i) {
        HomeController.to.quranPageController.pageController.jumpToPage(i);

        HomeController.to.currentPage.value = pages[i].pageNumber;
        HomeController.to.quranPageController.page = pages[i].pageNumber;

        HomeController.to.currentPageNumber.value = pages[i].pageNumber;
        HomeController.to.saveLastPageAndSura(
          pages[i].pageNumber,
          pages[i].suraNumber,
        );
        var lastLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines).values
            .lastWhere((element) => element.pageNumber == pages[i].pageNumber);
        HomeController.to.lastVerseNumberInPage.value = lastLine.verseNumber;
        HomeController.to.lastSuraNumberInPage.value = lastLine.suraNumber;
        var firstLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines).values
            .firstWhere((element) => element.pageNumber == pages[i].pageNumber);
        HomeController.to.firstVerseNumberInPage.value = firstLine.verseNumber;
        HomeController.to.firstSuraNumberInPage.value = firstLine.suraNumber;
      },
      controller: HomeController.to.quranPageController.pageController,
      itemCount: pages.length,
      itemBuilder: (context, i) {
        var page = pages[i];
        var verses = BoxesHelper.getPageVerses(page.pageNumber);
        String suraName =
            Get.locale?.languageCode == "ar"
                ? (Hive.box<QuranSura>(
                      Boxes.quranSuar,
                    ).get(pages[i].suraNumber)?.name ??
                    "")
                : (Hive.box<QuranSura>(
                      Boxes.quranSuar,
                    ).get(pages[i].suraNumber)?.nameEn ??
                    "");
        bool isPortrait =
            (HomeController.to.pageWidth / HomeController.to.pageHeight) >
            (context.mediaQuery.size.width / context.mediaQuery.size.height);
        return SingleChildScrollView(
          child: SizedBox(
            height: newheight,
            width: Get.width,
            child: Obx(
              () => Stack(
                children: [
                  if (isPortrait) PageBackground(isLeft: i.isEven),
                  GestureDetector(
                    onTap: () {
                      HomeController.to.toggleShowOverlay();
                    },
                    child: Container(
                      color:
                          isDarkColor
                              ? HomeController.to.pageColor.value
                              : HomeController.to.pageColor.value?.withOpacity(
                                0.2,
                              ),
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          PageHeader(suraName: suraName, pageNumber: i + 1),

                          if (tapBar != null) tapBar!,

                          Expanded(
                            child: Stack(
                              children: [
                                ListView.builder(
                                  padding: const EdgeInsets.only(bottom: 40),
                                  itemCount: verses.length,
                                  itemBuilder: (context, index) {
                                    //print(verses[index].translation);
                                    return Obx(() {
                                      var tafseer = TafseerController.instance
                                          .getVerseTafseer(
                                            verses[index].verseNumber,
                                            verses[index].suraNumber,
                                          );
                                      print(
                                        "tafseer for verse ${verses[index].verseNumber} & sura ${verses[index].suraNumber} $tafseer",
                                      );
                                      return Column(
                                        children: [
                                          if (verses[index].verseNumber == 1)
                                            Container(
                                              margin: const EdgeInsets.only(
                                                top: 8,
                                              ),
                                              child: Column(
                                                children: [
                                                  Stack(
                                                    alignment: Alignment.center,
                                                    children: [
                                                      Image.asset(
                                                        "assets/images/header.png",
                                                        // color: HomeController.to
                                                        //         .pageColor.value ??
                                                        //     CustomColors
                                                        //         .accentColor,
                                                        // colorBlendMode:
                                                        //     BlendMode.srcATop,
                                                        fit: BoxFit.fill,
                                                      ),
                                                      SizedBox(
                                                        width: double.maxFinite,
                                                        child: Icon(
                                                          verses[index]
                                                              .suraNumber
                                                              .toSuraIconData(),
                                                          size: 23,
                                                          color:
                                                              isDarkColor
                                                                  ? const Color(
                                                                    0xff797D87,
                                                                  )
                                                                  : null,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  if (verses[index]
                                                              .suraNumber !=
                                                          1 &&
                                                      verses[index]
                                                              .suraNumber !=
                                                          9)
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            top: 8,
                                                          ),
                                                      width: double.maxFinite,
                                                      child: Icon(
                                                        QuranSuarIcons.basmala,
                                                        color:
                                                            isDarkColor
                                                                ? const Color(
                                                                  0xff797D87,
                                                                )
                                                                : null,
                                                        size: 35,
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          Column(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  HomeController.to
                                                      .toggleShowOverlay();
                                                },
                                                onLongPress: () {
                                                  HomeController.to
                                                      .openVerseOptionsDailog(
                                                        suraNumber:
                                                            verses[index]
                                                                .suraNumber,
                                                        verseNumber:
                                                            verses[index]
                                                                .verseNumber,
                                                        pageNumber:
                                                            verses[index]
                                                                .pageNumber,
                                                      );
                                                },
                                                onTapDown: (detail) {
                                                  HomeController
                                                          .to
                                                          .selectedVerseNumber
                                                          .value =
                                                      verses[index].verseNumber;
                                                  HomeController
                                                          .to
                                                          .selectedSuraNumber
                                                          .value =
                                                      verses[index].suraNumber;
                                                },
                                                onTapUp: (detail) {
                                                  if (!QuranAudioService
                                                      .playing
                                                      .value) {
                                                    HomeController
                                                        .to
                                                        .selectedVerseNumber
                                                        .value = 0;
                                                    HomeController
                                                        .to
                                                        .selectedSuraNumber
                                                        .value = 0;
                                                  } else {
                                                    HomeController
                                                            .to
                                                            .selectedVerseNumber
                                                            .value =
                                                        QuranAudioService
                                                            .currentVerse
                                                            .value
                                                            ?.number ??
                                                        0;
                                                    HomeController
                                                            .to
                                                            .selectedSuraNumber
                                                            .value =
                                                        QuranAudioService
                                                            .currentSurah
                                                            .value
                                                            ?.number ??
                                                        0;
                                                  }
                                                },
                                                child: Container(
                                                  padding: const EdgeInsets.all(
                                                    8,
                                                  ),
                                                  margin: const EdgeInsets.all(
                                                    8,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        CommonStyles
                                                            .borderRadius,
                                                    color:
                                                        (verses[index].verseNumber ==
                                                                    HomeController
                                                                        .to
                                                                        .selectedVerseNumber
                                                                        .value &&
                                                                verses[index]
                                                                        .suraNumber ==
                                                                    HomeController
                                                                        .to
                                                                        .selectedSuraNumber
                                                                        .value)
                                                            ? Get.isDarkMode
                                                                ? Get
                                                                    .theme
                                                                    .primaryColor
                                                                    .withOpacity(
                                                                      0.3,
                                                                    )
                                                                : (HomeController.to.pageColor.value ==
                                                                            Colors.white
                                                                        ? Get
                                                                            .theme
                                                                            .primaryColor
                                                                        : HomeController
                                                                            .to
                                                                            .pageColor
                                                                            .value)
                                                                    ?.withOpacity(
                                                                      0.3,
                                                                    )
                                                            : null,
                                                  ),
                                                  child: Text.rich(
                                                    TextSpan(
                                                      text:
                                                          "${verses[index].verseWithDiac} ",
                                                      children: [
                                                        WidgetSpan(
                                                          alignment:
                                                              PlaceholderAlignment
                                                                  .middle,
                                                          child: VerseMarker(
                                                            size:
                                                                HomeController
                                                                    .to
                                                                    .pageFontSize
                                                                    .value *
                                                                1.2,
                                                            verseNumber:
                                                                verses[index]
                                                                    .verseNumber,
                                                            suraNumber:
                                                                verses[index]
                                                                    .suraNumber,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    style: TextStyle(
                                                      fontSize:
                                                          HomeController
                                                              .to
                                                              .pageFontSize
                                                              .value,
                                                      fontFamily:
                                                          CommonConstants
                                                              .hafsFontFamily,
                                                      color:
                                                          isDarkColor
                                                              ? const Color(
                                                                0xff797D87,
                                                              )
                                                              : null,
                                                    ),
                                                    textDirection:
                                                        TextDirection.rtl,
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: double.maxFinite,
                                                margin:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                    ),
                                                padding: const EdgeInsets.all(
                                                  8,
                                                ),
                                                decoration: BoxDecoration(
                                                  color:
                                                      Get.isDarkMode
                                                          ? Get
                                                              .theme
                                                              .primaryColor
                                                              .withOpacity(0.2)
                                                          : (HomeController
                                                                          .to
                                                                          .pageColor
                                                                          .value ==
                                                                      Colors
                                                                          .white
                                                                  ? Get
                                                                      .theme
                                                                      .primaryColor
                                                                  : HomeController
                                                                      .to
                                                                      .pageColor
                                                                      .value)
                                                              ?.withOpacity(
                                                                0.2,
                                                              ),
                                                  borderRadius:
                                                      CommonStyles.borderRadius,
                                                ),
                                                child:
                                                    tafseer != null
                                                        ? TafseerHtmlView(
                                                          htmlText:
                                                              tafseer.text,
                                                        )
                                                        : Text(
                                                          "لايوجد تفسير لهذه الآية"
                                                              .tr,
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      );
                                    });
                                  },
                                ),
                                const Positioned(
                                  left: 0,
                                  bottom: 4,
                                  child: TafseerPickerButton(),
                                ),
                              ],
                            ),
                          ),
                          PageFooter(
                            partNumber: pages[i].partNumber,
                            quarter: pages[i].quarter,
                            quarterNumber: pages[i].quarterNumber,
                            pageNumber: pages[i].pageNumber,
                            hizbNumber: pages[i].hizbNumber,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class TafseerHtmlView extends StatelessWidget {
  const TafseerHtmlView({Key? key, required this.htmlText}) : super(key: key);

  final String htmlText;

  @override
  Widget build(BuildContext context) {
    var isDarkColor =
        HomeController.to.pageColor.value == const Color(0xFF181A1F);
    return Html(
      data: htmlText,
      style: {
        "*": Style(
          fontSize: FontSize(
            HomeController.to.pageFontSize.value * 0.8,
            units: "rem",
          ),
          lineHeight: const LineHeight(1.5),
          fontFamily: CommonConstants.samimFontFamily,
          textAlign: TextAlign.right,
          color: isDarkColor ? const Color(0xff797D87) : null,
        ),
        "table": Style(
          border: Border.all(
            color:
                HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value ?? Colors.black,
            width: 0.2,
          ),
        ),
        "tr": Style(
          width: double.maxFinite,
          border: Border.all(
            color:
                HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value ?? Colors.black,
            width: 0.2,
          ),
        ),
        "tr:first-child": Style(
          width: double.maxFinite,
          border: Border.all(
            color:
                HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value ?? Colors.black,
            width: 0.2,
          ),
        ),
        "td:first-child": Style(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.topLeft,
        ),
        "td:nth-child(1)": Style(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.topRight,
        ),
      },
    );
  }
}

class TafseerPickerButton extends StatelessWidget {
  const TafseerPickerButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Box>(
      valueListenable: BoxesHelper.getListenableSettingsTafseerId(),
      builder: (context, box, child) {
        int id = box.get(SettingsConstants.tafseerId, defaultValue: 1);
        var tafseer = BoxesHelper.getTafseerById(id);
        return Obx(
          () => Material(
            color:
                HomeController.to.pageColor.value == Colors.white
                    ? Get.theme.primaryColor
                    : HomeController.to.pageColor.value,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: () {
                TafseerController.instance.showTafseerDialog();
              },
              child: Container(
                padding: const EdgeInsetsDirectional.only(
                  bottom: 4,
                  top: 4,
                  start: 12,
                  end: 4,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (tafseer != null)
                      if (tafseer.title.length > 40)
                        SizedBox(
                          width: Get.width * 0.7,
                          child: Text(
                            tafseer.title,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        )
                      else
                        Text(
                          tafseer.title,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        )
                    else
                      const Text(""),
                    const Icon(Icons.arrow_drop_down, color: Colors.white),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
