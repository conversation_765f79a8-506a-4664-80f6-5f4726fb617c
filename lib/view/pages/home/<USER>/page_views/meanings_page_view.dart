import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/features/translation/translation.dart';
import 'package:tadars/view/pages/home/<USER>/page_views/tafseer_page_view.dart';

import '../../../../../controllers/home_controller.dart';

class MeaningsPageView extends StatelessWidget {
  const MeaningsPageView({super.key});

  Widget _buildTapBar() {
    return Theme(
      data: ThemeData(
        tabBarTheme: TabBarTheme(
          indicatorColor:
              Get.isDarkMode
                  ? Colors.white
                  : HomeController.to.pageColor.value == Colors.white ||
                      Get.isDarkMode
                  ? Get.theme.primaryColor
                  : HomeController.to.pageColor.value,
          labelColor:
              Get.isDarkMode
                  ? Colors.white
                  : HomeController.to.pageColor.value == Colors.white
                  ? Get.theme.primaryColor
                  : HomeController.to.pageColor.value,
          unselectedLabelStyle: TextStyle(
            color: Get.isDarkMode ? Colors.white : null,
            fontFamily: Get.textTheme.titleSmall?.fontFamily,
          ),
          labelStyle: TextStyle(
            fontFamily: Get.textTheme.titleSmall?.fontFamily,
          ),
        ),
      ),

      child: TabBar(
        key: Key("TabBar"),
        tabs: [Tab(text: "التفسير".tr), Tab(text: "الترجمة".tr)],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          // TabBar(tabs: [Tab(text: "التفسير".tr), Tab(text: "الترجمة".tr)]),
          Expanded(
            child: TabBarView(
              children: [
                TafseerPageView(tapBar: _buildTapBar(), onTap: () {}),
                TranslationPageView(tapBar: _buildTapBar()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
