import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../models/hive/bookmark.dart';
import '../../../../../utils/constants/boxes.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/enums.dart';
import '../../../../components/dialogs/sura_waqafat_dialog.dart';
import '../page_footer.dart';
import '../page_header.dart';

class QuranPageViewWrapper extends StatelessWidget {
  const QuranPageViewWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // var lastPage = HomeController.to.getLastPage();

    // HomeController.to.quranPageController.pageController =
    //     PageController(initialPage: lastPage - 1);

    var lastPage = HomeController.to.getLastPage();
    HomeController.to.quranPageController.pageController =
        PageController(initialPage: lastPage - 1);

   
    return GestureDetector(
      onTap: () {
        if (HomeController.to.showAutoScrollPagesTimer.value ==
                AutoScrollPagesStatus.active ||
            HomeController.to.showAutoScrollPagesTimer.value ==
                AutoScrollPagesStatus.puse) {
          HomeController.to.toggleShowOverlay();
        }
      },
      child: OrientationBuilder(builder: (context, orientation) {
        // if (orientation == Orientation.landscape) {
        //   HomeController.to.quranPageController.stopAutoScroll();
        //   HomeController.to.showAutoScrollPagesTimer =
        //       AutoScrollPagesStatus.stop.obs;
          // }
          return StreamBuilder(
            stream: UserSyncService.instance.bookmarks.stream,
            builder: (context, snapshot) {
              return Obx(() {
                var isDarkColor = Get.theme.brightness == Brightness.dark;
                // temp solution
                HomeController.to.prevVisitedPage.value?.isEven;
                return SizedBox(
                  height: double.maxFinite,
                  width: double.maxFinite,
                  child: QuranPageView(
                    pageTextBold: true,
                    controller: HomeController.to.quranPageController,
                    onSurahHeaderTap: (surah) {
                      Get.dialog(SuraWaqafatDialog(suraNumber: surah.number));
                    },
                    onPageChanged: (pageNumber) {
                      var page =
                          HomeController
                              .to
                              .quranPageController
                              .pages?[pageNumber];
                      // HomeController.to.quranPageController.page = index;
                      //
                      HomeController.to.prevVisitedPage.value =
                          HomeController.to.currentPageNumber.value;
                      //
                      HomeController.to.currentPage.value = pageNumber;
                      HomeController.to.currentPageNumber.value = pageNumber;
              
                      if (HomeController
                              .to
                              .quranPageController
                              .pages?[pageNumber] !=
                          null) {
                        print("saveLastPageAndSura index = $pageNumber");
                        HomeController.to.saveLastPageAndSura(
                          pageNumber,
                          HomeController
                              .to
                              .quranPageController
                              .pages![pageNumber]!
                              .surahNumber,
                        );
                      }
              
                      HomeController.to.lastVerseNumberInPage.value =
                          page?.lastVerseNumber ?? 0;
                      HomeController.to.lastSuraNumberInPage.value =
                          page?.lastSurahNumber ?? 0;
              
                      HomeController.to.firstVerseNumberInPage.value =
                          page?.firstVerseNumber ?? 0;
                      HomeController.to.firstSuraNumberInPage.value =
                          page?.firstSurahNumber ?? 0;
                    },
                    verseHighlightColor:
                        Get.isDarkMode
                            ? Get.theme.primaryColor.withOpacity(0.2)
                            : (HomeController.to.pageColor.value?.value ==
                                        Colors.white.value
                                    ? Get.theme.primaryColor.withOpacity(0.2)
                                    : HomeController.to.pageColor.value)
                                ?.withOpacity(0.3),
                    onVerseLongPress: (verse) async {
                      await Future.delayed(const Duration(milliseconds: 100));
                      HomeController.to.openVerseOptionsDailog(
                        suraNumber: verse.surahNumber,
                        verseNumber: verse.number,
                        pageNumber: verse.pageNumber,
                      );
                    },
                    onPageTap: (page) {
                      HomeController.to.toggleShowOverlay();
                    },
                    pageColor:
                        (Get.theme.brightness == Brightness.dark
                            ? const Color(0xFF181A1F)
                            : HomeController.to.isCustomColor.value
                            ? HomeController.to.customBackgroundColor.value
                            : (HomeController.to.pageColor.value?.withOpacity(
                              0.4,
                            ))),
                    pageBorderColor: Get.theme.primaryColor,
                    pageTextColor:
                        isDarkColor
                            ? Colors.white
                            : HomeController.to.isCustomColor.value
                            ? HomeController.to.customTextColor.value
                            : null,
                    headerBuilder:
                        (page) => PageHeader(
                          suraName: page?.surahs.first.searchableName ?? "",
                          pageNumber: page?.number ?? 0,
                        ),
                    verseMarkerColorBuilder: (Verse verse) {
                      var bookmark = HomeController.to.getBookmark(
                        suraNumber: verse.surahNumber,
                        verseNumber: verse.number,
                      );
                      return bookmark != null
                          ? CustomColors.bookmarksColors[bookmark.colorIndex!]
                          : null;
                    },
                    footerBuilder:
                        (page) => PageFooter(
                          partNumber: page?.partNumber ?? 0,
                          quarter: page?.quarter ?? 0,
                          quarterNumber: page?.quarterNumber ?? 0,
                          pageNumber: page?.number ?? 0,
                          hizbNumber: page?.hizbNumber ?? 0,
                        ),
                  ),
                );
              });
            },
          );
      }),
    );
  }
}
