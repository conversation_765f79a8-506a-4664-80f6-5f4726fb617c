import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart' hide QuranPage;
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/tadars_controller.dart';
import 'package:tadars/controllers/tafseer_controller.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_page_line.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/tadars_constants.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/quran_suar_icons.dart';
import 'package:tadars/view/components/dialogs/verse_waqafat_dialog.dart';
import 'package:tadars/view/pages/home/<USER>/page_background.dart';
import 'package:tadars/view/pages/home/<USER>/page_footer.dart';
import 'package:tadars/view/pages/home/<USER>/page_header.dart';
import 'package:tadars/view/pages/home/<USER>/verse_marker.dart';



class WaqafatPageView extends GetView<TadarsController> {
  final VoidCallback onTap;
  const WaqafatPageView({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    //
    Get.put(TadarsController());

    var pages = Hive.box<QuranPage>(Boxes.quranPages).values.toList();
    var height = (MediaQuery.of(context).size.height);
    double newheight = height -
        Get.mediaQuery.viewPadding.top -
        Get.mediaQuery.viewPadding.bottom;
    if (kDebugMode) {
      print(Get.mediaQuery.viewPadding.top);
    }
    var lastPage = HomeController.to.getLastPage();
    HomeController.to.quranPageController.pageController =
        PageController(initialPage: lastPage - 1);
   
    Get.put(TafseerController());
    return PageView.builder(
      onPageChanged: (i) {

        
        HomeController.to.currentPage.value = pages[i].pageNumber;
        HomeController.to.currentPageNumber.value = pages[i].pageNumber;
        HomeController.to
            .saveLastPageAndSura(pages[i].pageNumber, pages[i].suraNumber);
        var lastLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
            .values
            .lastWhere((element) => element.pageNumber == pages[i].pageNumber);
        HomeController.to.lastVerseNumberInPage.value = lastLine.verseNumber;
        HomeController.to.lastSuraNumberInPage.value = lastLine.suraNumber;
        var firstLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
            .values
            .firstWhere((element) => element.pageNumber == pages[i].pageNumber);
        HomeController.to.firstVerseNumberInPage.value = firstLine.verseNumber;
        HomeController.to.firstSuraNumberInPage.value = firstLine.suraNumber;
      },
      controller: HomeController.to.quranPageController.pageController,
      itemCount: pages.length,
      dragStartBehavior: DragStartBehavior.down,
      itemBuilder: (context, i) {
        var page = pages[i];
        var verses = BoxesHelper.getPageVerses(page.pageNumber);
        String suraName = Get.locale?.languageCode == "ar"
            ? (Hive.box<QuranSura>(Boxes.quranSuar)
                    .get(pages[i].suraNumber)
                    ?.name ??
                "")
            : (Hive.box<QuranSura>(Boxes.quranSuar)
                    .get(pages[i].suraNumber)
                    ?.nameEn ??
                "");
        bool isPortrait = (HomeController.to.pageWidth /
                HomeController.to.pageHeight) >
            (context.mediaQuery.size.width / context.mediaQuery.size.height);
        return DefaultTabController(
          length: TadarsConstants.getEnabledTdbrTypes().length,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: SizedBox(
                    height: newheight,
                    width: Get.width,
                    child: Obx(
                      () => Stack(
                        children: [
                          if (isPortrait)
                            PageBackground(
                              isLeft: i.isEven,
                            ),
                          GestureDetector(
                            onTap: () {
                              HomeController.to.toggleShowOverlay();
                            },
                            child: Container(
                              color: HomeController.to.pageColor.value
                                  ?.withOpacity(0.2),
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  PageHeader(
                                    suraName: suraName,
                                    pageNumber: i + 1,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      right: 8.0,
                                      left: 8,
                                      bottom: 8,
                                    ),
                                    child: Theme(
                                      data: ThemeData(
                                        tabBarTheme: TabBarTheme(
                                          tabAlignment: TabAlignment.start,
                                          indicatorColor: Get.isDarkMode
                                              ? Colors.white
                                              : HomeController
                                                      .to.pageColor.value ==
                                                          Colors.white ||
                                                      Get.isDarkMode
                                              ? Get.theme.primaryColor
                                              : HomeController
                                                  .to.pageColor.value,
                                          labelColor: Get.isDarkMode
                                              ? Colors.white
                                              : HomeController
                                                      .to.pageColor.value ==
                                                  Colors.white
                                                  
                                              ? Get.theme.primaryColor
                                              : HomeController
                                                  .to.pageColor.value,
                                          unselectedLabelStyle: TextStyle(
                                            color: Get.isDarkMode
                                                ? Colors.white
                                                : null,
                                            fontFamily: Get.textTheme.titleSmall
                                                ?.fontFamily,
                                          ),
                                          labelStyle: TextStyle(
                                            fontFamily: Get.textTheme.titleSmall
                                                ?.fontFamily,
                                          ),
                                        ),
                                     
                                     
                                     
                                      ),
                                      child: TabBar(
                                        isScrollable: true,
                                        tabs: TadarsConstants
                                                .getEnabledTdbrTypes()
                                            .map((e) {
                                          return Tab(
                                                    text:
                                                        TadarsConstants
                                                            .tdbrKeyTranlation[e],
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                 
                                 
                                 
                                  Expanded(
                                    child: TabBarView(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      children: TadarsConstants.getEnabledTdbrTypes().map((
                                        tdbrType,
                                      ) {
                                        return ListView.builder(
                                          primary: true,
                                          padding:
                                              const EdgeInsets.only(bottom: 40),
                                          itemCount: verses.length,
                                          itemBuilder: (context, index) {
                                            //print(verses[index].translation);
                                            return Obx(
                                              () {
                                                // var tdbrs =
                                                //     BoxesHelper.getVerseTdbr(
                                                //         verses[index]
                                                //             .suraNumber,
                                                //         verses[index]
                                                //             .verseNumber,
                                                //         e);
                                                return Column(
                                                  children: [
                                                    if (verses[index]
                                                            .verseNumber ==
                                                        1)
                                                      Container(
                                                        margin: const EdgeInsets
                                                            .only(top: 8),
                                                        child: Column(
                                                          children: [
                                                            Stack(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              children: [
                                                                Image.asset(
                                                                  "assets/images/header.png",
                                                                  color: HomeController
                                                                              .to.pageColor.value ==
                                                                          Colors
                                                                              .white
                                                                      ? Get
                                                                          .theme
                                                                          .primaryColor
                                                                      : HomeController
                                                                          .to
                                                                          .pageColor
                                                                          .value,
                                                                  colorBlendMode:
                                                                      BlendMode
                                                                          .srcATop,
                                                                  fit: BoxFit
                                                                      .fill,
                                                                ),
                                                                SizedBox(
                                                                  width: double
                                                                      .maxFinite,
                                                                  child: Icon(
                                                                    verses[index]
                                                                        .suraNumber
                                                                        .toSuraIconData(),
                                                                    size: 23,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            if (verses[index]
                                                                        .suraNumber !=
                                                                    1 &&
                                                                verses[index]
                                                                        .suraNumber !=
                                                                    9)
                                                              Container(
                                                                padding:
                                                                    const EdgeInsets
                                                                            .only(
                                                                        top: 8),
                                                                width: double
                                                                    .maxFinite,
                                                                child:
                                                                    const Icon(
                                                                  QuranSuarIcons
                                                                      .basmala,
                                                                  size: 35,
                                                                ),
                                                              )
                                                          ],
                                                        ),
                                                      ),
                                                    Column(
                                                      children: [
                                                        GestureDetector(
                                                          onTap: () {
                                                            HomeController.to
                                                                .toggleShowOverlay();
                                                          },
                                                          onLongPress: () {
                                                            HomeController.to
                                                                .openVerseOptionsDailog(
                                                              suraNumber: verses[
                                                                      index]
                                                                  .suraNumber,
                                                              verseNumber: verses[
                                                                      index]
                                                                  .verseNumber,
                                                              pageNumber: verses[
                                                                      index]
                                                                  .pageNumber,
                                                            );
                                                          },
                                                          onTapDown: (detail) {
                                                            HomeController
                                                                .to
                                                                .selectedVerseNumber
                                                                .value = verses[
                                                                    index]
                                                                .verseNumber;
                                                            HomeController
                                                                .to
                                                                .selectedSuraNumber
                                                                .value = verses[
                                                                    index]
                                                                .suraNumber;
                                                          },
                                                          onTapUp: (detail) {
                                                            if (!QuranAudioService
                                                                .playing
                                                                .value) {
                                                              HomeController
                                                                  .to
                                                                  .selectedVerseNumber
                                                                  .value = 0;
                                                              HomeController
                                                                  .to
                                                                  .selectedSuraNumber
                                                                  .value = 0;
                                                            } else {
                                                              HomeController
                                                                  .to
                                                                  .selectedVerseNumber
                                                                  .value =
                                                          QuranAudioService
                                                                      .currentVerse
                                                                      .value
                                                                      ?.number ??
                                                                  0;
                                                              HomeController
                                                                  .to
                                                                  .selectedSuraNumber
                                                                  .value =
                                                          QuranAudioService
                                                                      .currentSurah
                                                                      .value
                                                                      ?.number ??
                                                                  0;
                                                            }
                                                          },
                                                          child: Container(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(8),
                                                            margin:
                                                                const EdgeInsets
                                                                    .all(8),
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  CommonStyles
                                                                      .borderRadius,
                                                              color: (verses[index]
                                                                              .verseNumber ==
                                                                          HomeController
                                                                              .to
                                                                              .selectedVerseNumber
                                                                              .value &&
                                                                      verses[index]
                                                                              .suraNumber ==
                                                                          HomeController
                                                                              .to
                                                                              .selectedSuraNumber
                                                                              .value)
                                                                  ? Get
                                                                          .isDarkMode ||
                                                                          HomeController.to.pageColor.value ==
                                                                              Colors
                                                                                  .white
                                                                      ? Get
                                                                          .theme
                                                                          .primaryColor
                                                                          .withOpacity(
                                                                              0.3)
                                                                      : HomeController
                                                                      .to
                                                                      .pageColor
                                                                      .value
                                                                      ?.withOpacity(
                                                                          0.4)
                                                                  : null,
                                                            ),
                                                            child: Text.rich(
                                                              TextSpan(
                                                                text:
                                                                    "${verses[index].verseWithDiac} ",
                                                                children: [
                                                                  WidgetSpan(
                                                                    alignment:
                                                                        PlaceholderAlignment
                                                                            .middle,
                                                                    child:
                                                                        VerseMarker(
                                                                      size: HomeController
                                                                              .to
                                                                              .pageFontSize
                                                                              .value *
                                                                          1.2,
                                                                      verseNumber:
                                                                          verses[index]
                                                                              .verseNumber,
                                                                      suraNumber:
                                                                          verses[index]
                                                                              .suraNumber,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              style: TextStyle(
                                                                fontSize:
                                                                    HomeController
                                                                        .to
                                                                        .pageFontSize
                                                                        .value,
                                                                fontFamily:
                                                                    CommonConstants
                                                                        .hafsFontFamily,
                                                              ),
                                                              textDirection:
                                                                  TextDirection
                                                                      .rtl,
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                            ),
                                                          ),
                                                        ),
                                                       
                                                       
                                                       
                                                        VerseTdbrView(
                                                            verseNumber: verses[
                                                                    index]
                                                                .verseNumber,
                                                            suraNumber:
                                                                verses[index]
                                                                    .suraNumber,
                                                            tdbrType: tdbrType)
                                                      ],
                                                    )
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                  PageFooter(
                                    partNumber: pages[i].partNumber,
                                    quarter: pages[i].quarter,
                                    quarterNumber: pages[i].quarterNumber,
                                    pageNumber: pages[i].pageNumber,
                                    hizbNumber: pages[i].hizbNumber,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      
      
      
      },
    );
  }
}
