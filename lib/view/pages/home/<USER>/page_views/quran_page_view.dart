import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_page_line.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_sura_header.dart';

import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/view/pages/home/<USER>/page_background.dart';
import 'package:tadars/view/pages/home/<USER>/page_footer.dart';
import 'package:tadars/view/pages/home/<USER>/page_header.dart';
import 'package:tadars/view/pages/home/<USER>/page_header_layer.dart';
import 'package:tadars/view/pages/home/<USER>/page_interaction_layer.dart';
import 'package:tadars/view/pages/home/<USER>/verses_selecion_layer.dart';

class QuranPageView extends StatelessWidget {
  const QuranPageView({super.key});
  @override
  Widget build(BuildContext context) {
    var pages = Hive.box<QuranPage>(Boxes.quranPages).values.toList();
    var lastPage = HomeController.to.getLastPage();
    PageController pageController = PageController(initialPage: lastPage);
    HomeController.to.currentPage.value = lastPage;

    HomeController.to.pageController.value = pageController;
    var lastLine = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
        .values
        .lastWhere(
            (element) => element.pageNumber == pages[lastPage - 1].pageNumber);
    HomeController.to.lastVerseNumberInPage.value = lastLine.verseNumber;
    HomeController.to.lastSuraNumberInPage.value = lastLine.suraNumber;
    TransformationController transformationController =
        TransformationController();
    return Directionality(
      textDirection: TextDirection.rtl,
      child: InteractiveViewer(
          transformationController: transformationController,
          panEnabled: true,
          maxScale: 5,
          minScale: 1,
          onInteractionStart: (__) {
            HomeController.to.canChangePage.value = false;
          },
          onInteractionEnd: (__) {
            if (transformationController.value.getMaxScaleOnAxis() == 1) {
              HomeController.to.canChangePage.value = true;
            }
          },
          child: Obx(
            () {
              return PageView.builder(
                allowImplicitScrolling: true,
                dragStartBehavior: DragStartBehavior.start,
                physics: HomeController.to.canChangePage.value
                    ? const BouncingScrollPhysics()
                    : const NeverScrollableScrollPhysics(),
                onPageChanged: (i) {
                  //
                  HomeController.to.prevVisitedPage.value =
                      HomeController.to.currentPageNumber.value;
                  //
                  HomeController.to.currentPage.value = pages[i].pageNumber;
                  HomeController.to.currentPageNumber.value =
                      pages[i].pageNumber;
                  HomeController.to.saveLastPageAndSura(
                      pages[i].pageNumber, pages[i].suraNumber);

                  HomeController.to.lastVerseNumberInPage.value =
                      pages[i].lastVerseNumber;
                  HomeController.to.lastSuraNumberInPage.value =
                      pages[i].lastSuraNumber;

                  HomeController.to.firstVerseNumberInPage.value =
                      pages[i].firstVerseNumber;
                  HomeController.to.firstSuraNumberInPage.value =
                      pages[i].firstSuraNumber;
                },
                controller: pageController,
                itemCount: pages.length,
                itemBuilder: (context, i) {
                  var lines = Hive.box<QuranPageLine>(Boxes.quranPagesLines)
                      .values
                      .where((element) =>
                          element.pageNumber == pages[i].pageNumber)
                      .toList();
                  var headers =
                      Hive.box<QuranSuraHeader>(Boxes.quranSuarHeaders)
                          .values
                          .where((element) =>
                              element.pageNumber == pages[i].pageNumber)
                          .toList();
                  String suraName = Get.locale?.languageCode == "ar"
                      ? (Hive.box<QuranSura>(Boxes.quranSuar)
                              .get(pages[i].suraNumber)
                              ?.name ??
                          "")
                      : (Hive.box<QuranSura>(Boxes.quranSuar)
                              .get(pages[i].suraNumber)
                              ?.nameEn ??
                          "");

                  return QuranView(
                      suraName: suraName,
                      lines: lines,
                      pages: pages,
                      index: i,
                      headers: headers,
                      onTap: () {
                        HomeController.to.toggleShowOverlay();
                      });
                },
              );
            },
          )),
    );
  }
}

class QuranView extends StatelessWidget {
  const QuranView(
      {super.key,
      required this.suraName,
      required this.lines,
      required this.pages,
      required this.headers,
      required this.index,
      this.onTap});

  final String suraName;
  final List<QuranPageLine> lines;
  final List<QuranPage> pages;
  final List<QuranSuraHeader> headers;
  final int index;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(builder: (context, orientation) {
      var height = (MediaQuery.of(context).size.height);
      double newheight = height -
          Get.mediaQuery.viewPadding.top -
          Get.mediaQuery.viewPadding.bottom;
      var isPortrait = orientation == Orientation.portrait;
      var isDarkColor =
          HomeController.to.pageColor.value == const Color(0xFF181A1F);
      print(
          'page color ${HomeController.to.pageColor.value == const Color(0xFF181A1F)}');
      return SingleChildScrollView(
        
        child: GestureDetector(
          onTap: onTap,
          child: SizedBox(
            width: Get.width,
            height: !isPortrait ? null : newheight,
            child: Obx(
              () => Stack(
                children: [
                  if (isPortrait)
                    PageBackground(
                      isLeft: index.isEven,
                    ),
                  Container(
                    color: isDarkColor
                        ? HomeController.to.pageColor.value
                        : HomeController.to.isCustomColor.value
                            ? HomeController.to.customBackgroundColor.value
                            : HomeController.to.pageColor.value
                                ?.withOpacity(0.2),
                    padding: const EdgeInsets.all(8),
                    child: Column( 
                      children: [
                        PageHeader(
                          suraName: suraName,
                          pageNumber: index + 1,
                        ),
                        // if (isPortrait) const Spacer(),
                        Expanded(
                          flex: isPortrait ? 1 : 0,
                          child: SizedBox(
                            width: double.maxFinite,
                            // height: double.maxFinite,
                            child: AspectRatio(
                              aspectRatio: HomeController.to.pageWidth /
                                  HomeController.to.pageHeight,
                              child: FittedBox(
                                fit: BoxFit.contain,
                                child: SizedBox(
                                  height: HomeController.to.pageHeight,
                                  width: HomeController.to.pageWidth,
                                  child: Stack(
                                    children: [
                                      VersesSelectionLayer(lines: lines),
                                      SizedBox(
                                        width: double.maxFinite,
                                        height: double.maxFinite,
                                        child: ValueListenableBuilder(
                                            valueListenable:
                                                Hive.box(Boxes.settings)
                                                    .listenable(keys: [
                                              SettingsConstants
                                                  .appDarkModeEnabledKey
                                            ]),
                                            builder: (context, Box box, child) {
                                              return Obx(() {
                                                var isCustom = HomeController
                                                    .to.isCustomColor.value;
                                                return Image.asset(
                                                  "${CommonConstants.quranPagesAssetsPath}${pages[index].pageNumber}.png",
                                                  fit: BoxFit.fitWidth,
                                                  color: Get.isDarkMode
                                                      ? Colors.white
                                                      : HomeController
                                                              .to
                                                              .isCustomColor
                                                              .value  
                                                          ? HomeController
                                                              .to
                                                              .customTextColor
                                                              .value
                                                          : HomeController
                                                              .to
                                                              .pageTextColor
                                                              .value,
                                                  filterQuality:
                                                      FilterQuality.high,
                                                );
                                              });
                                            }),
                                      ),
                                      if (headers.isNotEmpty)
                                        PageHeaderLayer(
                                          headers: headers,
                                        ),
                                      PageInteractionLayer(
                                        lines: lines,
                                        onLongPress: (line) {
                                          // Get.defaultDialog();
                                          HomeController.to
                                              .openVerseOptionsDailog(
                                                  suraNumber: line.suraNumber,
                                                  verseNumber: line.verseNumber,
                                                  pageNumber: line.pageNumber);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        // if (isPortrait) const Spacer(),
                        PageFooter(
                          partNumber: pages[index].partNumber,
                          quarter: pages[index].quarter,
                          quarterNumber: pages[index].quarterNumber,
                          pageNumber: pages[index].pageNumber,
                          hizbNumber: pages[index].hizbNumber,
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      // } else {
      //   return SingleChildScrollView(
      //     child: GestureDetector(
      //       onTap: () {
      //         HomeController.to.toggleShowOverlay();
      //       },
      //       child: Column(),
      //     ),
      //   );
      // }
    });
  }
}
