import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../utils/common_styles.dart';

class CustomToggleButton extends StatelessWidget {
  const CustomToggleButton({
    super.key,
    this.selected = false,
    required this.label,
    required this.onTap,
  });
  final bool selected;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 1.5, horizontal: 8),
        decoration: !selected
            ? null
            : BoxDecoration(
                color: context.theme.scaffoldBackgroundColor,
                borderRadius: CommonStyles.borderRadius,
              ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 15,
            color: !selected ? Colors.white : null,
          ),
        ),
      ),
    );
  }
}
