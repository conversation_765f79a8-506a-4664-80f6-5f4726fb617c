import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:tadars/models/view_models/hizb_index_vm.dart';
import 'package:tadars/utils/extensions.dart';

import 'sura_card.dart';

class HizbStickyHeader extends StatelessWidget {
  const HizbStickyHeader({
    super.key,
    required this.item,
  });
  final HizbIndexVM item;
  @override
  Widget build(BuildContext context) {
    return SliverStickyHeader(
      header: Container(
        color: Get.theme.scaffoldBackgroundColor,
        child: Container(
          color: Get.theme.primaryColor.withOpacity(0.3),
          padding: const EdgeInsets.symmetric(
            horizontal: 32,
            vertical: 4,
          ),
          width: double.maxFinite,
          child: Text(
            "${"الحزب".tr} ${item.hizbNumber.toLocaleNumber()}",
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ),
      ),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (BuildContext context, int index) {
            var icon = "assets/svgs/";
            if (item.pages[index].quarter == 2) {
              icon += "half_quarter.svg";
            } else if (item.pages[index].quarter == 2) {
              icon += "third_quarter.svg";
            } else if (item.pages[index].quarter == 4) {
              icon += "full_quarter.svg";
            } else {
              icon += "quarter.svg";
            }
            // return SuraCard(
            //   icon: SvgPicture.asset(
            //     icon,
            //     width: 25,
            //     height: 25,
            //   ),
            //   margin: index == 0 ? const EdgeInsets.all(8) : null,
            //   pageNumber: item.pages[index].number,
            //   title: item.pages[index].surahs
            //           .firstWhereOrNull((surah) => surah.verses.any((verse) =>
            //               verse.quarterNumber == item.pages[index].quarter))
            //           ?.verses
            //           .firstWhereOrNull((verse) =>
            //               verse.quarterNumber == item.pages[index].quarter)
            //           ?.unicode ??
            //       "",
            //   titleStyle: TextStyle(
            //     fontWeight: FontWeight.w400,
            //     fontFamily: "page_${item.pages[index].number}",
            //     fontSize: 18,
            //     // color: Colors.black,
            //   ),

            //   subTitle:
            //       "${item.pages[index].surahs.first.searchableName.tr} - ${"صفحة".tr} ${item.pages[index].number.toLocaleNumber()}",

            //   suraNumber: item.pages[index].surahNumber,
            // );

            return Container();
          },
          childCount: item.pages.length,
        ),
      ),
    );
  }
}
