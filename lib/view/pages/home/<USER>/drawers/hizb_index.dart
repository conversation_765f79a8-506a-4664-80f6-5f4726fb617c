import 'package:flutter/material.dart' hide Page;
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/utils/extensions.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/hizb_sticky_header.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/hizb_widget.dart';
import '../../../../../models/view_models/hizb_index_vm.dart';
import 'search_text_field.dart';

class HizbIndex extends StatelessWidget {
  const HizbIndex({super.key});

  List<HizbIndexVM> createIndexTree(List<Page> items) {
    List<HizbIndexVM> tree = [];
    int lastIndex = -1;
    int hizbNumer = 0;

    for (var item in items) {
      if (hizbNumer != item.hizbNumber) {
        lastIndex++;
        tree.add(HizbIndexVM(hizbNumber: item.hizbNumber, pages: [item]));

        hizbNumer = item.hizbNumber;
      } else {
        print("quarter ${item.quarter}");
        if (!tree[lastIndex]
            .pages
            .any((page) => page.quarter == item.quarter)) {
          tree[lastIndex].pages.add(item);
        }
      }
    }

    return tree;
  }

  // @override
  // void initState() {
  //   hizb = Hive.box<QuranPage>(Boxes.quranPages)
  //       .values
  //       .where((page) => pageNumbers.contains(page.pageNumber))
  //       .toList();
  //   super.initState();
  // }

  @override
  Widget build(BuildContext context) {
    // var quartesGroupdBy = groupBy(
    //     HomeController.to.quranPageController.pages?.values.toList() ??
    //         <Page>[],
    //     (Page p) => p.quarterNumber).entries.map(toElement);

    // var quartes = HomeController.to.quranPageController.pages?.values
    //     .toList()
    //     .distinctBy((page) => page.hizbNumber)
    //     .toList();

    var hizb =
        HomeController.to.quranPageController.pages?.values.toList() ?? [];

    ValueNotifier<List<Page>> hizbValue = ValueNotifier<List<Page>>(hizb);

    return Column(
      children: [
        SearchTextField(
          hintText: "بحث برقم الحزب".tr,
          onChanged: (value) {
            if (value.trim().isNotEmpty) {
              hizbValue.value = hizb
                  .where((el) =>
                      el.hizbNumber.toArabicNumber() ==
                      (int.tryParse(value)?.toArabicNumber() ?? value))
                  .toList();
            }
          },
        ),
        Expanded(
          child: ValueListenableBuilder<List<Page>>(
              valueListenable: hizbValue,
              builder: (context, value, child) {
                var treeIndex = createIndexTree(hizbValue.value);
                return ListView.builder(
                  itemCount: treeIndex.length,
                  itemBuilder: (ctx, index) => HizbWidget(
                    item: treeIndex[index],
                  ),
                );
    
               
              }),
        )
      ],
    );
  }
}
