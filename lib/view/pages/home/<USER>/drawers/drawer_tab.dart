import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DrawerTab extends StatelessWidget {
  const DrawerTab({
    super.key,
    required this.text,
    this.icon,
    this.svgIcon,
  });
  final String text;
  final IconData? icon;
  final String? svgIcon;
  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: const EdgeInsets.all(2),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: 4.0,
            ),
            child: svgIcon != null
                ? SvgPicture.asset(
                    svgIcon!,
                    width: 25,
                    height: 25,
                  )
                : Icon(
                    icon,
                    size: 20,
                  ),
          ),
          Text(
            text,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
