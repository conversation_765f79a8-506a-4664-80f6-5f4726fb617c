import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:jiffy/jiffy.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../features/user_data_sync/data/enums/bookmark_type.dart';
import '../../../../../features/user_data_sync/models/bookmark.dart';
import '../../../../../helpers/boxes_helper.dart';
import '../../../../../models/hive/quran_sura.dart';
import '../../../../../models/hive/quran_verse.dart';
import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/boxes.dart';
import '../../../../../utils/constants/common_constants.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/extensions.dart';
import '../../../../../utils/quran_ui_icons.dart';

class BookmarkCard extends StatelessWidget {
  const BookmarkCard({super.key, required this.item, this.margin});
  final Bookmark item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var suraName =
        Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.name.tr;
    var verseText =
        Hive.box<QuranVerse>(Boxes.quranVerses)
            .get(
              "${item.suraNumber.toString().padLeft(3, "0")}_${item.verseNumber.toString().padLeft(3, "0")}",
            )
            ?.verseWithDiac;
    return Container(
      margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: CommonStyles.borderRadius,
        color: Get.theme.scaffoldBackgroundColor,
      ),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.25,
          children: <Widget>[
            SlidableAction(
              label: 'حذف'.tr,
              backgroundColor: Colors.redAccent,
              // icon: Icons.delete,
              onPressed: (context) {
                if (item.type == BookmarkType.page) {
                  UserSyncService.instance.deletePageFromBookmarks(
                    item.pageNumber ?? 0,
                  );
                } else {
                  UserSyncService.instance.removeFromBookmarks(
                    suraNumber: item.suraNumber ?? 0,
                    verseNumber: item.verseNumber ?? 0,
                  );

               
                }
              },
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: CommonStyles.borderRadius,
          child: ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: CommonStyles.borderRadius,
            ),
            leading: Icon(
              QuranUIIcons.bookmarkFilled,
              color: CustomColors.bookmarksColors[item.colorIndex ?? 0],
            ),
            onTap: () async {
              Get.back();
              var homeController = Get.find<HomeController>();
              if (item.type == BookmarkType.verse) {
                unawaited(
                  homeController.quranPageController.goToVerse(
                    item.suraNumber ?? 0,
                    item.verseNumber ?? 0,
                  ),
                );
              } else {
                homeController.goToPage(item.pageNumber ?? 0);
              }
            },
            dense: true,
            trailing: Container(
              height: 28,
              width: 28,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: CustomColors.accentColor.withValues(alpha: 0.1),
              ),
              child: Center(
                child: Text(
                  item.pageNumber?.toLocaleNumber() ?? '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: CommonConstants.numbersFontFamily,
                    color: CustomColors.accentColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (item.type == BookmarkType.verse)
                  Text(
                    "${suraName ?? ""} - ${"الاية رقم".tr}${item.verseNumber?.toLocaleNumber()}",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  )
                else
                  Text(
                    "${"الصفحة".tr} ${item.pageNumber?.toLocaleNumber() ?? ''}",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                Text(
                  verseText ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
            subtitle: Text(
              Jiffy.parseFromDateTime(item.createdAt).fromNow(),
              style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ),
    );
  }
}
