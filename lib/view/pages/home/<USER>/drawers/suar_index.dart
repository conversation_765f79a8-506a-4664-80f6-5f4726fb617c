import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../models/quran_index_section.dart';
import '../../../../../utils/extensions.dart';
import 'quran_index_listview.dart';
import 'search_text_field.dart';

class SuarIndex extends StatelessWidget {
  const SuarIndex({
    super.key,
    required this.suar,
  });
  final List<Surah> suar;

  List<QuranIndexSection> createIndexTree(List<Surah> items) {
    var stopwatch = Stopwatch();
    stopwatch.start();
    var lastPartNumer = 0;
    var lastindex = -1;
    var tree = <QuranIndexSection>[];
    for (var item in items) {
      var partNumber = item.partNumber;
      if (partNumber != lastPartNumer) {
        tree.add(
          QuranIndexSection(
              header: "${"الجزء".tr} ${partNumber?.toLocaleNumber()}",
              items: [item]),
        );
        lastPartNumer = partNumber ?? 0;
        lastindex++;
      } else {
        tree[lastindex].items.add(item);
      }
    }
    debugPrint('createIndexTree: ${stopwatch.elapsedMilliseconds}');
    return tree;
  }

  @override
  Widget build(BuildContext context) {
    var suarValue = ValueNotifier<List<Surah>>(suar);
    WidgetsBinding.instance
        .addPostFrameCallback((_) => Scrollable.ensureVisible(
              HomeController.to.dataKey.value.currentContext ?? context,
              alignment: 0.2,
              duration: const Duration(milliseconds: 500),
            ));
    return Column(
      children: [
        SearchTextField(
          hintText: 'بحث في اسماء السور'.tr,
          onChanged: (value) {
            if (value.trim().isNotEmpty) {
              suarValue.value = suar
                  .where((element) => element.name.tr.arabicNormalize
                      .contains(value.arabicNormalize))
                  .toList();
            }
          },
        ),
        Expanded(
          child: ValueListenableBuilder<List<Surah>>(
              valueListenable: suarValue,
              builder: (context, value, child) {
                return QuranIndexListView(
                  items: createIndexTree(value),
                );
              }),
        )
      ],
    );
  }
}
