import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:jiffy/jiffy.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../features/user_data_sync/data/services/user_sync_service.dart';
import '../../../../../features/user_data_sync/models/note.dart';
import '../../../../../helpers/boxes_helper.dart';
import '../../../../../models/hive/quran_sura.dart';
import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/boxes.dart';
import '../../../../../utils/constants/common_constants.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/extensions.dart';
import '../../../../../utils/quran_ui_icons.dart';

class NoteCard extends StatelessWidget {
  const NoteCard({
    super.key,
    required this.item,
    this.margin,
  });
  final Note item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var suraName =
        Get.locale?.languageCode == 'ar'
        ? Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.name
        : Hive.box<QuranSura>(Boxes.quranSuar).get(item.suraNumber)?.nameEn;
    return Container(
      margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: CommonStyles.borderRadius,
        color: Get.theme.scaffoldBackgroundColor,
      ),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.25,
          children: <Widget>[
            SlidableAction(
              label: 'حذف'.tr,
              backgroundColor: Colors.redAccent,
              // icon: Icons.delete,
              onPressed: (context) {
                debugPrint('delete');

                UserSyncService.instance.deleteNote(
                  item.suraNumber!,
                  item.verseNumber!,
                );
              },
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: CommonStyles.borderRadius,
          child: ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: CommonStyles.borderRadius,
            ),
            leading: Icon(
              QuranUIIcons.note,
              color: Get.theme.colorScheme.secondary,
              size: 20,
            ),
            onTap: () async {
              Get.back();
              var homeController = Get.find<HomeController>();
              // homeController.selectedSuraNumber.value = item.suraNumber!;
              // homeController.selectedVerseNumber.value = item.verseNumber!;

              homeController.goToVerse(item.suraNumber!, item.verseNumber!);
            },
            dense: true,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${suraName ?? ""} - ${"الاية رقم".tr} ${Get.locale?.languageCode == "ar" ? item.verseNumber!.toArabicNumber() : item.verseNumber}",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  item.note ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
            subtitle: Text(
              Jiffy.parseFromDateTime(item.createdAt!).fromNow(),
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
