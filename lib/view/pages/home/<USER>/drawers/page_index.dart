import 'package:flutter/material.dart' hide Page;
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/custom_packages/flutter_html/lib/flutter_html.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/utils/extensions.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../utils/constants/common_constants.dart';
import 'search_text_field.dart';
import 'sura_card.dart';

// class PageIndex extends StatefulWidget {
//   const PageIndex({super.key});

//   @override
//   State<PageIndex> createState() => _PageIndexState();
// }

class PageIndex extends StatelessWidget {
  // late List<QuranPage> pages;
  // late List<QuranVerse> verses;

  // @override
  // void initState() {
  //   pages = Hive.box<QuranPage>(Boxes.quranPages).values.toList();
  //   verses = Hive.box<QuranVerse>(Boxes.quranVerses).values.toList();
  //   super.initState();
  // }

  const PageIndex({super.key});

  @override
  Widget build(BuildContext context) {
    var pages =
        HomeController.to.quranPageController.pages?.values.toList() ?? [];
    ValueNotifier<List<Page>> pagesValue = ValueNotifier<List<Page>>(pages);
    return Column(
      children: [
        SearchTextField(
          hintText: "بحث برقم الصفحة".tr,
          onChanged: (value) {
            if (value.trim().isNotEmpty) {
              pagesValue.value =
                  pages
                  .where((el) =>
                      el.number.toArabicNumber() ==
                      (int.tryParse(value)?.toArabicNumber() ?? value))
                  .toList();
            }
          },
        ),
        Expanded(
          child: ValueListenableBuilder<List<Page>>(
              valueListenable: pagesValue,
              builder: (context, value, child) {
             
                return ListView.builder(
                    itemCount: value.length,
                    itemBuilder: (context, index) {
                      var surah = value[index].surahs.first;
                      surah.pageNumber = value[index].number;
                      return Column(
                        children: [
                          // Container(
                          //   color: Get.theme.scaffoldBackgroundColor,
                          //   child: Container(
                          //     color: Get.theme.primaryColor.withOpacity(0.3),
                          //     padding: const EdgeInsets.symmetric(
                          //       horizontal: 32,
                          //       vertical: 4,
                          //     ),
                          //     width: double.maxFinite,
                          //     child: Text(
                          //       "${"الحزب".tr} ${value[index].hizbNumber}",
                          //       style: const TextStyle(
                          //         fontWeight: FontWeight.w600,
                          //         fontSize: 11,
                          //       ),
                          //     ),
                          //   ),
                          // // ),



                          SuraCard(

                            margin: const EdgeInsets.all(8),
                            item: surah,
                            title: Text(
                              value[index].surahs.first.verses.first.text,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: CommonConstants.hafsFontFamily,
                                  fontSize: 16,
                                
                                  // height: 1.1,
                                  // package: "quran_core",
                                  color: Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black),
                            ),
                            index: value[index].number,
                            subtitle: SizedBox.shrink(),

                            // pageNumber: value[index].number,
                            // title:
                            //     value[index].surahs.first.verses.first.unicode,
                            // titleStyle: TextStyle(
                            //   fontWeight: FontWeight.w400,
                            //   fontFamily: "page_${value[index].number}",
                            //   fontSize: 18,
                            //   color:
                            //       Get.isDarkMode ? Colors.white : Colors.black,
                            // ),
                            // subTitle:
                            //     "${value[index].surahs.first.name.tr} - ${"صفحة".tr} ${value[index].number.toLocaleNumber()}",

                            // suraNumber: value[index].firstSurahNumber,
                          )
                       
                       
                        ],
                      );
                    });
              }),
        )
      ],
    );
  }
}
