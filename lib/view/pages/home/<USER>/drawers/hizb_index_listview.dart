import 'package:flutter/material.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/hizb_sticky_header.dart';

import '../../../../../models/view_models/hizb_index_vm.dart';

class HizbIndexListView extends StatelessWidget {
  const HizbIndexListView({
    super.key,
    this.items = const [],
  });
  final List<HizbIndexVM> items;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomScrollView(
          shrinkWrap: true,
          slivers: items
              .map(
                (e) => HizbStickyHeader(
                  item: e,
                ),
              )
              .toList(),
        ),
      ],
    );
  }
}
