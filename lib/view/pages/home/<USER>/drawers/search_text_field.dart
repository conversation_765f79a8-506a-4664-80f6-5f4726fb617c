import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/custom_colors.dart';

class SearchTextField extends StatelessWidget {
  const SearchTextField({
    Key? key,
    this.onChanged,
    this.hintText,
    this.onSubmitted,
    this.fillColor,
    this.margin,
    this.controller,
  }) : super(key: key);
  final void Function(String value)? onChanged;
  final void Function(String value)? onSubmitted;
  final String? hintText;
  final Color? fillColor;
  final TextEditingController? controller;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: TextField(
        enableInteractiveSelection: false,
        onChanged: onChanged,
        style: TextStyle(
          fontSize: 16,
          color: CustomColors.primaryColor,
          fontWeight: FontWeight.w500,
        ),
        controller: controller,
        textInputAction: TextInputAction.search,
        onSubmitted: onSubmitted,
   
        decoration: InputDecoration(
          isDense: true,
          prefixIconConstraints:
              const BoxConstraints(minWidth: 35, minHeight: 35),
          prefixIcon: const Icon(Icons.search),
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
          fillColor: fillColor ?? Get.theme.scaffoldBackgroundColor,
          filled: true,
          hintText: hintText,
          hintStyle: TextStyle(
            fontSize: 14,
            color: Get.isDarkMode ? Colors.white : Colors.black54,
            fontWeight: FontWeight.w500,
          ),
          border: OutlineInputBorder(
            borderSide: const BorderSide(
              width: 0,
              color: Colors.transparent,
            ),
            gapPadding: 0,
            borderRadius: CommonStyles.borderRadius,
          ),
          focusedBorder: OutlineInputBorder(
            gapPadding: 0,
            borderSide: const BorderSide(
              width: 0,
              color: Colors.transparent,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          errorBorder: OutlineInputBorder(
            gapPadding: 0,
            borderSide: const BorderSide(
              width: 0,
              color: Colors.red,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
          enabledBorder: OutlineInputBorder(
            gapPadding: 0,
            borderSide: const BorderSide(
              width: 0,
              color: Colors.transparent,
            ),
            borderRadius: CommonStyles.borderRadius,
          ),
        ),
      ),
    );
  }
}
