import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/models/view_models/hizb_index_vm.dart';
import 'package:tadars/utils/extensions.dart';

import 'sura_card.dart';

class HizbWidget extends StatelessWidget {
  const HizbWidget({
    super.key,
    required this.item,
  });
  final HizbIndexVM item;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: Get.theme.scaffoldBackgroundColor,
          child: Container(
            color: Get.theme.primaryColor.withOpacity(0.3),
            padding: const EdgeInsets.symmetric(
              horizontal: 32,
              vertical: 4,
            ),
            width: double.maxFinite,
            child: Text(
              "${"الحزب".tr} ${item.hizbNumber.toLocaleNumber()}",
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 11,
              ),
            ),
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        ...item.pages.map(
          (e) => Builder(builder: (ctz) {
            var icon = "assets/svgs/";
            if (e.quarter == 2) {
              icon += "half_quarter.svg";
            } else if (e.quarter == 3) {
              icon += "third_quarter_ic.svg";
            } else if (e.quarter == 4) {
              icon += "full_quarter.svg";
            } else {
              icon += "quarter.svg";
            }

            var verse = e.surahs
                .firstWhereOrNull((surah) => surah.verses
                    .any((verse) => verse.quarterNumber == e.quarter))
                ?.verses
                .firstWhereOrNull((verse) => verse.quarterNumber == e.quarter);


            var surah = e.surahs.first;
            surah.pageNumber = e.number;
            return SuraCard(
              index: e.number,
              item: e.surahs.first,
              icon: SvgPicture.asset(
                icon,
                width: 25,
                height: 25,
              ),
              // margin: index == 0 ? const EdgeInsets.all(8) : null,
              // pageNumber: e.number,
              // title: verse?.unicode ?? "",
              // titleStyle: TextStyle(
              //   fontWeight: FontWeight.w400,
              //   fontFamily: "page_${e.number}",
              //   fontSize: 18,
              //     color: Get.isDarkMode ? Colors.white : Colors.black
              // ),

              // subTitle:
              //     "${e.surahs.first.searchableName.tr} - ${"صفحة".tr} ${e.number.toLocaleNumber()}",
              // suraNumber: e.surahNumber,
              // onTap: () {
              //   HomeController.to.quranPageController.selectVerse(verse);
              // },
            );

          }),
        )
      ],
    );
  }
}
