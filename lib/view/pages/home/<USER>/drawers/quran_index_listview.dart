import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:sticky_and_expandable_list/sticky_and_expandable_list.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/sura_card.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../models/quran_index_section.dart';

class QuranIndexListView extends StatefulWidget {
  const QuranIndexListView({
    super.key,
    this.items = const [],
  });
  final List<QuranIndexSection> items;

  @override
  State<QuranIndexListView> createState() => _QuranIndexListViewState();
}

class _QuranIndexListViewState extends State<QuranIndexListView>
    with AutomaticKeepAliveClientMixin {
  late AutoScrollController scrollController;
  @override
  void initState() {
    super.initState();
    scrollController = AutoScrollController(
        viewportBoundaryGetter: () =>
            Rect.fromLTRB(0, 0, 0, MediaQuery.of(context).padding.bottom),
        axis: Axis.vertical);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController.scrollToIndex(
        HomeController.to.currentSuraNumber.value,
        preferPosition: AutoScrollPosition.middle,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ExpandableListView(
      controller: scrollController,
      builder: SliverExpandableChildDelegate(
        itemBuilder: (context, sectionIndex, itemIndex, index) {
          return SuraCard(
            item: widget.items[sectionIndex].items[itemIndex],
            margin: itemIndex == 0 ? const EdgeInsets.all(8) : null,
            isActive: widget.items[sectionIndex].items[itemIndex].number ==
                HomeController.to.currentSuraNumber.value,
            index: index,
            scrollController: scrollController,
          );
        },
        headerBuilder: (context, sectionIndex, index) {
          return InkWell(
            onTap: () {
              setState(() {
                widget.items[sectionIndex]
                    .setSectionExpanded(!widget.items[sectionIndex].expanded);
              });
            },
            child: Container(
              color: Get.theme.scaffoldBackgroundColor,
              child: Container(
                color: Get.theme.primaryColor.withOpacity(0.3),
                padding: const EdgeInsets.only(
                  right: 32,
                  left: 16,
                  top: 4,
                  bottom: 4,
                ),
                width: double.maxFinite,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.items[sectionIndex].header,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                    ),
                    Icon(
                      !widget.items[sectionIndex].expanded
                          ? Icons.arrow_drop_down_outlined
                          : Icons.arrow_drop_up_rounded,
                      color: Get.theme.primaryColor,
                    )
                  ],
                ),
              ),
            ),
          );
        },
        sectionList: widget.items,
      ),
    );
    // return Stack(
    //   children: [
    //     CustomScrollView(
    //       shrinkWrap: true,
    //       slivers: widget.items
    //           .map(
    //             (e) => PartStickyHeader(
    //               item: e,
    //             ),
    //           )
    //           .toList(),
    //     ),
    //   ],
    // );
  }

  @override
  bool get wantKeepAlive => true;
}
