import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../../controllers/home_controller.dart';
import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/common_constants.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/extensions.dart';

class SuraCard extends StatelessWidget {
  const SuraCard({
    super.key,
    required this.item,
    this.margin,
    this.isActive = false,
    required this.index,
    this.scrollController,
    this.icon,
    this.title,
    this.subtitle,
  });
  final Surah item;
  final EdgeInsetsGeometry? margin;
  final bool isActive;
  final int index;
  final AutoScrollController? scrollController;
  final Widget? icon;
  final Widget? title;
  final Widget? subtitle;
  @override
  Widget build(BuildContext context) {
    return AutoScrollTag(
      controller: scrollController ?? AutoScrollController(),
      index: item.number,
      key: ValueKey(item.number),
      child: Container(
        margin: margin ?? const EdgeInsets.only(right: 8, left: 8, bottom: 8),
        decoration: BoxDecoration(
          borderRadius: CommonStyles.borderRadius,
          color: Get.theme.scaffoldBackgroundColor,
          border: isActive
              ? Border.all(width: 1, color: CustomColors.accentColor)
              : null,
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: CommonStyles.borderRadius,
          child: ListTile(
            leading: icon,
            shape: RoundedRectangleBorder(
              borderRadius: CommonStyles.borderRadius,
            ),
            onTap: () async {
              Get.back();
              var homeController = Get.find<HomeController>();
              homeController.currentSuraNumber.value = item.number;
              homeController.selectedSuraNumber.value = item.number;
              homeController.selectedVerseNumber.value = 1;
              await Future.delayed(const Duration(milliseconds: 300));
              homeController.goToPage(item.pageNumber ?? 1);
            },
            dense: true,
            trailing: Container(
              height: 28,
              width: 28,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isActive
                    ? CustomColors.accentColor.withOpacity(0.8)
                    : CustomColors.accentColor.withOpacity(0.1),
              ),
              child: Center(
                child: Text(
                  item.pageNumber?.toLocaleNumber() ?? '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: CommonConstants.numbersFontFamily,
                    color: isActive
                        ? Colors.white
                        : (Get.isDarkMode ? Colors.white : Colors.black),
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            title: title ??
                Text(
                  item.unicode.trim().tr,
                  style: TextStyle(
                      fontWeight: FontWeight.normal,
                      fontSize: 24,
                      fontFamily: 'suar_name',
                      package: 'quran_core',
                      color: (Get.isDarkMode ? Colors.white : Colors.black)),
                ),
            subtitle: subtitle ??
                Text(
                  "${"رقمها".tr} ${item.number.toLocaleNumber()} - ${"آياتها".tr} ${item.versesCount.toLocaleNumber()} - ${item.surahType == SurahType.makki ? "مكيّة".tr : "مدنيّة".tr}",
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: (Get.isDarkMode ? Colors.white : Colors.black),
                  ),
                ),
          ),
        ),
      ),
    );
  }
}
