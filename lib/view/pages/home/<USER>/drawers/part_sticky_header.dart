// import 'package:flutter/material.dart';
// import 'package:flutter_sticky_header/flutter_sticky_header.dart';
// import 'package:get/get.dart';
// import 'package:quran_core/quran_core.dart';
// import 'package:tadars/utils/extensions.dart';

// import '../../../../../controllers/home_controller.dart';
// import '../../../../../models/view_models/quran_index_vm.dart';
// import 'sura_card.dart';

// class PartStickyHeader extends StatelessWidget {
//   const PartStickyHeader({
//     super.key,
//     required this.item,
//   });
//   final QuranIndexVM item;
//   @override
//   Widget build(BuildContext context) {
//     var homeController = Get.find<HomeController>();

//     return SliverStickyHeader(
//       header: Container(
//         color: Get.theme.scaffoldBackgroundColor,
//         child: Container(
//           color: Get.theme.primaryColor.withOpacity(0.3),
//           padding: const EdgeInsets.symmetric(
//             horizontal: 32,
//             vertical: 4,
//           ),
//           width: double.maxFinite,
//           child: Text(
//             "${"الجزء".tr} ${item.partNumber.toLocaleNumber()}",
//             style: const TextStyle(
//               fontWeight: FontWeight.w600,
//               fontSize: 11,
//             ),
//           ),
//         ),
//       ),
//       sliver: SliverList(
//         delegate: SliverChildBuilderDelegate(
//           (BuildContext context, int index) {
//             return SuraCard(
//               margin: index == 0 ? const EdgeInsets.all(8) : null,
//               // item: item.suar[index],
//               pageNumber: item.suar[index].pageNumber ?? 1,
//               title: item.suar[index].unicode,
//               titleStyle: TextStyle(
//                 fontWeight: FontWeight.w400,
//                   fontFamily: "suar_name",
//                 fontSize: 20,
//                 package: "quran_core",
//                   color: Get.isDarkMode ? Colors.white : Colors.black
//               ),

//               subTitle:
//                   "${"رقمها".tr} ${item.suar[index].number.toLocaleNumber()} - ${"آياتها".tr} ${item.suar[index].versesCount.toLocaleNumber()} - ${item.suar[index].surahType == SurahType.makki ? "مكيّة".tr : "مدنيّة".tr}",
//               suraNumber: item.suar[index].number,
//               isActive:
//                   item.suar[index].number ==
//                   homeController.currentSuraNumber.value,
//             );
//           },
//           childCount: item.suar.length,
//         ),
//       ),
//     );
//   }
// }
