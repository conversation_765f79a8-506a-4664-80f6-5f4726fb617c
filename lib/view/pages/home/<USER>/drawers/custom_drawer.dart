import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/hizb_index.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/juz_index.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/page_index.dart';
import 'package:tadars/view/pages/home/<USER>/drawers/suar_index.dart';
import '../../../../../models/hive/quran_sura.dart';
import '../../../../../utils/constants/boxes.dart';
import '../../../../../utils/constants/common_constants.dart';
import 'bookmarks_listview.dart';
import 'drawer_tab.dart';
import 'notes_listview.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    //var homeController = HomeController.to;
    return Drawer(
      child: DefaultTabController(
        length: 3,
        child: Container(
          color: Get.theme.primaryColor.withOpacity(0.1),
          child: SafeArea(
            child: Column(
              children: [
                Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  child: Column(
                    children: [
                      Material(
                        color: Colors.transparent,
                        child: Container(
                          padding: const EdgeInsets.only(
                            top: 16.0,
                            right: 16,
                            left: 16,
                            bottom: 16,
                          ),
                          child: Row(
                            children: [
                              Text(
                                "القائمة".tr,
                                style: const TextStyle(fontSize: 30),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              const Spacer(),
                              IconButton(
                                onPressed: () {
                                  Scaffold.of(context).openEndDrawer();
                                },
                                icon: Icon(
                                  QuranUIIcons.settings,
                                  size: 18,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  if (Platform.isAndroid) {
                                    Get.defaultDialog<bool?>(
                                        title: "تأكيد الخروج".tr,
                                        middleText:
                                            "هل أنت متأكد من الخروج من التطبيق؟"
                                                .tr,
                                        textConfirm: "نعم".tr,
                                        textCancel: "لا".tr,
                                        buttonColor: CustomColors.primaryColor,
                                        confirmTextColor: Colors.white,
                                        cancelTextColor:
                                            CustomColors.primaryColor,
                                        onConfirm: () {
                                          SystemNavigator.pop();
                                        },
                                        onCancel: () {
                                          Get.back(result: false);
                                        });
                                  }
                                },
                                icon: Icon(
                                  Icons.close,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                   
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        // SuarIndex(
                        //           suar: Hive.box<QuranSura>(Boxes.quranSuar)
                        //               .values
                        //               .toList(),
                        //         ),
                        DefaultTabController(
                          length: 4,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: const EdgeInsets.only(top: 16),
                                // decoration: BoxDecoration(
                                //   boxShadow: CommonStyles.boxShadow,
                                //   color: Colors.white,
                                // ),
                                child: TabBar(
                                    automaticIndicatorColorAdjustment: true,
                                    labelStyle: const TextStyle(
                                      fontSize: 12,
                                      fontFamily:
                                          CommonConstants.cairoFontFamily,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    indicatorSize: TabBarIndicatorSize.tab,
                                    // indicator: BoxDecoration(),
                                    unselectedLabelColor: Get.isDarkMode
                                        ? Colors.white
                                        : Colors.black54,

                                    labelColor: CustomColors.accentColor,
                                    indicatorColor: CustomColors.accentColor,
                                    tabs: [
                                      DrawerTab(
                                        icon: QuranUIIcons.menu,
                                        text: "السور".tr,
                                      ),
                                      DrawerTab(
                                        svgIcon: "assets/svgs/half_quarter.svg",
                                        text: "الجزء".tr,
                                      ),
                                      DrawerTab(
                                        // icon: QuranUIIcons.bookmark,
                                        svgIcon: "assets/svgs/full_quarter.svg",
                                        text: "الحزب".tr,
                                      ),
                                      DrawerTab(
                                        // icon: QuranUIIcons.bookmark,
                                        svgIcon: "assets/svgs/book.svg",
                                        text: "الصفحة".tr,
                                      ),
                                    ]),
                              ),
                              Expanded(
                                child: TabBarView(children: [
                                  FutureBuilder(
                                    future:
                                        QuranDatabaseProvider.getAllSurahs(),
                                    builder: (ctx, snapShot) {
                                      if (snapShot.connectionState ==
                                          ConnectionState.waiting) {
                                        return Center(
                                          child: CircularProgressIndicator(
                                            color: CustomColors.accentColor,
                                          ),
                                        );
                                      }

                                      return SuarIndex(
                                          suar: snapShot.data ?? []);
                                    },
                                  ),
                          
                          
                                  JuzIndex(),
                                  const HizbIndex(),
                                  const PageIndex(),
                                ]),
                              ),
                            ],
                          ),
                        ),
                        const NotesListView(),
                        const BookmarksListView(),
                      ]),
                ),
              
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TabBar(
                    automaticIndicatorColorAdjustment: true,
                    labelStyle: const TextStyle(
                      fontSize: 12,
                      fontFamily: CommonConstants.cairoFontFamily,
                      fontWeight: FontWeight.bold,
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    // indicator: BoxDecoration(),
                    unselectedLabelColor:
                        Get.isDarkMode ? Colors.white : Colors.black54,
                    labelColor: CustomColors.accentColor,
                    indicatorColor: CustomColors.accentColor,
                    tabs: [
                      DrawerTab(
                        icon: QuranUIIcons.menu,
                        text: "الفهرس".tr,
                      ),
                      DrawerTab(
                        icon: QuranUIIcons.edit,
                        text: "الملاحظات".tr,
                      ),
                      DrawerTab(
                        icon: QuranUIIcons.bookmark,
                        text: "الفواصل".tr,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
