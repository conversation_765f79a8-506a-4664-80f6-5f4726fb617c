import 'package:flutter/material.dart' hide Page;
import 'package:get/get.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/controllers/home_controller.dart';

import 'package:tadars/utils/extensions.dart';
import 'search_text_field.dart';
import 'sura_card.dart';

// class JuzIndex extends StatefulWidget {

//   @override
//   State<JuzIndex> createState() => _JuzIndexState();
// }

class JuzIndex extends StatelessWidget {
  // late List<QuranPage> juz;
  JuzIndex({super.key});

  final List<int> pageNumbers = [
    1,
    22,
    42,
    62,
    82,
    102,
    121,
    142,
    162,
    182,
    201,
    222,
    242,
    262,
    282,
    302,
    322,
    342,
    362,
    382,
    402,
    422,
    442,
    462,
    482,
    502,
    522,
    542,
    562,
    582
  ];

  @override
  Widget build(BuildContext context) {
    //

    var juzs = HomeController.to.quranPageController.pages?.values
            .where((page) => pageNumbers.contains(page.number))
            .toList() ??
        [];
    ValueNotifier<List<Page>> juzsValue = ValueNotifier<List<Page>>(juzs);

    return Column(
      children: [
        SearchTextField(
          hintText: "بحث برقم الجزء".tr,
          onChanged: (value) {
            if (value.trim().isNotEmpty) {
              juzsValue.value = juzs
                  .where((el) =>
                      el.partNumber.toArabicNumber() ==
                      (int.tryParse(value)?.toArabicNumber() ?? value))
                  .toList();
            }
          },
        ),
        Expanded(
          child: ValueListenableBuilder<List<Page>>(
              valueListenable: juzsValue,
              builder: (context, value, child) {
                return ListView.builder(
                    itemCount: value.length,
                    itemBuilder: (context, index) {
                      var surah = value[index].surahs.first;
                      surah.pageNumber = value[index].number;
                      return Column(
                        children: [
                          Container(
                            color: Get.theme.scaffoldBackgroundColor,
                            child: Container(
                              color: Get.theme.primaryColor.withOpacity(0.3),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 4,
                              ),
                              width: double.maxFinite,
                              child: Text(
                                "${"الجزء".tr} ${value[index].partNumber}",
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 11,
                                ),
                              ),
                            ),
                          ),
                          SuraCard(
                            margin: const EdgeInsets.all(8),
                            // item: item.suar[index],
                            item: value[index].surahs.first,
                            index: index,

                            // pageNumber: value[index].number,
                            // title:
                            //     value[index].surahs.first.verses.first.unicode,
                            // titleStyle: TextStyle(
                            //   fontWeight: FontWeight.w400,
                            //   fontFamily: "page_${value[index].number}",
                            //   fontSize: 18,
                            //   color:
                            //       Get.isDarkMode ? Colors.white : Colors.black,
                            // ),

                            // subTitle:
                            //     "${value[index].surahs.first.searchableName.tr} - ${"صفحة".tr} ${value[index].number.toLocaleNumber()}",

                            // suraNumber: value[index].surahNumber,
                          )
                        ],
                      );
                    });
              }),
        )
      ],
    );
  }
}
