import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../features/user_data_sync/data/services/user_sync_service.dart';

import '../../../../../utils/quran_ui_icons.dart';
import 'note_card.dart';

class NotesListView extends StatelessWidget {
  const NotesListView({
    super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var notes = UserSyncService.instance.notes.values.toList();

      return Builder(
        builder: (context) {
          notes.sort((i, j) => j.createdAt!.compareTo(i.createdAt!));
          return notes.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      QuranUIIcons.edit1,
                      size: 50,
                      color: Theme.of(context).primaryColor,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 32.0),
                      child: Text('لم يتم إضافة أي عنصر لقائمة الملاحظات'.tr),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: notes.length,
                itemBuilder: (context, i) {
                  return NoteCard(
                    item: notes[i],
                    margin: i == 0 ? const EdgeInsets.all(8) : null,
                  );
                },
              );
        },
      );
    });
  }
}
