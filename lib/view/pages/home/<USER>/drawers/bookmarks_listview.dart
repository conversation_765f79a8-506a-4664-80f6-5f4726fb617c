import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../features/user_data_sync/data/services/user_sync_service.dart';
import '../../../../../features/user_data_sync/models/bookmark.dart';
import '../../../../../utils/quran_ui_icons.dart';
import 'bookmark_card.dart';

class BookmarksListView extends StatelessWidget {
  const BookmarksListView({
    super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
        var bookmarks = UserSyncService.instance.bookmarks.values.toList();
        bookmarks.sort(
            (Bookmark i, Bookmark j) => j.createdAt.compareTo(i.createdAt));
        return bookmarks.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      QuranUIIcons.bookmark,
                      size: 50,
                      color: Theme.of(context).primaryColor,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 32.0),
                    child: Text('لم يتم إضافة أي عنصر لقائمة الفواصل'.tr),
                    )
                  ],
                ),
              )
            : ListView.builder(
                itemCount: bookmarks.length,
                itemBuilder: (context, i) {
                  return BookmarkCard(
                    item: bookmarks[i],
                    margin: i == 0 ? const EdgeInsets.all(8) : null,
                  );
                },
              );
      },
    );
  }
}
