import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:jiffy/jiffy.dart';
import 'package:quran_core/quran_core.dart';
import 'package:sqlbrite/sqlbrite.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/controllers/setting_controller.dart';
import 'package:tadars/controllers/tafseer_controller.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';
import 'package:tadars/features/intro_verse/widgets/verse_notfication_end_drawer_item.dart';
import 'package:tadars/features/translation/translation.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/services/audio_player_service.dart';
import 'package:tadars/services/sync_service.dart';
import 'package:tadars/utils/common.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tadars/view/components/dialogs/sources_bookmark_dialog.dart';
import 'package:tadars/view/components/dialogs/tdbr_bookmark_dialog.dart';
import '../../../../components/custom_slider/custom_slider.dart';
import '../../../../components/dialogs/tadars_category_dialog.dart';
import '../../../../components/dialogs/waqafat_dialog.dart';
import '../../../../components/form_fields/choice_button_field.dart';
import '../../../../components/form_fields/color_picker_field.dart';
import 'setting_label.dart';
import 'package:path/path.dart';

class CustomEndDrawer extends StatelessWidget {
  const CustomEndDrawer({super.key});
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        children: [
          Container(
            color: Get.theme.primaryColor.withOpacity(0.1),
            padding: const EdgeInsets.only(
              bottom: 25,
              top: 16,
              right: 16,
              left: 16,
            ),
            child: Text(
              "الإعدادات".tr,
              style: const TextStyle(fontSize: 35, fontWeight: FontWeight.bold),
            ),
          ),
          ChoiceButtonField<String>(
            initialValue: Get.locale?.languageCode ?? "ar",
            margin: const EdgeInsets.all(16),
            onChanged: (value) {
              if (value != null) {
                Jiffy.setLocale(value);
                HomeController.to.changeAppLocale(value);
              }
            },
            items: [
              ChoiceItem(label: 'عربي', value: "ar"),
              ChoiceItem(label: 'English', value: "en"),
            ],
          ),
          SettingLabel(label: 'تخصيص الالوان'.tr),
          Obx(
            () => Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: ColorPickerFormField(
                initialValue:
                    HomeController.to.pageColor.value ??
                    Color(Get.theme.primaryColor.value),
                onChanged: (value) {
                  HomeController.to.changeThemeMode(false);
                  Get.changeThemeMode(ThemeMode.light);
                  if (value != null) HomeController.to.changePageColor(value);
                  if (kDebugMode) {
                    print(Get.isDarkMode);
                  }
                },
                colors: const [
                  Color(0xffffffff),
                  Color.fromARGB(255, 92, 138, 80),
                  Color.fromARGB(255, 66, 78, 154),
                  Color.fromARGB(255, 87, 82, 92),
                  Color.fromARGB(255, 164, 85, 70),
                  // Color(0xFF181A1F),
                ],
              ),
            ),
          ),
          SettingLabel(label: 'المظهر'.tr),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "الوضع الداكن".tr,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: Hive.box(
                    Boxes.settings,
                  ).listenable(keys: [SettingsConstants.appDarkModeEnabledKey]),
                  builder: (context, Box box, child) {
                    return FlutterSwitch(
                      inactiveIcon: Icon(
                        QuranUIIcons.sun,
                        color: CustomColors.accentColor,
                      ),
                      activeIcon: Icon(
                        QuranUIIcons.moon,
                        color: CustomColors.accentColor,
                      ),
                      activeColor: CustomColors.accentColor,
                      //toggleColor: Get.theme.primaryColor,
                      height: 27,
                      width: 50,
                      onToggle: (bool value) {
                        HomeController.to.changeThemeMode(value);
                      },
                      value: box.get(SettingsConstants.appDarkModeEnabledKey),
                    );
                  },
                ),
              ],
            ),
          ),
          SettingLabel(label: 'حجم الخط'.tr),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Text(
              "التغيير يتم في التفسير والوقفات".tr,
              style: const TextStyle(fontSize: 16),
            ),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: CustomSlider(
              margin: const EdgeInsets.only(bottom: 16),
              onChanged: (value) {
                if (kDebugMode) {
                  print(value * 0.6);
                }
                //HomeController.to.pageFontSize.value = value * 0.6;
                HomeController.to.changeFontSize(value * 0.6);
              },
              min: 20,
              max: 100,
              value: HomeController.to.pageFontSize.value ~/ 0.6,
            ),
          ),
          Obx(
            () => Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Text(
                "معاينة حجم الخط".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: HomeController.to.pageFontSize.value,
                ),
              ),
            ),
          ),

          SettingLabel(label: 'حجم خط المصحف'.tr),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Text(
              "التغيير يتم في صفحات المصحف".tr,
              style: const TextStyle(fontSize: 16),
            ),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: CustomSlider(
              margin: const EdgeInsets.only(bottom: 16),
              onChanged: (value) {
                if (kDebugMode) {
                  Get.log("${value / 10}");
                }
                //HomeController.to.pageFontSize.value = value * 0.6;
                HomeController.to.changeQuranFontScale(value / 10);
              },
              min: 10,
              max: 26,
              value: (HomeController.to.quranPageController.scale * 10).toInt(),
            ),
          ),
          StreamBuilder<double>(
            stream: HomeController.to.quranPageController.scaleStream,
            builder: (context, snapshot) {
              return Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8,
                ),
                child: Text(
                  "معاينة حجم الخط".tr,
                  textAlign: TextAlign.center,
                  textScaler: TextScaler.linear(snapshot.data ?? 1.0),
                  style: TextStyle(
                    fontSize: HomeController.to.pageFontSize.value,
                  ),
                ),
              );
            },
          ),

          SettingLabel(label: 'القراءة'.tr),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "إبقاء ضوء الشاشة وقت القراءة".tr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ValueListenableBuilder(
                  builder: (BuildContext context, value, Widget? child) {
                    return FlutterSwitch(
                      padding: 3,
                      activeColor: CustomColors.accentColor,
                      //toggleColor: Get.theme.primaryColor,
                      height: 25,
                      width: 50,
                      onToggle: (bool value) {
                        HomeController.to.changeWakelockEnableStatus(value);
                      },
                      value: HomeController.to.isWAkelockEnabled(),
                    );
                  },
                  valueListenable: Hive.box(
                    Boxes.settings,
                  ).listenable(keys: [SettingsConstants.wakelockEnableKey]),
                ),
              ],
            ),
          ),
          const Divider(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "إظهار الساعة".tr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ValueListenableBuilder(
                  builder: (BuildContext context, value, Widget? child) {
                    return FlutterSwitch(
                      padding: 3,
                      activeColor: CustomColors.accentColor,
                      //toggleColor: Get.theme.primaryColor,
                      height: 25,
                      width: 50,
                      onToggle: (bool value) {
                        HomeController.to.changeClockVisibility(value);
                      },
                      value: HomeController.to.isClockVisible(),
                    );
                  },
                  valueListenable: Hive.box(
                    Boxes.settings,
                  ).listenable(keys: [SettingsConstants.clockVisibilityKey]),
                ),
              ],
            ),
          ),
          const Divider(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "إظهار شريط التنقل".tr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ValueListenableBuilder(
                  builder: (BuildContext context, box, Widget? child) {
                    return FlutterSwitch(
                      padding: 3,
                      activeColor: CustomColors.accentColor,
                      //toggleColor: Get.theme.primaryColor,
                      height: 25,
                      width: 50,
                      onToggle: (bool value) {
                        box.put(SettingsConstants.navVisibilityKey, value);
                      },
                      value: box.get(
                        SettingsConstants.navVisibilityKey,
                        defaultValue: true,
                      ),
                    );
                  },
                  valueListenable: Hive.box(
                    Boxes.settings,
                  ).listenable(keys: [SettingsConstants.navVisibilityKey]),
                ),
              ],
            ),
          ),
          SettingLabel(label: 'مشغل الصوت'.tr),

          InkWell(
            onTap: () async {
              Common.showChangeReciterBottomSheet();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "القارئ الافتراضي".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        ListenableBuilder(
                          listenable: Hive.box(
                            Boxes.settings,
                          ).listenable(keys: [SettingsConstants.reciter]),
                          builder: (context, child) {
                            return FutureBuilder(
                              future:
                                  QuranReciterDatabaseProvider.getReciterById(
                                    BoxesHelper.getSettingReciterId(),
                                  ),
                              builder: (context, snapshot) {
                                var name =
                                    Get.locale?.languageCode == "ar"
                                        ? snapshot.data?.name
                                        : snapshot.data?.enName;

                                return Text(
                                  name ?? "",
                                  style: TextStyle(color: Colors.grey.shade700),
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              var loopCount = BoxesHelper.getLoopCount();
              loopCount++;

              if (loopCount > 5) {
                loopCount = 1;
              }
              BoxesHelper.setLoopCount(loopCount);

              QuranAudioService.setVerseLoopCount(loopCount);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "عدد مرات تكرار الآية".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        StreamBuilder(
                          stream: QuranAudioService.verseLoopCount.stream,
                          builder: (context, snapshot) {
                            return Text(
                              (CommonConstants.getLoopStrings()[snapshot.data ??
                                      1] ??
                                  ""),
                              style: TextStyle(color: Colors.grey.shade700),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),

          // CustomGroupRadioButton<int>(
          //   items: [
          //     ChoiceItem(label: "الترجمة الأورومية", value: 1),
          //     ChoiceItem(label: "2 الترجمة الأورومية", value: 2),
          //   ],
          // )
          SettingLabel(label: 'التدارس'.tr),
          InkWell(
            onTap: () {
              Get.back();
              Get.dialog(const WaqafatDialog());
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "الوقفات".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Get.back();
              Get.dialog(const TdbrBookmarkDialog());
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "الوقفات المفضلة".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Get.back();
              Get.dialog(const SourcesBookmarkDialog());
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "المصادر المفضلة".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Get.dialog(const TadarsCategoryDialog());
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "تصنيفات التدارس للعرض والمزامنة".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "عرض عدد الوقفات أمام كل آية".tr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ValueListenableBuilder(
                  builder: (BuildContext context, Box value, Widget? child) {
                    return FlutterSwitch(
                      padding: 3,
                      activeColor: CustomColors.accentColor,
                      //toggleColor: Get.theme.primaryColor,
                      height: 25,
                      width: 50,
                      onToggle: (bool value) {
                        Hive.box(Boxes.settings).put(
                          SettingsConstants.showWaqfatCountKey,
                          !Hive.box(Boxes.settings).get(
                            SettingsConstants.showWaqfatCountKey,
                            defaultValue: false,
                          ),
                        );
                      },
                      value: value.get(
                        SettingsConstants.showWaqfatCountKey,
                        defaultValue: false,
                      ),
                    );
                  },
                  valueListenable: Hive.box(
                    Boxes.settings,
                  ).listenable(keys: [SettingsConstants.showWaqfatCountKey]),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Obx(
            () => InkWell(
              onTap:
                  SyncService.instance.isSyncing.value
                      ? null
                      : () {
                        Get.back();
                        SyncService.instance.sync();
                      },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 16,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        SyncService.instance.isSyncing.value
                            ? "جاري المزامنة ...".tr
                            : "مزامنة الآن".tr,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          SettingLabel(label: 'الختم والحفظ'.tr),
          InkWell(
            onTap: () async {
              Get.toNamed(Routes.werdsPage);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "الورد اليومي".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (Platform.isAndroid) EndDrawerItem(),

          if (Platform.isIOS) VerseNotficationEndDrawerItem(),


          // InkWell(
          //   onTap: () async {
          //     var databasesPath = await getDatabasesPath();
          //     var path = join(databasesPath, "tdrs_db.sqlite");
          //     Share.shareXFiles([XFile(path)]);

          //   },
          //   child: Container(
          //     padding: const EdgeInsets.symmetric(
          //       horizontal: 16.0,
          //       vertical: 16,
          //     ),
          //     child: Row(
          //       children: [Expanded(child: Text("استخراج قاعدة البيانات".tr))],
          //     ),
          //   ),
          // ),
          SettingLabel(label: 'مصاحف وترجمات'.tr),
          InkWell(
            onTap: () {
              SettingController.instance.showBooksDialog();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "المصحف الافتراضي".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        Obx(
                          () => Text(
                            SettingController.instance.quranBook.value.title,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(color: Colors.grey.shade700),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Get.toNamed(Routes.booksPage);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "قائمة الكتب".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'المصاحف والترجمات القابلة للتحميل'.tr,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),

          const Divider(height: 1),

          ValueListenableBuilder<Box<dynamic>>(
            valueListenable: Hive.box(Boxes.settings).listenable(),
            builder: (context, box, child) {
              return FutureBuilder(
                future: TranslationSqlHelper.getTranslationBooks(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting ||
                      (snapshot.data?.isEmpty ?? true)) {
                    return SizedBox.shrink();
                  }

                  var defulatTranslation = snapshot.data?.firstWhere(
                    (e) =>
                        e.id ==
                        box.get(
                          SettingsConstants.translationBookId,
                          defaultValue: 1,
                        ),
                  );
                  return InkWell(
                    onTap: () {
                      box.put(
                        SettingsConstants.translationBookId,

                        snapshot.data
                            ?.firstWhere(
                              (e) =>
                                  e.id !=
                                  box.get(
                                    SettingsConstants.translationBookId,
                                    defaultValue: 1,
                                  ),
                            )
                            .id,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "الترجمة الافتراضية".tr,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                  ),
                                ),

                                Text(
                                  Get.locale?.languageCode == "ar"
                                      ? defulatTranslation?.arabicName ?? ""
                                      : defulatTranslation?.name ?? "",
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.chevron_right,
                            color: Get.theme.colorScheme.secondary,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),

          SettingLabel(label: 'التفسير'.tr),
          InkWell(
            onTap: () {
              TafseerController.instance.showTafseerDialog();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "التفسير الافتراضي".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        Obx(
                          () => Text(
                            TafseerController.instance.tafseer.value?.title ??
                                "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(color: Colors.grey.shade700),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Get.toNamed(Routes.tafseerPage);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "كتب التفسير".tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'كتب التفسير المحملة'.tr,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(color: Colors.grey.shade700),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Get.theme.colorScheme.secondary,
                  ),
                ],
              ),
            ),
          ),
          SettingLabel(label: 'أخرى'.tr),
          InkWell(
            onTap: () {
              Get.toNamed(Routes.aboutPage);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "عن التطبيق".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () {
              Share.share("share_app".tr);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "مشاركة التطبيق".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: () async {
              CommonFunctions.contact();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "تواصل معنا".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),

          InkWell(
            onTap: () async {
              if (Platform.isAndroid) {
                CommonFunctions.launchURL(
                  "https://play.google.com/store/apps/details?id=com.tadarose_quran",
                );
              } else if (Platform.isIOS) {
                String appId = "1409992825";
                CommonFunctions.launchURL(
                  "https://apps.apple.com/app/id$appId?action=write-review",
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "تقييم التطبيق".tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Divider(height: 1),
          ValueListenableBuilder<Box<dynamic>>(
            valueListenable: Hive.box(Boxes.settings).listenable(),
            builder: (context, box, child) {
              return InkWell(
                onTap: () {
                  if (box.get("user_token") != null) {
                    Get.defaultDialog<bool?>(
                      title: "تأكيد تسجيل الخروج".tr,
                      middleText: "هل أنت متأكد من تسجيل الخروج؟".tr,
                      textConfirm: "نعم".tr,
                      textCancel: "لا".tr,
                      buttonColor: CustomColors.primaryColor,
                      confirmTextColor: Colors.white,
                      cancelTextColor: CustomColors.primaryColor,
                      onConfirm: () {
                        Hive.box(Boxes.settings).delete("user_token");
                        Get.back();
                      },
                      onCancel: () {
                        Get.back();
                      },
                    );
                  } else {
                    Get.back();
                    CommonFunctions.showLoginDailog();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          box.get("user_token") != null
                              ? "تسجيل الخروج".tr
                              : "تسجيل الدخول".tr,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
