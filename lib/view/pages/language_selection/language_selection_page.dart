import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/controllers/language_selection_controller.dart';
import 'package:tadars/utils/constants/custom_colors.dart';
import 'package:tadars/view/widgets/language_loading_dialog.dart';

class LanguageSelectionPage extends StatelessWidget {
  const LanguageSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LanguageSelectionController());

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Image.asset(
                  'assets/images/new_logo.png',
                  width: 150,
                  height: 150,
                ),
                const SizedBox(height: 30),
                Text(
                  'اختر لغة التطبيق',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: CustomColors.primaryColor,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Choose App Language',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: CustomColors.primaryColor,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Choisir la langue de l\'application',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: CustomColors.primaryColor,
                  ),
                ),
                const SizedBox(height: 50),
                _buildLanguageOption(controller, 'ar', 'العربية'),
                _buildLanguageOption(controller, 'en', 'English'),
                _buildLanguageOption(controller, 'fr', 'Français'),
                const SizedBox(height: 50),
                Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value
                    ? null
                            : () {
                              controller.processLanguageSelection();
                            },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: CustomColors.primaryColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 50,
                      vertical: 15,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    'متابعة / Continue',
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                    ),
                  ),
                )),
              ],
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(LanguageSelectionController controller, String langCode, String langName) {
    return Obx(() {
      bool isSelected = controller.selectedLanguage.value == langCode;
      return GestureDetector(
        onTap: () {
          controller.setSelectedLanguage(langCode);
        },
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
          decoration: BoxDecoration(
            color: isSelected ? CustomColors.primaryColor.withAlpha(25) : Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: isSelected ? CustomColors.primaryColor : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              isSelected
                  ? Icon(
                      Icons.check_circle,
                      color: CustomColors.primaryColor,
                      size: 24,
                    )
                  : Icon(
                      Icons.circle_outlined,
                      color: Colors.grey,
                      size: 24,
                    ),
              const SizedBox(width: 15),
              Text(
                langName,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? CustomColors.primaryColor : Colors.black,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
