import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:tadars/controllers/tafseer_controller.dart';
import 'package:tadars/models/hive/quran_tafseer.dart';

import '../../../../../helpers/boxes_helper.dart';
import '../../../../../utils/common_styles.dart';
import '../../../../../utils/constants/custom_colors.dart';
import '../../../../../utils/quran_ui_icons.dart';

class TafseerCard extends StatelessWidget {
  const TafseerCard({
    Key? key,
    required this.item,
    this.margin,
  }) : super(key: key);
  final QuranTafseer item;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    var notDeletable = item.id == 6 || item.id == 27;
    

    return Obx(
      () {
        var isDownloaded = BoxesHelper.isTafseerDownloaded(item.name);
        var downloading =
            TafseerController.instance.downloadingItems[item.name];
        return Container(
          margin:
              margin ?? const EdgeInsets.only(right: 16, left: 16, bottom: 12),
          child: Slidable(
            endActionPane: (!isDownloaded)
                ? null
                : ActionPane(
                    motion: const ScrollMotion(),
                    extentRatio: 0.25,
                    children: <Widget>[
                      SlidableAction(
                        label: 'حذف'.tr,
                        backgroundColor: Colors.redAccent,
                        icon: Icons.remove,
                        onPressed: (context) {
                          Get.defaultDialog(
                            title: 'حذف التفسير'.tr,
                            middleText: 'هل أنت متأكد من حذف هذا التفسير؟'.tr,
                            textConfirm: 'نعم'.tr,
                            textCancel: 'لا'.tr,
                            confirmTextColor: Colors.white,
                            onConfirm: () {
                              BoxesHelper.deleteTafseerFromDownloaded(
                                  item.name);
                              TafseerController.instance.downloadingItems
                                  .refresh();
                              Get.back();
                            },
                          );
                        },
                      ),
                    ],
                  ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: CommonStyles.borderRadius,
                color: Get.theme.scaffoldBackgroundColor,
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: CommonStyles.borderRadius,
                child: ListTile(
                  contentPadding: const EdgeInsetsDirectional.only(
                    start: 16,
                    bottom: 2,
                    top: 2,
                    end: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: CommonStyles.borderRadius,
                  ),
                  // leading: Icon(
                  //   QuranUIIcons2.book,
                  //   color: CustomColors.primaryColor,
                  // ),
                  trailing: notDeletable
                      ? null
                      : downloading != null
                          ? Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CircularPercentIndicator(
                                radius: 11,
                                lineWidth: 3,
                                progressColor: CustomColors.accentColor,
                                percent:
                                    downloading.progress / downloading.total,
                              ),
                            )
                          : isDownloaded
                              ? const SizedBox(
                                  width: 0,
                                )
                              : InkWell(
                                  borderRadius: CommonStyles.borderRadius,
                                  onTap: isDownloaded
                                      ? null
                                      : () {
                                          TafseerController.instance
                                              .downloadTafseer(item.name);
                                        },
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Icon(
                                      isDownloaded
                                          ? QuranUIIcons.trash
                                          : QuranUIIcons.download,
                                      color: isDownloaded
                                          ? Colors.redAccent
                                          : CustomColors.accentColor,
                                      size: 20,
                                    ),
                                  ),
                                ),
                  title: Text(
                    item.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 15,
                    ),
                  ),
                  subtitle: Text(
                    item.author,
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
