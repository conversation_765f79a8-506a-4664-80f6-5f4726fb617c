import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/view/pages/tafseer/components/tafseer_card.dart';

class TafseerPage extends StatelessWidget {
  const TafseerPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var tafseers = BoxesHelper.getAllTafseers();
    // sort tafseers by is downloaded
    tafseers.sort((a, b) => a.downloaded ? -1 : 1);
    return Scaffold(
      appBar: AppBar(
        title: const Text('كتب التفسير'),
      ),
      body: Container(
        color: Get.theme.primaryColor.withOpacity(0.1),
        child: ListView.builder(
          padding: const EdgeInsets.only(top: 16),
          itemCount: tafseers.length,
          itemBuilder: (context, index) {
            return TafseerCard(item: tafseers[index]);
          },
        ),
      ),
    );
  }
}
