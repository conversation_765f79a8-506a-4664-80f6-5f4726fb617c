import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:tadars/services/loading_services.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'utils/app_pages.dart';
import 'utils/app_translations.dart';
import 'utils/constants/routes.dart';
import 'utils/constants/themes.dart';

class QuranApp extends StatelessWidget {
  const QuranApp({super.key});

  @override
  Widget build(BuildContext context) {
    Get.lazyPut(() => LoadingService());
    var localeCode = Hive.box(Boxes.settings).get(
        SettingsConstants.appLocaleCodeKey,
        defaultValue: "ar");
    var isDark = Hive.box(Boxes.settings)
        .get(SettingsConstants.appDarkModeEnabledKey, defaultValue: false);

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'تدارس القرآن'.tr,
      darkTheme: AppThemes.darkTheme(localeCode),
      theme: AppThemes.lightTheme(localeCode),
      themeMode: isDark ? ThemeMode.dark : ThemeMode.light,
      fallbackLocale: const Locale("ar"),
      locale: Locale(localeCode),
      translations: AppTranslations(),
      getPages: appPages, 
      initialRoute: Routes.splashPage,
    
    );
  }
}
