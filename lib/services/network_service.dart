import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tadars/services/loading_services.dart';

import '../utils/common_functions.dart';

class NetworkServices extends GetxService {
  static NetworkServices instance = Get.find();
  var connected = false.obs;

  @override
  Future<void> onInit() async {
    var connectivity = Connectivity();
    connected.value = !(await connectivity.checkConnectivity())
        .contains(ConnectivityResult.none);

    print("connect result ${connected.value}");

    connectivity.onConnectivityChanged.listen((result) {
      print("connect result $result");

      if (!result.contains(ConnectivityResult.none)) {
        connected.value = true;
      } else {
        connected.value = false;
      }
    });
    super.onInit();
  }

  Future<NetworkServices> init() async {
    return this;
  }

  Future<bool> isConnected([bool showErrorMess = true]) async {
    var connectivity = Connectivity();
    connected.value = !(await connectivity.checkConnectivity())
        .contains(ConnectivityResult.none);

    if (showErrorMess && (!connected.value)) {
      CommonFunctions.showErrorMessage("انت غير متصل بالانترنت".tr);
    }

    return connected.value;
  }

  Future<void> checkConnectivity(VoidCallback connectedCallback,
      [VoidCallback? notConnectedCallback]) async {
    try {
      var connectivity = Connectivity();
      connected.value = !(await connectivity.checkConnectivity())
          .contains(ConnectivityResult.none);

      if (!connected.value) {
        LoadingService.to.hide();
        CommonFunctions.showErrorMessage("انت غير متصل بالانترنت".tr);
      } else {
        connectedCallback();
      }
    } catch (e) {
      if (notConnectedCallback == null) {
        CommonFunctions.showErrorMessage("حدث خطاء غير معروف".tr);
        if (kDebugMode) {
          print('conntection error $e');
        }
      } else {
        notConnectedCallback();
      }
    }
  }
}
