//sync table 
//action 0
 // print(element.table);
            // if (element.table == 'tdbr_ayat') {
            //   //
            //   BoxesHelper.deleteTdbr('tdbr_verses', element.id);
            // } else {
            //   BoxesHelper.deleteTdbr(element.table, element.id);
            // }
            // if (element.table == Boxes.tdbrVerses) {
            //   BoxesHelper.deleteTdbrAyat(element.id);
            // } else if (element.table == Boxes.tdbrConsiders) {
            //   BoxesHelper.deleteTdbrConsider(element.id);
            // } else if (element.table == Boxes.tdbrLinks) {
            //   BoxesHelper.deleteTdbrLinks(element.id);
            // } else if (element.table == Boxes.tdbrMedia) {
            //   BoxesHelper.deleteTdbrMedia(element.id);
            // } else if (element.table == Boxes.tdbrPrays) {
            //   BoxesHelper.deleteTdbrPray(element.id);
            // } else if (element.table == Boxes.tdbrQuestions) {
            //   BoxesHelper.deleteTdbrQuestions(element.id);
            // } else if (element.table == Boxes.tdbrRules) {
            //   BoxesHelper.deleteTdbrRules(element.id);
            // } else if (element.table == Boxes.tdbrSources) {
            //   BoxesHelper.deleteTdbrSources(element.id);
            // } else if (element.table == Boxes.tdbrSuggests) {
            //   BoxesHelper.deleteTdbrSuggest(element.id);
            // } else if (element.table == Boxes.tdbrTadabors) {
            //   BoxesHelper.deleteTdbrTadabors(element.id);
            // } else if (element.table == Boxes.tdbrComparables) {
            //   BoxesHelper.deleteTdbrComparables(element.id);
            // } else if (element.table == Boxes.tdbrEloquences) {
            //   BoxesHelper.deleteTdbrEloquences(element.id);
            // } else if (element.table == Boxes.tdbrTadaborCategories) {
            //   BoxesHelper.deleteTdbrLinks(element.id);
            // } else if (element.table == Boxes.likes) {
            //   BoxesHelper.deleteLike(element.id);
            // } else if (element.table == Boxes.quranTafseer) {
            //   BoxesHelper.deleteTafseer(element.id);
            // } else if (element.table == Boxes.shares) {
            //   BoxesHelper.deleteShare(element.id);
            // } else if (element.table == Boxes.quranBooks) {
            //   BoxesHelper.deleteQuranBook(element.id);
            // } else if (element.table == Boxes.users) {
            //   BoxesHelper.deleteUser(element.id);
            // }

 //action 2

   // Hive.box(element.table).get(element.id)?.status = 0;

            // if (element.table == Boxes.tdbrVerses) {
            //   Hive.box<TdbrVerse>(Boxes.tdbrVerses).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.tdbrConsiders) {
            //   Hive.box<TdbrConsider>(Boxes.tdbrConsiders)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.tdbrLinks) {
            //   Hive.box<TdbrLink>(Boxes.tdbrLinks).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.tdbrMedia) {
            //   Hive.box<TdbrMedia>(Boxes.tdbrMedia).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.tdbrPrays) {
            //   Hive.box<TdbrPray>(Boxes.tdbrPrays).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.tdbrQuestions) {
            //   Hive.box<TdbrQuestion>(Boxes.tdbrQuestions)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.tdbrRules) {
            //   Hive.box<TdbrRule>(Boxes.tdbrRules).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.tdbrSources) {
            //   Hive.box<TdbrSource>(Boxes.tdbrSources).get(element.id)?.status =
            //       0;
            // } else if (element.table == Boxes.tdbrSuggests) {
            //   Hive.box<TdbrSuggest>(Boxes.tdbrSuggests)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.tdbrTadabors) {
            //   Hive.box<TdbrTadabor>(Boxes.tdbrTadabors)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.tdbrComparables) {
            //   Hive.box<TdbrComparable>(Boxes.tdbrComparables)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.tdbrEloquences) {
            //   Hive.box<TdbrEloquence>(Boxes.tdbrEloquences)
            //       .get(element.id)
            //       ?.status = 0;
            // }

            // // else if (element.table == Boxes.tdbrTadaborCategories) {
            // //   Hive.box<TdbrTadaborCategory>(Boxes.tdbrTadaborCategories).get(element.id)?.status = 0;
            // // }

            // else if (element.table == Boxes.likes) {
            //   Hive.box<Like>(Boxes.likes).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.quranTafseer) {
            //   Hive.box<QuranTafseer>(Boxes.quranTafseer)
            //       .get(element.id)
            //       ?.status = 0;
            // } else if (element.table == Boxes.shares) {
            //   Hive.box<Share>(Boxes.shares).get(element.id)?.status = 0;
            // } else if (element.table == Boxes.quranBooks) {
            //   Hive.box<QuranBook>(Boxes.quranBooks).get(element.id)?.status = 0;
            // }

            // // else if (element.table == Boxes.users) {
            // //   Hive.box<User>(Boxes.users).get(element.id)?.status = 0;
            // // }

  // action 3
    // Hive.box(element.table).get(element.id)?.status = 1;

            // if (element.table == Boxes.tdbrVerses) {
            //   Hive.box<TdbrVerse>(Boxes.tdbrVerses).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.tdbrConsiders) {
            //   Hive.box<TdbrConsider>(Boxes.tdbrConsiders)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.tdbrLinks) {
            //   Hive.box<TdbrLink>(Boxes.tdbrLinks).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.tdbrMedia) {
            //   Hive.box<TdbrMedia>(Boxes.tdbrMedia).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.tdbrPrays) {
            //   Hive.box<TdbrPray>(Boxes.tdbrPrays).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.tdbrQuestions) {
            //   Hive.box<TdbrQuestion>(Boxes.tdbrQuestions)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.tdbrRules) {
            //   Hive.box<TdbrRule>(Boxes.tdbrRules).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.tdbrSources) {
            //   Hive.box<TdbrSource>(Boxes.tdbrSources).get(element.id)?.status =
            //       1;
            // } else if (element.table == Boxes.tdbrSuggests) {
            //   Hive.box<TdbrSuggest>(Boxes.tdbrSuggests)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.tdbrTadabors) {
            //   Hive.box<TdbrTadabor>(Boxes.tdbrTadabors)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.tdbrComparables) {
            //   Hive.box<TdbrComparable>(Boxes.tdbrComparables)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.tdbrEloquences) {
            //   Hive.box<TdbrEloquence>(Boxes.tdbrEloquences)
            //       .get(element.id)
            //       ?.status = 1;
            // }

            // // else if (element.table == Boxes.tdbrTadaborCategories) {
            // //   Hive.box<TdbrTadaborCategory>(Boxes.tdbrTadaborCategories).get(element.id)?.status = 0;
            // // }

            // else if (element.table == Boxes.likes) {
            //   Hive.box<Like>(Boxes.likes).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.quranTafseer) {
            //   Hive.box<QuranTafseer>(Boxes.quranTafseer)
            //       .get(element.id)
            //       ?.status = 1;
            // } else if (element.table == Boxes.shares) {
            //   Hive.box<Share>(Boxes.shares).get(element.id)?.status = 1;
            // } else if (element.table == Boxes.quranBooks) {
            //   Hive.box<QuranBook>(Boxes.quranBooks).get(element.id)?.status = 1;
            // }

            // // else if (element.table == Boxes.users) {
            // //   Hive.box<User>(Boxes.users).get(element.id)?.status = 0;
            // // }