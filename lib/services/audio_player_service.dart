// import 'package:audio_service/audio_service.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:quran_core/quran_core.dart';
// import 'package:tadars/helpers/reciter_sql_helper.dart';
// import 'package:tadars/services/network_service.dart';

// import '../controllers/home_controller.dart';
// import '../helpers/quran_audio_handler.dart';
// import '../helpers/boxes_helper.dart';
// import '../models/view_models/download_vm.dart';

// class AudioPlayerService extends GetxService {
//   // late AudioHandler audioHandler;

//   late QuranAudioHandler audioHandler;

//   static AudioPlayerService get to => Get.find<AudioPlayerService>();
//   var showPlayer = false.obs;
//   var isPlaying = false.obs;
//   var isLoading = false.obs;
//   var isDownloaded = false.obs;
//   // var loopCount = 1.obs;
//   var currentSura = 0.obs;
//   var currentVerse = 0.obs;
//   var currentPosition = Duration.zero.obs;
//   var totalDuration = Duration.zero.obs;
//   var currentMediaId = "".obs;
//   var downloadingItems = <String, DownloadVM>{}.obs;
//   Rx<Reciter?> currentReciter = Rx<Reciter?>(null);
//   // Rx<MediaItem?> currentMediaItem = Rx<MediaItem?>(null);

//   //%new%
//   void toggleLoop() {
//     var loopCount = BoxesHelper.getLoopCount();
//     loopCount++;

//     if (loopCount > 5) {
//       loopCount = 1;
//     }
//     BoxesHelper.setLoopCount(loopCount);

//     QuranAudioService.setVerseLoopCount(loopCount);
//   }

//   Future<void> changeReciter(int id) async {
//     QuranAudioService.setReciter(id);

//     // BoxesHelper.setSettingReciterId(id);
//     currentReciter.value =
//         await QuranReciterDatabaseProvider.getReciterById(id);
//     // if (!(currentReciter.value?.downloaded ?? false)) {
//     //   await CommonFunctions.downloadReciterTiming(currentReciter.value);
//     // }
//     // // currentReciter.value = await ReciterSqlHelper.getReciterById(id);
//     // if (currentReciter.value?.id != null) {
//     //   QuranAudioService.setReciter(id);
//     // }
//   }

//   checkNetworkOrDownload(Function onSuccess) async {
//     var suraNumber = currentSura.value;
//     String url =
//         "${currentReciter.value?.filesUrl}${getFilesUrlSuffix(currentReciter.value!.id, suraNumber)}${suraNumber.toString().padLeft(3, "0")}.mp3";
//     var file = BoxesHelper.getDownloadedFile(url);
//     if (file != null ||
//         (await NetworkServices.instance.isConnected(file == null))) {
//       onSuccess();
//     }
//   }

// //handel verse change
//   void handelVerseChange(args) {
//     currentVerse.value = args["verse_number"];
//     currentSura.value = args["sura_number"];

//     HomeController.to.goToVerse(
//       currentSura.value,
//       currentVerse.value,
//     );
//   }

// //handel playback state
//   void handelPlaybackState() {
//     audioHandler.playbackState.distinct().listen((state) {
//       if (kDebugMode) {
//         print("processing state ${state.processingState}");
//       }
//       if (kDebugMode) {
//         print("playing ${state.playing}");
//       }
//       isPlaying.value = state.playing;
//       switch (state.processingState) {
//         case AudioProcessingState.loading:
//           showPlayer.value = true;
//           isLoading.value = true;
//           break;
//         case AudioProcessingState.buffering:
//           showPlayer.value = true;
//           isLoading.value = false;
//           break;
//         case AudioProcessingState.ready:
//           showPlayer.value = true;
//           isLoading.value = false;
//           break;
//         case AudioProcessingState.idle:
//           showPlayer.value = false;
//           isLoading.value = false;
//           break;
//         case AudioProcessingState.completed:
//           showPlayer.value = false;
//           isLoading.value = false;
//           audioHandler.stop();
//           break;
//         case AudioProcessingState.error:
//           Get.snackbar(
//             "خطأ",
//             "خطأ في مشغل الصوت",
//             backgroundColor: Colors.red,
//             colorText: Colors.white,
//           );
//           break;
//         default:
//       }
//     });
//   }

//   @override
//   void onInit() async {
//     // loopCount.value = BoxesHelper.getLoopCount();
//     // var reciterId = await BoxesHelper.getSettingReciterId();
//     // currentReciter.value = await ReciterSqlHelper.getReciterById(reciterId);

//     audioHandler.customEvent.distinct().listen((event) {
//       switch (event["type"]) {
//         case "verseChange":
//           handelVerseChange(event["args"]);
//           break;
//         case "setAudioSourceErrorEvent":
//           Get.snackbar(
//             "خطأ",
//             "تأكد من إتصالك بالانترنت ثم أعد المحاولة",
//             backgroundColor: Colors.red,
//             colorText: Colors.white,
//           );
//           break;
//         default:
//       }
//     });
//     // audioHandler.customEvent.distinct().listen((event) {
//     //   if (kDebugMode) {
//     //     print("custom event $event");
//     //   }
//     //   switch (event["type"]) {
//     //     case "versePosition":
//     //       currentVerse.value = event["args"]["verse_number"];
//     //       currentSura.value = event["args"]["sura_number"];
//     //       HomeController.to.goToVerse(
//     //         event["args"]["sura_number"],
//     //         event["args"]["verse_number"],
//     //       );
//     //       break;
//     //     case "onSeekErrorEvent":
//     //       print(event["args"]);
//     //       break;
//     //     case "downloadEvent":
//     //       downloadingItems.update(
//     //           event["args"]["id"],
//     //           (value) => DownloadVM.fromJson({
//     //                 "total": event["args"]["total"],
//     //                 "progress": event["args"]["progress"],
//     //               }),
//     //           ifAbsent: () => DownloadVM.fromJson({
//     //                 "total": event["args"]["total"],
//     //                 "progress": event["args"]["progress"],
//     //               }));
//     //       if (kDebugMode) {
//     //         print(downloadingItems);
//     //       }
//     //       downloadingItems.refresh();
//     //       break;
//     //     case "downloadCompletedEvent":
//     //       isDownloaded.value = true;
//     //       downloadingItems.remove(event["args"]["id"]);
//     //       downloadingItems.refresh();
//     //       break;
//     //     case "connectionErrorEvent":
//     //       Get.snackbar(
//     //         "خطأ",
//     //         "تأكد من إتصالك بالانترنت ثم أعد المحاولة",
//     //         backgroundColor: Colors.red,
//     //         colorText: Colors.white,
//     //       );
//     //       break;
//     //     case "setAudioSourceErrorEvent":
//     //       Get.snackbar(
//     //         "خطأ",
//     //         "تأكد من إتصالك بالانترنت ثم أعد المحاولة",
//     //         backgroundColor: Colors.red,
//     //         colorText: Colors.white,
//     //       );
//     //       break;
//     //   }
//     // });
//     // audioHandler.mediaItem.distinct().listen((mediaItem) {
//     //   if (kDebugMode) {
//     //     print("media item ${mediaItem?.extras}");
//     //   }
//     //   if (mediaItem != null) {
//     //     currentMediaItem.value = mediaItem;
//     //     currentMediaId.value = mediaItem.id;
//     //     BoxesHelper.getLazyDownloadedFile(mediaItem.id).then((value) {
//     //       if (value != null) {
//     //         isDownloaded.value = true;
//     //       } else {
//     //         isDownloaded.value = false;
//     //       }
//     //     });

//     //     if (mediaItem.extras != null) {
//     //       totalDuration.value = Duration(
//     //         milliseconds: (mediaItem.extras!["duration"] ?? 0),
//     //       );
//     //     }
//     //   }
//     // });
//     QuranAudioService.position.distinct().listen((position) {
//       currentPosition.value = position;
//     });

//     handelPlaybackState();
//     super.onInit();
//   }

//   Future<void> loadSuraPlayList(int suraNumber) async {
//     await audioHandler.removeQueueItems();

//     var timing = await ReciterSqlHelper.getSoraTimings(
//         suraNo: suraNumber, reciterId: currentReciter.value?.id);

//     var sura = await QuranDatabaseProvider.getSurahByNumber(suraNumber);
//     var url =
//         '${currentReciter.value?.filesUrl}${getFilesUrlSuffix(currentReciter.value?.id ?? 0, suraNumber)}${suraNumber.toString().padLeft(3, "0")}.mp3';
//     var file = BoxesHelper.getDownloadedFile(url);

//     //current media id
//     currentMediaId.value = url;
//     //check if it downloaded
//     isDownloaded.value = file == null ? false : true;

//     var mediaItems = timing?.map((time) {
//       return MediaItem(
//         // album: "djwfjjm",
//         // artist: "wfkwqejfjwef",
//         id: url,
//         title: sura?.name ?? "",
//         // displayTitle: "djwfjjm",
//         duration: time.endAt == null
//             ? null
//             : Duration(milliseconds: time.endAt!.toInt()),
//         // duration: time.endAt == null
//         //     ? null
//         //     : Duration(milliseconds: time.endAt!.toInt()),
//         // genre: "fdgkdjk",
//         // displaySubtitle: "الاية ${time.verseNumber}",
//         // displayDescription: "dsfjar",
//         extras: {
//           'verse_number': time.verseNumber,
//           'source': file != null ? 'file' : 'url',
//           "url": file != null ? file.filePath : url,
//           "start": Duration(milliseconds: time.startAt.toInt()),
//           "end": time.endAt == null
//               ? null
//               : Duration(milliseconds: time.endAt!.toInt()),
//         },
//       );
//     }).toList();

//     audioHandler.addQueueItems(mediaItems ?? []);
//   }

//   String getFilesUrlSuffix(int reciterId, int suraNumber) {
//     var fileUrl = "";
//     if (reciterId == 3) {
//       switch (suraNumber) {
//         case 2:
//         case 27:
//         case 28:
//         case 58:
//           fileUrl = "replaced/";
//           break;
//         default:
//       }
//     }
//     return fileUrl;
//   }

//   Future<void> downloadFile() async {
//     var suraNumber = currentSura.value;
//     String url =
//         "${currentReciter.value?.filesUrl}${getFilesUrlSuffix(currentReciter.value!.id, suraNumber)}${suraNumber.toString().padLeft(3, "0")}.mp3";
//     var downloadDirectory = await getApplicationDocumentsDirectory();
//     var savePath =
//         "${downloadDirectory.path}/${currentReciter.value!.id}/${suraNumber.toString().padLeft(3, "0")}.mp3";

//     NetworkServices.instance.checkConnectivity(() {
//       Dio().download(url, savePath, onReceiveProgress: (progress, total) {
//         // print("total $total");
//         // print("progress $progress");
//         downloadingItems.update(
//             url,
//             (value) => DownloadVM.fromJson({
//                   "total": total,
//                   "progress": progress,
//                 }),
//             ifAbsent: () => DownloadVM.fromJson({
//                   "total": total,
//                   "progress": progress,
//                 }));
//         if (kDebugMode) {
//           print(downloadingItems);
//         }
//         downloadingItems.refresh();
//       }, deleteOnError: true).catchError((error) {
//         // audioHandler.customEvent.add({
//         //   "type": "downloadErrorEvent",
//         //   "args": {
//         //     "error": error,
//         //     "id": url,
//         //   }
//         // });
//       }).then((value) async {
//         await BoxesHelper.addFileToDownloads(url, savePath);
//         // await setAudioSource(suraNumber);
//         isDownloaded.value = true;
//         downloadingItems.remove(url);
//         downloadingItems.refresh();
//       });
//     }, () {
//       Get.snackbar(
//         "خطأ",
//         "تأكد من إتصالك بالانترنت ثم أعد المحاولة",
//         backgroundColor: Colors.red,
//         colorText: Colors.white,
//       );
//     });
//   }
// }
