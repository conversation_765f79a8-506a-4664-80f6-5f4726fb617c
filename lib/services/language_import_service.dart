import 'dart:convert';
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:sqlbrite/sqlbrite.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/settings.dart';

class LanguageImportService {
  final Dio _dio = Dio();
  final String _baseUrl = 'https://admin.tadars.com';

  /// Downloads a database from the network based on the locale
  /// First checks if the zip file exists locally, if not, downloads it
  /// Then extracts the database from the zip file
  /// Returns the path to the extracted database
  Future<String?> downloadDatabaseForLocale(
    String locale, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
  }) async {
    try {
      // 1. Check if the zip file exists locally
      final fileName = '${locale}_export.zip';
      final tempDir = await getTemporaryDirectory();
      final zipFilePath = '${tempDir.path}/$fileName';
      final zipFile = File(zipFilePath);

      // 2. Download the zip file if it doesn't exist
      if (!await zipFile.exists()) {
        if (onDownloadStart != null) onDownloadStart();
        await _downloadZipFile(locale, onProgress: onDownloadProgress);
        if (onDownloadComplete != null) onDownloadComplete();
      } else {
        if (kDebugMode) {
          print('Zip file already exists: $zipFilePath');
        }
        if (onDownloadComplete != null) onDownloadComplete();
      }

      // 3. Extract the database from the zip file
      if (onExtractionStart != null) onExtractionStart();
      final extractedDir = await _extractZipFile(zipFilePath);
      if (onExtractionComplete != null) onExtractionComplete();

      // 4. Find the database file in the extracted directory
      final dbFile = await _findDatabaseFile(extractedDir);

      return dbFile;
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading database for locale $locale: $e');
      }
      return null;
    }
  }

  /// Downloads a zip file from the network for the given locale
  Future<String> _downloadZipFile(
    String locale, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      final fileName = '${locale}_export.zip';
      final url = '$_baseUrl/$fileName';

      // Get the temporary directory path
      final tempDir = await getTemporaryDirectory();
      final zipFilePath = '${tempDir.path}/$fileName';

      // Download the file
      await _dio.download(
        url,
        zipFilePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
            if (kDebugMode) {
              print('Download progress: $progress%');
            }

            if (onProgress != null) {
              onProgress(received, total);
            }
          }
        },
      );

      return zipFilePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading zip file: $e');
      }
      rethrow;
    }
  }

  /// Finds a database file in the extracted directory
  /// Returns the path to the database file
  Future<String?> _findDatabaseFile(String extractedDir) async {
    try {
      final directory = Directory(extractedDir);
      final files = await directory.list().toList();

      // Look for .db or .sqlite files
      for (final file in files) {
        if (file is File) {
          final extension = path.extension(file.path).toLowerCase();
          if (extension == '.db' || extension == '.sqlite') {
            return file.path;
          }
        }
      }

      // If no database file is found, return null
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding database file: $e');
      }
      return null;
    }
  }

  /// Checks if a database exists for the given locale
  /// Returns the path to the database if it exists, null otherwise
  Future<String?> checkDatabaseExists(String locale) async {
    try {
      // Check if the database file exists in the app's documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final dbPath = '${appDocDir.path}/${locale}_db.sqlite';
      final dbFile = File(dbPath);

      if (await dbFile.exists()) {
        return dbPath;
      }

      // Check if the database file exists in the app's temporary directory
      final tempDir = await getTemporaryDirectory();
      final tempDbPath = '${tempDir.path}/${locale}_db.sqlite';
      final tempDbFile = File(tempDbPath);

      if (await tempDbFile.exists()) {
        return tempDbPath;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if database exists: $e');
      }
      return null;
    }
  }

  /// Gets the database for the given locale
  /// If the database doesn't exist, downloads it from the network
  /// Returns the path to the database
  Future<String?> getDatabaseForLocale(
    String locale, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
  }) async {
    try {
      // 1. Check if the database already exists
      String? dbPath = await checkDatabaseExists(locale);
      if (dbPath != null) {
        if (kDebugMode) {
          print('Database already exists: $dbPath');
        }
        return dbPath;
      }

      // 2. If the database doesn't exist, download it
      dbPath = await downloadDatabaseForLocale(
        locale,
        onDownloadStart: onDownloadStart,
        onDownloadProgress: onDownloadProgress,
        onDownloadComplete: onDownloadComplete,
        onExtractionStart: onExtractionStart,
        onExtractionComplete: onExtractionComplete,
      );

      if (dbPath != null) {
        // 3. Copy the database to a permanent location
        final appDocDir = await getApplicationDocumentsDirectory();
        final permanentDbPath = '${appDocDir.path}/${locale}_db.sqlite';

        // Copy the file
        await File(dbPath).copy(permanentDbPath);

        // Return the permanent path
        return permanentDbPath;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting database for locale $locale: $e');
      }
      return null;
    }
  }

  /// Downloads and processes language files for the given language code
  Future<void> downloadAndProcessLanguageFiles(
    String languageCode, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
    Function(int fileCount)? onProcessingStart,
    Function(String fileName, int current, int total)? onFileProcessed,
    Function? onProcessingComplete,
  }) async {
    try {
      // 1. Download the ZIP file
      if (onDownloadStart != null) onDownloadStart();
      final zipFilePath = await _downloadLanguageZip(
        languageCode,
        onProgress: onDownloadProgress,
      );
      if (onDownloadComplete != null) onDownloadComplete();

      // 2. Extract the ZIP file
      if (onExtractionStart != null) onExtractionStart();
      final extractedDir = await _extractZipFile(zipFilePath);
      if (onExtractionComplete != null) onExtractionComplete();

      // 3. Process the CSV files
      final directory = Directory(extractedDir);
      final files = await directory.list().where((entity) =>
        entity is File && path.extension(entity.path) == '.csv'
      ).toList();

      if (onProcessingStart != null) onProcessingStart(files.length);

      await _processCsvFiles(
        extractedDir,
        onFileProcessed: onFileProcessed,
        totalFiles: files.length,
      );

      if (onProcessingComplete != null) onProcessingComplete();

      // 4. Mark language data as imported
      await Hive.box(Boxes.settings).put(
        SettingsConstants.languageDataImportedKey,
        true
      );

      // 5. Clean up temporary files
      await _cleanupFiles(zipFilePath, extractedDir);

    } catch (e) {
      if (kDebugMode) {
        print('Error in downloadAndProcessLanguageFiles: $e');
      }
      rethrow;
    }
  }

  /// Downloads the language ZIP file for the given language code
  Future<String> _downloadLanguageZip(
    String languageCode, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      final fileName = 'csv_${languageCode}_exports.zip';
      final url = '$_baseUrl/$fileName';

      // Get the temporary directory path
      final tempDir = await getTemporaryDirectory();
      final zipFilePath = '${tempDir.path}/$fileName';

      // Download the file
      await _dio.download(
        url,
        zipFilePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
            if (kDebugMode) {
              print('Download progress: $progress%');
            }

            if (onProgress != null) {
              onProgress(received, total);
            }
          }
        },
      );

      return zipFilePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading language ZIP: $e');
      }
      rethrow;
    }
  }

  /// Extracts the ZIP file to a temporary directory
  Future<String> _extractZipFile(String zipFilePath) async {
    try {
      // Read the zip file
      final bytes = await File(zipFilePath).readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Create a directory for extracted files
      final tempDir = await getTemporaryDirectory();
      final extractDir = '${tempDir.path}/extracted_${DateTime.now().millisecondsSinceEpoch}';
      await Directory(extractDir).create(recursive: true);

      // Extract each file
      for (final file in archive) {
        final filename = file.name;
        if (file.isFile) {
          final data = file.content as List<int>;
          File('$extractDir/$filename')
            ..createSync(recursive: true)
            ..writeAsBytesSync(data);
        } else {
          await Directory('$extractDir/$filename').create(recursive: true);
        }
      }

      return extractDir;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting ZIP file: $e');
      }
      rethrow;
    }
  }

  /// Processes the CSV files and imports them into the database
  Future<void> _processCsvFiles(
    String extractedDir, {
    Function(String fileName, int current, int total)? onFileProcessed,
    int totalFiles = 0,
  }) async {
    try {
      final directory = Directory(extractedDir);
      final files = await directory.list().toList();
      int processedCount = 0;

      for (final file in files) {
        if (file is File && path.extension(file.path) == '.csv') {
          final tableName = path.basenameWithoutExtension(file.path);
          final fileName = path.basename(file.path);

          await _importCsvToTable(file.path, tableName);

          processedCount++;
          if (onFileProcessed != null) {
            onFileProcessed(fileName, processedCount, totalFiles);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error processing CSV files: $e');
      }
      rethrow;
    }
  }

  /// Imports a CSV file into a database table using bulk insertion for better performance
  /// Uses streaming for efficient memory usage with large files
  Future<void> _importCsvToTable(String filePath, String tableName) async {
    try {
      final file = File(filePath);

      // Get database
      final db = await SqlHelper.briteDB;
      final database = db?.database;

      // Open file as a stream for memory-efficient processing of large files
      final Stream<String> lines = file
          .openRead()
          .transform(utf8.decoder)
          .transform(const LineSplitter());

      // Variables to track processing
      List<String>? header;
      final List<Map<String, dynamic>> batchRows = [];
      final int batchSize = 5000; // Adjust based on your data size

      // Prepare batch for bulk insertion
      final batch = database?.batch();

      // Process the file line by line
      await for (final line in lines) {
        if (line.trim().isEmpty) continue;

        // First line is the header
        if (header == null) {
          header = _parseCsvLine(line);

          // Check if table exists, create if not
          await _ensureTableExists(db, tableName, header);
          continue;
        }

        // Process data row
        final values = _parseCsvLine(line);
        if (values.length != header.length) continue;

        final row = <String, dynamic>{};
        for (int j = 0; j < header.length; j++) {
          row[header[j]] = values[j];
        }

        batchRows.add(row);

        // When batch size is reached, insert the batch
        if (batchRows.length >= batchSize) {
          await _executeBatch(batch, db, tableName, batchRows);
          batchRows.clear();
        }
      }

      // Insert any remaining rows
      if (batchRows.isNotEmpty) {
        await _executeBatch(batch, db, tableName, batchRows);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error importing CSV to table $tableName: $e');
      }
      // Continue with other files even if one fails
    }
  }

  /// Helper method to execute a batch of inserts
  Future<void> _executeBatch(
    Batch? batch,
    BriteDatabase? db,
    String tableName,
    List<Map<String, dynamic>> rows
  ) async {
    if (batch != null) {
      // Add all rows to the batch
      for (final row in rows) {
        batch.insert(tableName, row);
      }

      // Execute the batch
      await batch.commit(noResult: true);
      if (kDebugMode) {
        print('Batch executed');
      }
    } else {
      // Fallback to individual inserts if batch is not available
      for (final row in rows) {
        await db?.insert(tableName, row);
      }
    }
  }

  /// Ensures the table exists in the database
  Future<void> _ensureTableExists(BriteDatabase? db, String tableName, List<String> columns) async {
    try {
      // Check if table exists
      final tables = await db?.query('sqlite_master',
        columns: ['name'],
        where: 'type = ? AND name = ?',
        whereArgs: ['table', tableName]
      );

      if (tables != null && tables.isNotEmpty) {
        // Table exists, drop it to replace with new data
        await db?.execute('DROP TABLE IF EXISTS $tableName');
      }

      // Create table
      String createTableSql = 'CREATE TABLE $tableName (';
      for (int i = 0; i < columns.length; i++) {
        createTableSql += '${columns[i]} TEXT';
        if (i < columns.length - 1) {
          createTableSql += ', ';
        }
      }
      createTableSql += ')';

      await db?.execute(createTableSql);
    } catch (e) {
      if (kDebugMode) {
        print('Error ensuring table exists: $e');
      }
      rethrow;
    }
  }

  /// Parses a CSV line into a list of values with optimized handling
  List<String> _parseCsvLine(String line) {
    // Fast path for empty lines
    if (line.isEmpty) {
      return [];
    }

    final List<String> result = [];
    bool inQuotes = false;
    final StringBuffer buffer = StringBuffer();

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        // Handle escaped quotes (two double quotes in a row)
        if (i + 1 < line.length && line[i + 1] == '"') {
          buffer.write('"');
          i++; // Skip the next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char == ',' && !inQuotes) {
        // End of field
        result.add(buffer.toString());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }

    // Add the last value
    result.add(buffer.toString());

    return result;
  }

  /// Cleans up temporary files
  Future<void> _cleanupFiles(String zipFilePath, String extractedDir) async {
    try {
      // Delete the ZIP file
      final zipFile = File(zipFilePath);
      if (await zipFile.exists()) {
        await zipFile.delete();
      }

      // Delete the extracted directory
      final directory = Directory(extractedDir);
      if (await directory.exists()) {
        await directory.delete(recursive: true);
      }
    } catch (e) {
      // Just log cleanup errors, don't throw
      if (kDebugMode) {
        print('Error cleaning up files: $e');
      }
    }
  }

  /// Main public method to download and get a database for a locale
  /// This is the method that should be called from outside this class
  ///
  /// Example usage:
  /// ```dart
  /// final dbPath = await languageImportService.getOrDownloadDatabase(
  ///   'ar',
  ///   onDownloadStart: () => showLoading(),
  ///   onDownloadProgress: (received, total) => updateProgress(received / total),
  ///   onDownloadComplete: () => hideLoading(),
  /// );
  ///
  /// if (dbPath != null) {
  ///   // Use the database
  ///   final db = await openDatabase(dbPath);
  ///   // ...
  /// }
  /// ```
  Future<String?> getOrDownloadDatabase(
    String locale, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
  }) async {
    try {
      // First check if the database already exists
      final dbPath = await getDatabaseForLocale(
        locale,
        onDownloadStart: onDownloadStart,
        onDownloadProgress: onDownloadProgress,
        onDownloadComplete: onDownloadComplete,
        onExtractionStart: onExtractionStart,
        onExtractionComplete: onExtractionComplete,
      );

      // Mark as imported in settings
      if (dbPath != null) {
        await Hive.box(Boxes.settings).put('${locale}_database_imported', true);
      }

      return dbPath;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getOrDownloadDatabase: $e');
      }
      return null;
    }
  }
}
