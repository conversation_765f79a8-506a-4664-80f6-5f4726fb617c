import 'dart:io';
import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:sqlbrite/sqlbrite.dart';

class DownloadTdrsDbService {
  
  final Dio _dio = Dio();
  final String _baseUrl = 'https://admin.tadars.com';



  /// Downloads a database from the network based on the locale
  /// First checks if the zip file exists locally, if not, downloads it
  /// Then extracts the database from the zip file
  /// Returns the path to the extracted database
  Future<String?> downloadDatabaseForLocale(
    String locale, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
  }) async {
    try {
      // 1. Check if the zip file exists locally
      final fileName = '${locale}_export.zip';
      final tempDir = await getTemporaryDirectory();
      final zipFilePath = '${tempDir.path}/$fileName';
      final zipFile = File(zipFilePath);

      // 2. Download the zip file if it doesn't exist
      if (!await zipFile.exists()) {
        if (onDownloadStart != null) onDownloadStart();
        await _downloadZipFile(locale, onProgress: onDownloadProgress);
        if (onDownloadComplete != null) onDownloadComplete();
      } else {
        if (kDebugMode) {
          print('Zip file already exists: $zipFilePath');
        }
        if (onDownloadComplete != null) onDownloadComplete();
      }

      // 3. Extract the database from the zip file
      if (onExtractionStart != null) onExtractionStart();
      final extractedDir = await _extractZipFile(zipFilePath);
      if (onExtractionComplete != null) onExtractionComplete();

      // 4. Find the database file in the extracted directory
      final dbFile = await _findDatabaseFile(extractedDir);

      return dbFile;
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading database for locale $locale: $e');
      }
      return null;
    }
  }

  /// Downloads a zip file from the network for the given locale
  Future<String> _downloadZipFile(
    String locale, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      final fileName = '${locale}_export.zip';
      final url = '$_baseUrl/$fileName';

      // Get the temporary directory path
      final tempDir = await getTemporaryDirectory();
      final zipFilePath = '${tempDir.path}/$fileName';

      // Download the file
      await _dio.download(
        url,
        zipFilePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
            if (kDebugMode) {
              print('Download progress: $progress%');
            }

            if (onProgress != null) {
              onProgress(received, total);
            }
          }
        },
      );

      return zipFilePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading zip file: $e');
      }
      rethrow;
    }
  }

  /// Finds a database file in the extracted directory
  /// Returns the path to the database file
  Future<String?> _findDatabaseFile(String extractedDir) async {
    try {
      final directory = Directory(extractedDir);
      final files = await directory.list().toList();

      // Look for .db or .sqlite files
      for (final file in files) {
        if (file is File) {
          final extension = path.extension(file.path).toLowerCase();
          if (extension == '.db' || extension == '.sqlite') {
            return file.path;
          }
        }
      }

      // If no database file is found, return null
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding database file: $e');
      }
      return null;
    }
  }

  /// Checks if a database exists for the given locale
  /// Returns the path to the database if it exists, null otherwise
  // Future<String?>
  // checkDatabaseExists(String locale) async {
  //   try {
  //     // Check if the database file exists in the app's documents directory
  //     final appDocDir = await getApplicationDocumentsDirectory();
  //     final dbPath = '${appDocDir.path}/${locale}_db.sqlite';
  //     final dbFile = File(dbPath);

  //     if (await dbFile.exists()) {
  //       return dbPath;
  //     }

  //     // Check if the database file exists in the app's temporary directory
  //     final tempDir = await getTemporaryDirectory();
  //     final tempDbPath = '${tempDir.path}/${locale}_db.sqlite';
  //     final tempDbFile = File(tempDbPath);

  //     if (await tempDbFile.exists()) {
  //       return tempDbPath;
  //     }

  //     return null;
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('Error checking if database exists: $e');
  //     }
  //     return null;
  //   }
  // }

  /// Gets the database for the given locale
  /// If the database doesn't exist, downloads it from the network
  /// Returns the path to the database
  Future<String?> getDatabaseForLocale(
    String locale, {
    Function? onDownloadStart,
    Function(int received, int total)? onDownloadProgress,
    Function? onDownloadComplete,
    Function? onExtractionStart,
    Function? onExtractionComplete,
  }) async {
    try {
      // // 1. Check if the database already exists
      // String? dbPath = await checkDatabaseExists(locale);
      // if (dbPath != null) {
      //   if (kDebugMode) {
      //     print('Database already exists: $dbPath');
      //   }
      //   return dbPath;
      // }

      // 2. If the database doesn't exist, download it
      var dbPath = await downloadDatabaseForLocale(
        locale,
        onDownloadStart: onDownloadStart,
        onDownloadProgress: onDownloadProgress,
        onDownloadComplete: onDownloadComplete,
        onExtractionStart: onExtractionStart,
        onExtractionComplete: onExtractionComplete,
      );

      if (dbPath != null) {
        // 3. Copy the database to a permanent location
        var databasesPath = await getDatabasesPath();
        var permanentDbPath = path.join(databasesPath, "tdrs_db.sqlite");

        // Copy the file to the permanent location
        await File(dbPath).copy(permanentDbPath);

        // Return the permanent path
        return permanentDbPath;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting database for locale $locale: $e');
      }
      return null;
    }
  }



  // /// Extracts the ZIP file to a temporary directory
  Future<String> _extractZipFile(String zipFilePath) async {
    try {
      // Read the zip file
      final bytes = await File(zipFilePath).readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Create a directory for extracted files
      final tempDir = await getTemporaryDirectory();
      final extractDir = '${tempDir.path}/extracted_${DateTime.now().millisecondsSinceEpoch}';
      await Directory(extractDir).create(recursive: true);

      // Extract each file
      for (final file in archive) {
        final filename = file.name;
        if (file.isFile) {
          final data = file.content as List<int>;
          File('$extractDir/$filename')
            ..createSync(recursive: true)
            ..writeAsBytesSync(data);
        } else {
          await Directory('$extractDir/$filename').create(recursive: true);
        }
      }

      return extractDir;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting ZIP file: $e');
      }
      rethrow;
    }
  }





}
