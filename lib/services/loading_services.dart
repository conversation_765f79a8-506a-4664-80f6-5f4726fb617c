import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:tadars/utils/constants/custom_colors.dart';

class LoadingService extends GetxService {
  Future<LoadingService> init() async {
    return this;
  }

  static LoadingService to = Get.find<LoadingService>();
  var isLoading = false.obs;
  void show() {
    isLoading.value = true;
  }

  void hide() {
    isLoading.value = false;
  }

  @override
  void onInit() {
    isLoading.listen((value) {
      if (Get.isSnackbarOpen) {
        Get.closeAllSnackbars();
      }
      if (value) {
        Get.dialog(
            AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Padding(
                  padding: const EdgeInsets.only(top: 50, bottom: 50),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LoadingAnimationWidget.threeRotatingDots(
                        color: CustomColors.primaryColor,
                        size: 40,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 15),
                        child: Text(
                          "يرجى الإنتظار ...",
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: CustomColors.primaryColor),
                        ),
                      )
                    ],
                  ),
                )),
            barrierDismissible: false);
      } else {
        Get.back();
      }
    });
    super.onInit();
  }
}
