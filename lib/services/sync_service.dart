import 'dart:async';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/sync.dart';
import 'package:tadars/utils/common_functions.dart';

import '../controllers/home_controller.dart';
import '../helpers/api_helper.dart';
import '../helpers/boxes_helper.dart';
import '../models/hive/quran_book.dart';
import '../models/hive/quran_tafseer.dart';
import '../models/view_models/sections_with_new_data_vm.dart';
import '../utils/constants/boxes.dart';

class SyncService extends GetxService {
  static final SyncService instance = Get.find();
  ApiHelper _apiHelper = ApiHelper(Dio(BaseOptions()));
  SectionsWithNewDataVm lastIds = SectionsWithNewDataVm();
  SectionsWithNewDataVm sectionsWithNewDataResponse = SectionsWithNewDataVm();
  Rx<bool> isSyncing = Rx<bool>(false);
  @override
  void onInit() {
    Dio dio = Dio();
    dio.interceptors.add(RetryInterceptor(
      dio: dio,
      logPrint: print, // specify log function
      retries: 3, // retry count
      retryDelays: const [
        Duration(seconds: 1), // wait 1 sec before first retry
        Duration(seconds: 2), // wait 2 sec before second retry
        Duration(seconds: 3), // wait 3 sec before third retry
      ],
    ));
    _apiHelper = ApiHelper(dio);
    super.onInit();
  }

  void sync() async {
    isSyncing.value = true;
    try {
      Get.log('Syncing...');
      lastIds = await SqlHelper.getLastIds();
      lastIds.quranBooks = BoxesHelper.getLastQuranBooksId();
      lastIds.tafseers = BoxesHelper.getLastTafseersId();
      await syncNewData();
      await syncExsistData();
      CommonFunctions.showSuccessMessage('تمت المزامنة بنجاح'.tr);
      HomeController.to.getWaqafatCounts();
      isSyncing.value = false;
    } on Exception catch (_, e) {
      CommonFunctions.showErrorMessage('حصل خطأ أثناء المزامنة'.tr);
      HomeController.to.getWaqafatCounts();
      print('sync error: $e');
      print(StackTrace.current.toString());
      isSyncing.value = false;
    }
    isSyncing.value = false;
  }

  Future<void> syncNewData() async {
    if (kDebugMode) {
      print('sync new data');
    }
    Stopwatch stopwatch = Stopwatch()..start();
    sectionsWithNewDataResponse =
        await _apiHelper.getSectionsWithNewData(lastIds);
    // await syncUser();
    await syncShare();
    await syncQuranBook();
    await syncTafseer();
    await syncTdbrSource();
    await syncTdbrComparables();
    await syncTdbrConsiders();
    await syncTdbrEloquences();
    await syncTdbrLinks();
    await syncTdbrMedia();
    await syncTdbrPrays();
    await syncTdbrQuestions();
    await syncTdbrRules();
    await syncTdbrSuggesgt();
    await syncTdbrTadaborCategories();
    await syncTdbrTadabors();
    await syncTdbrVerses();
    stopwatch.stop();
    Get.log("${stopwatch.elapsedMilliseconds} ms taken to sync");
  }

  Future<void> syncExsistData() async {
    print('sync exist data');
    int lastSyncId = await SqlHelper.getLastId('sync');
    print(lastSyncId);
    // await syncUser(lastSyncId: lastSyncId);
    await syncShare(lastSyncId: lastSyncId);
    await syncQuranBook(lastSyncId: lastSyncId);
    await syncTafseer(lastSyncId: lastSyncId);
    await syncTdbrSource(lastSyncId: lastSyncId);
    await syncTdbrComparables(lastSyncId: lastSyncId);
    await syncTdbrConsiders(lastSyncId: lastSyncId);
    await syncTdbrEloquences(lastSyncId: lastSyncId);
    await syncTdbrLinks(lastSyncId: lastSyncId);
    await syncTdbrMedia(lastSyncId: lastSyncId);
    await syncTdbrPrays(lastSyncId: lastSyncId);
    await syncTdbrQuestions(lastSyncId: lastSyncId);
    await syncTdbrRules(lastSyncId: lastSyncId);
    await syncTdbrSuggesgt(lastSyncId: lastSyncId);
    await syncTdbrTadaborCategories(lastSyncId: lastSyncId);
    await syncTdbrTadabors(lastSyncId: lastSyncId);
    await syncTdbrVerses(lastSyncId: lastSyncId);
    await syncSync();
  }

  syncSync() async {
    int perPage = 2000;
    int? syncableItemsNumber = sectionsWithNewDataResponse.sync;
    List<Sync> syncsResponse = [];
    var lastId = await SqlHelper.getLastId('sync');

    if (syncableItemsNumber != null) {
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        syncsResponse =
            await _apiHelper.getSync(id: lastId, page: page, perPage: perPage);

        for (var element in syncsResponse) {
          // print(element.toJson());
          if (element.action == 1) {
            continue;
          }
          if (element.action == 0) {
            //delete
            switch (element.table) {
              case "tafser":
                print("delete tafseer");
                BoxesHelper.deleteTafseer(element.rowId ?? 0);
                break;
              case "quran_books":
                print("delete quran_books");
                BoxesHelper.deleteQuranBook(element.rowId ?? 0);
                break;
              case "comments":
                break;
              default:
                print("delete default");
                SqlHelper.deleteTdbr(element.table, element.rowId ?? 0);
            }
          } else if (element.action == 2) {
            // status 1
            switch (element.table) {
              case "tafser":
                print("update tafseer status 1");
                BoxesHelper.updateTafseerStatus(element.rowId ?? 0, 1);
                break;
              case "quran_books":
                print("update quran_books status 1");
                BoxesHelper.updateQuranBookStatus(element.rowId ?? 0, 1);
                break;
              case "comments":
                break;

              default:
                print("update default status 1");
                SqlHelper.updateTdbrStatus(
                    element.table, element.rowId ?? 0, 1);
            }
          } else if (element.action == 3) {
            // status 0
            switch (element.table) {
              case "tafser":
                print("update tafseer status 0");
                BoxesHelper.updateTafseerStatus(element.rowId ?? 0, 0);
                break;
              case "quran_books":
                print("update quran_books status 0");
                BoxesHelper.updateQuranBookStatus(element.rowId ?? 0, 0);
                break;
              case "comments":
                break;
              default:
                print("update default status 0");
                SqlHelper.updateTdbrStatus(
                    element.table, element.rowId ?? 0, 0);
            }
          }
        }
        var dataToInsert = syncsResponse.map((e) => e.toJson()).toList();
        await SqlHelper.insertIntoTable('sync', dataToInsert);
      }
    }
  }

  Future<void> syncLikes({int? lastSyncId}) async {
    Get.log("Strart sync like");
    int perPage = 600;
    int? syncableItemsNumber = sectionsWithNewDataResponse.likes;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('likes');
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getLikes(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('likes', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('likes', dataToInsert);
        }
      }
    }
    Get.log("sync Link done");
  }

  Future<void> syncShare({int? lastSyncId}) async {
    Get.log("Strart sync Share");
    int perPage = 1000;
    int? syncableItemsNumber = sectionsWithNewDataResponse.shares;
    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('shares');
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getShares(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('shares', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('shares', dataToInsert);
        }
      }
    }

    Get.log("sync shares done");
  }

  Future<void> syncQuranBook({int? lastSyncId}) async {
    Get.log("start sync QuranBook");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.quranBooks;

    if (syncableItemsNumber != null) {
      var lastId = BoxesHelper.getLastQuranBooksId();

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        BoxesHelper.putBox<QuranBook>(
            Boxes.quranBooks,
            await _apiHelper.getQuranBooks(
              id: lastId,
              page: page,
              perPage: perPage,
              lastSyncId: lastSyncId,
            ));
      }
    }
    Get.log("sync QuranBook done");
  }

  Future<void> syncTafseer({int? lastSyncId}) async {
    Get.log("start sync Tafseer");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tafseers;

    if (syncableItemsNumber != null) {
      var lastId = BoxesHelper.getLastTafseersId();
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        BoxesHelper.putBox<QuranTafseer>(
            Boxes.quranTafseer,
            await _apiHelper.getTafser(
              id: lastId,
              page: page,
              perPage: perPage,
              lastSyncId: lastSyncId,
            ));
      }
    }
    Get.log("sync Tafseer done");
  }

  Future<void> syncTdbrComparables({int? lastSyncId}) async {
    Get.log("start sync TdbrComparables");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrComparables;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_comparable');
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrComparable(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_comparable', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_comparable', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrComparables done");
  }

  Future<void> syncTdbrConsiders({int? lastSyncId}) async {
    Get.log("start sync TdbrConsiders");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrConsiders;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_consider');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrConsiders(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_consider', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_consider', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrConsiders done");
  }

  Future<void> syncTdbrEloquences({int? lastSyncId}) async {
    Get.log("start sync TdbrEloquences");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrEloquences;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_eloquence');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrEloquence(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_eloquence', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_eloquence', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrEloquences done");
  }

  Future<void> syncTdbrLinks({int? lastSyncId}) async {
    Get.log("start sync TdbrLinks");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrLinks;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_links');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrLinks(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_links', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_links', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrLinks done");
  }

  Future<void> syncTdbrMedia({int? lastSyncId}) async {
    Get.log("start sync TdbrMedia");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrMedia;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_media');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrMedias(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_media', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_media', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrMedia done");
  }

  Future<void> syncTdbrPrays({int? lastSyncId}) async {
    Get.log("start sync TdbrPrays");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrPrays;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_pray');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrPrays(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_pray', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_pray', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrPrays done");
  }

  Future<void> syncTdbrQuestions({int? lastSyncId}) async {
    Get.log("start sync TdbrQuestions");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrQuestions;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_questions');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrQuestions(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_questions', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_questions', dataToInsert);
        }
      }
    }
    Get.log("sync TdbrQuestions done");
  }

  Future<void> syncTdbrRules({int? lastSyncId}) async {
    Get.log("start sync TdbrRules");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrRules;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_rules');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrRules(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_rules', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_rules', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrRules done");
  }

//get sync TdbrSource
  Future<void> syncTdbrSource({int? lastSyncId}) async {
    Get.log("start sync TdbrSource");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrSources;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_sources');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrSources(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_sources', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_sources', dataToInsert);
        }
      }
      Get.log("sync TdbrSource done");
    }
  }

  //get sync TdbrSuggest

  Future<void> syncTdbrSuggesgt({int? lastSyncId}) async {
    Get.log("start sync TdbrSuggest");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrQuestions;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_suggest');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrSuggeste(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_suggest', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_suggest', dataToInsert);
        }
      }
    }

    Get.log("sync TdbrSuggest done");
  }

  Future<void> syncTdbrTadaborCategories({int? lastSyncId}) async {
    Get.log("start sync TdbrTadaborCategories");
    int perPage = 400;
    int? syncableItemsNumber =
        sectionsWithNewDataResponse.tdbrTadaborCategories;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_tadabor_cats');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrTadaborCats(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_tadabor_cats', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_tadabor_cats', dataToInsert);
        }
      }
    }
    Get.log("sync TdbrTadaborCategories done");
  }

  Future<void> syncTdbrTadabors({int? lastSyncId}) async {
    Get.log("start sync TdbrTadabors");
    int perPage = 100;
    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrTadabors;
    print(syncableItemsNumber);

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_tadabor');
      print(lastId);

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrTadabors(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_tadabor', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_tadabor', dataToInsert);
        }
      }
    }
    Get.log("sync TdbrTadabors done");
  }

  Future<void> syncTdbrVerses({int? lastSyncId}) async {
    Get.log("start sync TdbrVerses");
    int perPage = 400;

    int? syncableItemsNumber = sectionsWithNewDataResponse.tdbrVerses;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('tdbr_ayat');

      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getTdbrVerses(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('tdbr_ayat', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('tdbr_ayat', dataToInsert);
        }
      }
    }
    Get.log("sync TdbrVerses done");
  }

  Future<void> syncUser({int? lastSyncId}) async {
    Get.log("start sync User");
    int perPage = 400;
    int? syncableItemsNumber = sectionsWithNewDataResponse.users;

    if (syncableItemsNumber != null) {
      var lastId = await SqlHelper.getLastId('users');
      for (var page = 1; page < (syncableItemsNumber / perPage) + 1; page++) {
        print(page);
        var data = await _apiHelper.getUsers(
          id: lastId,
          page: page,
          perPage: perPage,
          lastSyncId: lastSyncId,
        );
        var dataToInsert = data.map((e) => e.toJson()).toList();
        if (lastSyncId != null) {
          SqlHelper.updateTable('users', dataToInsert);
        } else {
          SqlHelper.insertIntoTable('users', dataToInsert);
        }
      }

      Get.log("sync User done");
    }
  }
}
