import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_core/quran_core.dart' hide QuranPage;
import 'package:sqlbrite/sqlbrite.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/features/intro_verse/intro_verse.dart';

import 'package:tadars/features/user_data_sync/data/helpers/user_sync_db_helper.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';
import 'package:tadars/models/hive/bookmark.dart';
import 'package:tadars/models/hive/downloaded_file.dart';
import 'package:tadars/models/hive/note.dart';
import 'package:tadars/models/hive/quran_tafseer.dart';
import 'package:tadars/models/hive/source_bookmark.dart';
import 'package:tadars/models/hive/tdbr_bookmark.dart';
import 'package:tadars/models/hive/werd.dart';
import 'package:tadars/services/cloud_messaging_service.dart';
import 'package:tadars/services/loading_services.dart';
import 'package:tadars/services/network_service.dart';
import 'package:tadars/services/notification_service.dart';
import 'package:tadars/services/sync_service.dart';
import 'helpers/boxes_helper.dart';
import 'models/hive/quran_page.dart';
import 'models/hive/quran_page_line.dart';
import 'models/hive/quran_reciter.dart';
import 'models/hive/quran_sura.dart';
import 'models/hive/quran_sura_header.dart';
import 'models/hive/quran_timing.dart';
import 'models/hive/quran_verse.dart';
import 'models/hive/quran_book.dart';
import 'models/hive/tafseer.dart';
import 'utils/constants/boxes.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';

void registerAdapters() {
  if (!Hive.isAdapterRegistered(0)) {
    Hive.registerAdapter(QuranPageAdapter());
  }
  if (!Hive.isAdapterRegistered(2)) {
    Hive.registerAdapter(QuranVerseAdapter());
  }
  if (!Hive.isAdapterRegistered(3)) {
    Hive.registerAdapter(QuranPageLineAdapter());
  }
  if (!Hive.isAdapterRegistered(1)) {
    Hive.registerAdapter(QuranSuraHeaderAdapter());
  }
  if (!Hive.isAdapterRegistered(5)) {
    Hive.registerAdapter(QuranSuraAdapter());
  }
  if (!Hive.isAdapterRegistered(6)) {
    Hive.registerAdapter(QuranTimingAdapter());
  }
  if (!Hive.isAdapterRegistered(4)) {
    Hive.registerAdapter(QuranReciterAdapter());
  }
  if (!Hive.isAdapterRegistered(8)) {
    Hive.registerAdapter(NoteAdapter());
  }
  if (!Hive.isAdapterRegistered(7)) {
    Hive.registerAdapter(BookmarkAdapter());
  }
  if (!Hive.isAdapterRegistered(50)) {
    Hive.registerAdapter(TdbrBookmarkAdapter());
  }
  if (!Hive.isAdapterRegistered(51)) {
    Hive.registerAdapter(SourceBookmarkAdapter());
  }
  if (!Hive.isAdapterRegistered(9)) {
    Hive.registerAdapter(DownloadedFileAdapter());
  }
  if (!Hive.isAdapterRegistered(10)) {
    Hive.registerAdapter(TafseerAdapter());
  }
  if (!Hive.isAdapterRegistered(11)) {
    Hive.registerAdapter(QuranTafseerAdapter());
  }
  if (!Hive.isAdapterRegistered(13)) {
    Hive.registerAdapter(QuranBookAdapter());
  }
  if (!Hive.isAdapterRegistered(12)) {
    Hive.registerAdapter(WerdAdapter());
  }
}

Future<void> openBoxes() async {
  await Hive.openBox<QuranPage>(Boxes.quranPages);
  await Hive.openBox<QuranPageLine>(Boxes.quranPagesLines);
  await Hive.openBox<QuranSuraHeader>(Boxes.quranSuarHeaders);
  await Hive.openBox<QuranVerse>(Boxes.quranVerses);
  await Hive.openBox<QuranSura>(Boxes.quranSuar);
  await Hive.openBox<Bookmark>(Boxes.bookmarks);
  await Hive.openBox<TdbrBookmark>(Boxes.tdbrBookmarks);
  await Hive.openBox<SourceBookmark>(Boxes.sourceBookmarks);
  await Hive.openBox<Note>(Boxes.notes);
  await Hive.openBox<QuranReciter>(Boxes.quranReciters);
  await Hive.openBox<DownloadedFile>(Boxes.downloads);
  await Hive.openBox<QuranTafseer>(Boxes.quranTafseer);
  await Hive.openBox<Tafseer>(Boxes.tafseerSa3dy);
  await Hive.openBox<String>(Boxes.searchHistory);
  await Hive.openBox<String>(Boxes.waqafatSearchHistory);
  await Hive.openBox<String>(Boxes.tafseerSearchHistory);
  await Hive.openBox<String>(Boxes.downloadedTafseers);
  await Hive.openBox<String>(Boxes.downloadedBooks);
  await Hive.openBox<QuranBook>(Boxes.quranBooks);
  await Hive.openBox<Werd>(Boxes.werds);
  await Hive.openBox(Boxes.hiveSequence);
  // tdbr
  // await Hive.openBox<Like>(Boxes.likes);
  // await Hive.openBox<Share>(Boxes.shares);
  // await Hive.openBox<User>(Boxes.users);
  // await Hive.openBox<TdbrTadabor>(Boxes.tdbrTadabors);
  // await Hive.openBox<TdbrComparable>(Boxes.tdbrComparables);
  // await Hive.openBox<TdbrConsider>(Boxes.tdbrConsiders);
  // await Hive.openBox<TdbrEloquence>(Boxes.tdbrEloquences);
  // await Hive.openBox<TdbrLink>(Boxes.tdbrLinks);
  // await Hive.openBox<TdbrMedia>(Boxes.tdbrMedia);
  // await Hive.openBox<TdbrPray>(Boxes.tdbrPrays);
  // await Hive.openBox<TdbrQuestion>(Boxes.tdbrQuestions);
  // await Hive.openBox<TdbrRule>(Boxes.tdbrRules);
  // await Hive.openBox<TdbrSource>(Boxes.tdbrSources);
  // await Hive.openBox<TdbrSuggest>(Boxes.tdbrSuggests);
  // await Hive.openBox<TdbrVerse>(Boxes.tdbrVerses);
  // await Hive.openBox<TdbrTadaborCategory>(Boxes.tdbrTadaborCategories);
}

Future<void> initBoxes(String path) async {
  int dbVersion = 1;
  for (var box in Boxes.assetBoxes) {
    await initBox(box, path, dbVersion);
  }

  BoxesHelper.setHiveDbVersion(dbVersion);
}

Future<void> initServices() async {
  var networkService = Get.put(NetworkServices());
  networkService.init();
  Get.put(SyncService());
  Get.put(LoadingService());
  // Get.put(AudioPlayerService());
  
  // Get.put(await UserSyncService().init());

  CloudMessagingService.instance.init();
  await QuranDatabaseProvider.init(2);
  // await QuranProvider.loadPagesFonts();
  await QuranReciterDatabaseProvider.init(
    path: "assets/db/reciters.db",
    version: 4,
  );
  var reciterId = BoxesHelper.getSettingReciterId();
  var loopCount = BoxesHelper.getLoopCount();

  await QuranAudioService.init(
    defaultReciterId: reciterId,
    defualtLoopCount: loopCount,
    defaultSurahNumber: 1,
    audioServiceConfig: AudioServiceConfig(
      androidNotificationChannelId: 'com.tadarose_quran.channel.audio',
      androidNotificationChannelName: 'تدارس القرآن',
      androidNotificationOngoing: true,
      notificationColor: Get.theme.primaryColor,
    ),
  );
  Get.put(await HomeController().init(), permanent: true);

  await NotificationService().initNotification(
    onDidReceiveBackgroundNotificationResponse:
        IntroVerseFunction.onVerseNotficationOpend,
    onDidReceiveNotificationResponse:
        IntroVerseFunction.onVerseNotficationOpend,
  );

  //TODO: remove this when the user is logged in
  // await deleteOldUserData();
  // Future.microtask(() async {
  //   await UserSyncService.instance.importNotesFromHive();
  //   await UserSyncService.instance.importBookmarksFromHive();
  //   await UserSyncService.instance.sync();
  // });

  // Get.put(AudioPlayerService());
}

Future<void> initBox(
  String boxName,
  String documentsPath,
  int dbVersion,
) async {
  var path = "$documentsPath/$boxName.hive";
  var exists = await File(path).exists();

  if ((!exists) || dbVersion > BoxesHelper.getHiveDbVersion()) {
    if (kDebugMode) {
      print("copying $boxName from asset");
    }
    ByteData data = await rootBundle.load("assets/boxes/$boxName.hive");
    List<int> bytes = data.buffer.asUint8List(
      data.offsetInBytes,
      data.lengthInBytes,
    );
    await File(path).writeAsBytes(bytes, flush: true);
  } else {
    if (kDebugMode) {
      print("box $boxName already Exist");
    }
  }
}

Future<void> initTdrsDB() async {
  var databasesPath = await getDatabasesPath();
  var path = join(databasesPath, "tdrs_db.sqlite");
  var exists = await databaseExists(path);

  var dbVersion = 10;

  if ((!exists) || dbVersion > BoxesHelper.getTdrsDbVersion()) {
    // Should happen only the first time you launch your application
    if (kDebugMode) {
      print("Creating  new tdrs_db copy from asset");
    }

    // Make sure the parent directory exists
    try {
      await Directory(dirname(path)).create(recursive: true);
    } catch (_) {}

    // Copy from asset

    ByteData data = await rootBundle.load(join("assets/db", "tdrs_db.sqlite"));
    List<int> bytes = data.buffer.asUint8List(
      data.offsetInBytes,
      data.lengthInBytes,
    );

    //Delete db if it exists
    if (File(path).existsSync()) {
      File(path).deleteSync();
    }

    // Write and flush the bytes written
    await File(path).writeAsBytes(bytes, flush: true);

    BoxesHelper.setTdrsDbVersion(dbVersion);
  } else {
    // if (kDebugMode) {
    //   print("database already Exist");
    // }
  }
}

Future<void> initReciterDB() async {
  var databasesPath = await getDatabasesPath();
  var path = join(databasesPath, "reciters.sqlite");
  var exists = await databaseExists(path);

  var dbVersion = 3;

  if ((!exists) || dbVersion > BoxesHelper.getReciterDbVersion()) {
    // Should happen only the first time you launch your application
    if (kDebugMode) {
      print("Creating new copy of reciters db from asset");
    }

    // Make sure the parent directory exists
    try {
      await Directory(dirname(path)).create(recursive: true);
    } catch (_) {}

    // Copy from asset

    ByteData data = await rootBundle.load(join("assets/db", "reciters.sqlite"));
    List<int> bytes = data.buffer.asUint8List(
      data.offsetInBytes,
      data.lengthInBytes,
    );

    //Delete db if it exists
    if (File(path).existsSync()) {
      File(path).deleteSync();
    }

    // Write and flush the bytes written
    await File(path).writeAsBytes(bytes, flush: true);

    BoxesHelper.setReciterDbVersion(dbVersion);
  } else {
    if (kDebugMode) {
      print("database reciters already Exist");
    }
  }



}

Future<void> deleteOldUserData() async {
  if (Hive.box(
        Boxes.settings,
      ).get("deleted_old_sql_user_data_v3", defaultValue: false) ==
      true) {
    Get.log("old user data already deleted");
    return;
  }

  if (Hive.box(Boxes.settings).get("user_token") != null) {
    var notes = BoxesHelper.getAllNotes();
    var bookmarks = BoxesHelper.getAllBookmarks();
    Get.log("notes length: ${notes.length}");
    Get.log("bookmarks length: ${bookmarks.length}");

    if (notes.isEmpty && bookmarks.isEmpty) {
      await UserSyncDBHelper.emptyTables();
      UserSyncService.instance.bookmarks.clear();

      UserSyncService.instance.notes.clear();
    }
  }
  Hive.box(Boxes.settings).put("deleted_old_sql_user_data_v3", true);
  Get.log("old user data deleted");
}
