import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/services/loading_services.dart';
import 'package:tadars/utils/common_functions.dart';

class AddTdbrController extends GetxController {
  static AddTdbrController to = Get.find<AddTdbrController>();
  Rx<List<Map<String, dynamic>>> ayats = Rx<List<Map<String, dynamic>>>([]);
  Rx<List<Map<String, dynamic>>> links = Rx<List<Map<String, dynamic>>>([]);

  final ApiHelper _apiHelper = ApiHelper(Dio(BaseOptions(headers: {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "lang": "ar"
  })));

  @override
  void onClose() {
    ayats.value.clear();
    ayats.refresh();
    super.onClose();
  }

  void addTdbr(Map<String, dynamic> data) async {
    try {
      LoadingService.to.show();
      var response = await _apiHelper.addTdbr({"data": jsonEncode(data)});
      LoadingService.to.hide();
      if (response.done) {
        CommonFunctions.showSuccessMessage(response.msg);
      } else {
        CommonFunctions.showErrorMessage(response.msg);
      }
    } catch (err) {
      LoadingService.to.hide();

      CommonFunctions.handleError(err);
    }
  }
}
