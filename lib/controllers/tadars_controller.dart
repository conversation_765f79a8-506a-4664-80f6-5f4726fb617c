import 'dart:async';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tadars/helpers/api_helper.dart';

import '../helpers/boxes_helper.dart';
import '../services/loading_services.dart';
import '../services/network_service.dart';
import '../utils/common_functions.dart';

class TadarsController extends GetxController {
  // static instance
  static final TadarsController instance = Get.find();
  final ApiHelper _apiHelper = ApiHelper(Dio(BaseOptions()));

  

  
  Future<void> reportTdbr(int id, String type, String reason) async {
    await NetworkServices.instance.checkConnectivity(() async {
      LoadingService.to.show();
      try {
        var response = await _apiHelper.denouncementItem({
          "id": id,
          "type": type,
          "denouncement": reason,
          "uid": BoxesHelper.getUid(),
        });
        LoadingService.to.hide();
        Get.back();
        CommonFunctions.showSuccessMessage('تم إضافة البلاغ بنجاح');
      } catch (e) {
        LoadingService.to.hide();
        Get.back();
        CommonFunctions.handleError(e);
      }
    });
  }
}
