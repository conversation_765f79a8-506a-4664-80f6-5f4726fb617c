import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tadars/enums/tdbr_type.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';

class WaqafatSearchController extends GetxController {
  WaqafatSearchController();
  Rx<String> searchKey = "".obs;
  Rx<bool> loading = false.obs;
  Rx<Map<TdbrType, dynamic>?> tdbrs = Rx<Map<TdbrType, dynamic>?>(null);
  Future<void> search(String key) async {
    loading.value = true;
    tdbrs.value = await SqlHelper.searchInTdbr(key);
    loading.value = false;
  }

  ApiHelper apiTest = ApiHelper(Dio());
}
