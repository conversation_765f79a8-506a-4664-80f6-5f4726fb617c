import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tadars/models/hive/quran_book.dart';
import 'package:tadars/models/user_credential.dart';
import 'package:tadars/models/view_models/toggle_item.dart';
import 'package:tadars/services/loading_services.dart';
import 'package:tadars/utils/common.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/extensions.dart';

import '../helpers/boxes_helper.dart';
import '../models/view_models/download_vm.dart';
import '../services/network_service.dart';
import '../utils/constants/boxes.dart';
import '../utils/constants/configs.dart';
import '../utils/constants/custom_colors.dart';

class SettingController extends GetxController {
  // instance
  static SettingController get instance => Get.find();
  // downloadingItems
  var downloadingItems = <int, DownloadVM>{}.obs;
  // quranBook
  var quranBook = Rx<QuranBook>(Common.madinaQuranBook);

  @override
  void onInit() {
    BoxesHelper.getListenableSettingsQuranBookId().addListener(() {
      var bookId = BoxesHelper.getSettingQuranBookId();
      quranBook.value =
          BoxesHelper.getQuranBookById(bookId) ?? Common.madinaQuranBook;
      
    });
    super.onInit();
  }

  //login

  void login(Map<String, dynamic> data) {
    NetworkServices.instance.checkConnectivity(() async {
      LoadingService.to.show();
      try {
        var credential = await loginApi(data);
        LoadingService.to.hide();

        if (credential is List) {
          CommonFunctions.showErrorMessage(credential[0]);
        } else {
          Hive.box(Boxes.settings).put("user_token", credential.token);
          Hive.box(Boxes.settings).put("uid", credential.id);

          Get.back();
          CommonFunctions.showSuccessMessage("تم تسجيل الدخول بنجاح");
        }
      } catch (e) {
        LoadingService.to.hide();
        CommonFunctions.handleError(e);
      }
    });
  }

  Future<dynamic> loginApi(data) async {
    var dio = Dio(BaseOptions(baseUrl: Configs.apiUrl));

    final result = await dio.fetch(
        Options(method: 'POST').compose(dio.options, 'login', data: data));

    if (result.data! is List) {
      return result.data!;
    }
    final value = UserCredential.fromJson(result.data! as Map<String, dynamic>);
    return value;
  }

  //register new user
  void newUser(Map<String, dynamic> data) {
    NetworkServices.instance.checkConnectivity(() async {
      LoadingService.to.show();
      try {
        var credential = await newUserApi(data);
        LoadingService.to.hide();
        if (credential.runtimeType == UserCredential) {
          Hive.box(Boxes.settings).put("user_token", credential.token);
          // print("-------------");
          // print(credential.id);
          Hive.box(Boxes.settings).put("uid", credential.id);
          Get.back();
          CommonFunctions.showSuccessMessage("تم إنشاء الدخول بنجاح");
        }

        if (credential.entries.elementAt(0).value is List) {
          CommonFunctions.showErrorMessage(
              credential.entries.elementAt(0).value.first);
        }
      } catch (e) {
        LoadingService.to.hide();
        CommonFunctions.handleError(e);
      }
    });
  }

  Future<dynamic> newUserApi(data) async {
    var dio = Dio(BaseOptions(baseUrl: Configs.apiUrl));

    final result = await dio.fetch(
        Options(method: 'POST').compose(dio.options, 'newUser', data: data));

    if (result.data!.entries.elementAt(0).value is List) {
      return result.data!;
    }

    final value = UserCredential.fromJson(result.data! as Map<String, dynamic>);

    return value;
  }

  //forget password
  void forgetPassword(Map<String, dynamic> data) {
    NetworkServices.instance.checkConnectivity(() async {
      LoadingService.to.show();
      try {
        var response = await forgetPasswordApi(data);
        LoadingService.to.hide();
        // if (credential.runtimeType == UserCredential) {

        Get.back();
        if (response ==
            "لا يمكننا العثور على مستخدم لديه عنوان البريد الإلكتروني هذا.") {
          CommonFunctions.showErrorMessage(response);
        } else {
          CommonFunctions.showSuccessMessage(response);
          // }
        }

        // if (credential.entries.elementAt(0).value is List) {
        //   CommonFunctions.showErrorMessage(
        //       credential.entries.elementAt(0).value.first);
        // }
      } catch (e) {
        LoadingService.to.hide();
        CommonFunctions.handleError(e);
      }
    });
  }

  Future<dynamic> forgetPasswordApi(data) async {
    var dio = Dio(BaseOptions(baseUrl: Configs.apiUrl));

    final result = await dio.fetch(Options(method: 'POST')
        .compose(dio.options, 'forgetPassword', data: data));

    return result.data!;
  }

  // download quran book
  void downloadQuranBook(int id, String path) {
    NetworkServices.instance.checkConnectivity(() async {
      // url
      var url = Configs.getQuranBooksDownloadUrl(path);
      // save path
      var downloadDirectory = await getApplicationDocumentsDirectory();
      // file path
      var filePath = "${downloadDirectory.path}/$id.zip";
      downloadingItems[id] = DownloadVM(
        progress: 0,
        total: 100,
      );
      downloadingItems.refresh();
      Dio().download(
        url,
        filePath,
        onReceiveProgress: (progress, total) {
          // if (kDebugMode) {
          //   print("$progress $total");
          // }
          downloadingItems[id] = DownloadVM(
            progress: progress.toDouble(),
            total: total.toDouble(),
          );
          downloadingItems.refresh();
        },
        deleteOnError: true,
      ).then((value) async {
        // if (kDebugMode) {
        //   print("download complete");
        // }
        downloadingItems.remove(id);
        downloadingItems.refresh();
        var documentDirectory = await getApplicationDocumentsDirectory();
        // unzip
        extractFileToDisk(
            filePath, "${documentDirectory.path}/books/$id");
        BoxesHelper.updateQuranBookIsDownloaded(id, true);
      });
    });
  }

  // delete quran book
  Future<void> deleteQuranBook(int id) async {
    await Get.defaultDialog(
      title: 'حذف الكتاب'.tr,
      middleText: 'هل أنت متأكد من حذف هذا الكتاب؟'.tr,
      textConfirm: 'نعم'.tr,
      textCancel: 'لا'.tr,
      confirmTextColor: Colors.white,
      onConfirm: () async {
        BoxesHelper.updateQuranBookIsDownloaded(id, false);
        // delete directory
        // documnet directory
        var documentDirectory = await getApplicationDocumentsDirectory();
        var dir = Directory("${documentDirectory.path}/books/$id");
        if (dir.existsSync()) {
          dir.deleteSync(recursive: true);
        }
        Get.back();
      },
    );
  }

  void showBooksDialog() {
    var dataKey = GlobalKey();

    var madinaQuranBook = Common.madinaQuranBook;
    Get.dialog(
      ValueListenableBuilder<Box<QuranBook>>(
          valueListenable: BoxesHelper.getListenableQuranBooks(),
          builder: (context, box, child) {
            var books = BoxesHelper.getDownloadedBooks();
            var bookId = BoxesHelper.getSettingQuranBookId();
            books.sort((a, b) => a.order.compareTo(b.order));
            return FractionallySizedBox(
              widthFactor: 0.9,
              heightFactor: 0.8,
              child: Material(
                borderRadius: BorderRadius.circular(8),
                child: Column(
                  children: [
                    Container(
                      height: 50,
                      padding: const EdgeInsets.all(8),
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                        color: CustomColors.primaryColor.withOpacity(0.3),
                      ),
                      child: NavigationToolbar(
                        middle: Text(
                          "اختر الكتاب",
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: Get.theme.textTheme.titleLarge?.fontSize,
                            color: CustomColors.primaryColor,
                          ),
                        ),
                        leading: IconButton(
                          icon: Icon(
                            Icons.close,
                            color: CustomColors.primaryColor,
                          ),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                        trailing: IconButton(
                          icon: Icon(
                            Icons.add,
                            color: CustomColors.primaryColor,
                          ),
                          onPressed: () {
                            Get.toNamed(Routes.booksPage);
                          },
                        ),
                      ),
                    ),
                    Expanded(
                        child: Scrollbar(
                      thumbVisibility: true,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            ListTile(
                              key:
                                  madinaQuranBook.id == bookId ? dataKey : null,
                              onTap: () {
                                Get.back();
                                BoxesHelper.setSettingQuranBookId(
                                    madinaQuranBook.id);
                              },
                              title: Text(
                                madinaQuranBook.title,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: Get.textTheme.bodyLarge?.fontSize,
                                ),
                              ),
                              subtitle: Text(
                                "عدد الصفحات ${madinaQuranBook.pagesCount.toArabicNumber()}",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              trailing: madinaQuranBook.id == bookId
                                  ? Icon(
                                      Icons.check,
                                      color: CustomColors.primaryColor,
                                    )
                                  : null,
                            ),
                            for (var t in books)
                              Column(
                                key: t.id == bookId ? dataKey : null,
                                children: [
                                  const Divider(
                                    height: 1,
                                  ),
                                  ListTile(
                                    onTap: () {
                                      Get.back();
                                      BoxesHelper.setSettingQuranBookId(t.id);
                                    },
                                    title: Text(
                                      t.title,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize:
                                            Get.textTheme.bodyLarge?.fontSize,
                                      ),
                                    ),
                                    subtitle: Text(
                                      "عدد الصفحات ${t.pagesCount.toArabicNumber()}",
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    trailing: t.id == bookId
                                        ? Icon(
                                            Icons.check,
                                            color:
                                                Get.theme.colorScheme.secondary,
                                          )
                                        : null,
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    )),
                  ],
                ),
              ),
            );
          }),
    );

    SchedulerBinding.instance.addPostFrameCallback((_) {
      var cntx = dataKey.currentContext;

      if (cntx != null) {
        Scrollable.ensureVisible(cntx);
      }
    });
  }

  // show add werd dialog
  // void showAddWerdDialog() {
  //   Get.dialog(
  //     const AddWerdDialog(),
  //     barrierDismissible: false,
  //   );
  // }
}

// class AddWerdDialog extends StatelessWidget {
//   const AddWerdDialog({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     // form key
//     final formKey = GlobalKey<FormState>();
//     return TadarsDialog(
//       title: 'إضافة ورد جديد',
//       leading: IconButton(
//         icon: Icon(
//           Icons.close,
//           color: CustomColors.primaryColor,
//         ),
//         onPressed: () {
//           Get.back();
//         },
//       ),
//       content: Container(),
//     );
//   }
// }

class FilledTextFormField extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final String? hintText;
  final String? labelText;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool? obscureText;
  final int? maxLines;
  final TextAlign? textAlign;
  final String? Function(String?)? validator;
  final void Function(String?)? onSaved;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final String? initialValue;
  const FilledTextFormField({
    Key? key,
    this.margin,
    this.hintText,
    this.labelText,
    this.controller,
    this.keyboardType,
    this.textInputAction,
    this.obscureText,
    this.maxLines,
    this.textAlign,
    this.validator,
    this.onSaved,
    this.maxLength,
    this.inputFormatters,
    this.onChanged,
    this.initialValue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (labelText != null)
            Padding(
              padding: const EdgeInsetsDirectional.only(
                start: 8.0,
                bottom: 8.0,
              ),
              child: Text(
                labelText!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          TextFormField(
            controller: controller,
            maxLength: maxLength,
            onChanged: onChanged,
            initialValue: initialValue,
            inputFormatters: inputFormatters,
            decoration: InputDecoration(
              hintText: hintText,
              enabled: true,
              hintStyle: const TextStyle(
                fontSize: 14,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              isDense: true,
              fillColor: CustomColors.primaryColor.withOpacity(0.15),
            ),
            validator: validator,
            onSaved: onSaved,
            keyboardType: keyboardType,
            textInputAction: textInputAction ?? TextInputAction.next,
            textAlign: textAlign ?? TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

class ToggleFormField<T> extends StatelessWidget {
  final String? labelText;
  final List<ToggleItem<T>> items;
  final void Function(T)? onChanged;
  final void Function(T?)? onSave;
  final EdgeInsetsGeometry? margin;
  final T? initialValue;
  const ToggleFormField({
    Key? key,
    required this.items,
    this.onChanged,
    this.onSave,
    this.initialValue,
    this.margin,
    this.labelText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (labelText != null)
            Padding(
              padding: const EdgeInsetsDirectional.only(start: 8.0, bottom: 4),
              child: Text(
                labelText!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          FormField<T>(
            initialValue: initialValue,
            onSaved: onSave,
            builder: (field) {
              return Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: CustomColors.primaryColor.withOpacity(0.15),
                  borderRadius: const BorderRadius.all(
                    Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: items
                      .map(
                        (e) => Expanded(
                          child: InkWell(
                            onTap: () {
                              field.didChange(e.value);
                              onChanged?.call(e.value);
                            },
                            borderRadius: const BorderRadius.all(
                              Radius.circular(8),
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8),
                                ),
                                color: field.value == e.value
                                    ? Colors.white
                                    : null,
                              ),
                              child: Text(
                                e.label,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: field.value == e.value
                                      ? Colors.black
                                      : const Color(0xFF535353),
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

// class TadarsDialog extends StatelessWidget {
//   final String title;
//   final Widget? leading;
//   final Widget? trailing;
//   final Widget content;
//   const TadarsDialog(
//       {Key? key,
//       required this.title,
//       this.leading,
//       this.trailing,
//       required this.content})
//       : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: SingleChildScrollView(
//         child: Material(
//           type: MaterialType.card,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(10),
//           ),
//           child: ConstrainedBox(
//             constraints: BoxConstraints(
//               minWidth: 250,
//               maxWidth: 300,
//               minHeight: 200,
//             ),
//             child: SingleChildScrollView(
//               physics: ClampingScrollPhysics(),
//               child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.stretch,
//                   children: [
//                     Container(
//                       height: 50,
//                       decoration: BoxDecoration(
//                         color: CustomColors.primaryColor.withOpacity(0.3),
//                         borderRadius: const BorderRadius.vertical(
//                           top: Radius.circular(10),
//                         ),
//                       ),
//                       width: double.maxFinite,
//                       child: NavigationToolbar(
//                         leading: leading,
//                         middle: Text(
//                           title,
//                           textAlign: TextAlign.center,
//                           style: TextStyle(
//                             fontSize: 18,
//                             fontWeight: FontWeight.bold,
//                             color: CustomColors.primaryColor,
//                           ),
//                         ),
//                       ),
//                     ),
//                     SingleChildScrollView(
//                       child: content,
//                     ),
//                   ]),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

class NumericalRangeFormatter extends TextInputFormatter {
  final double min;
  final double max;

  NumericalRangeFormatter({required this.min, required this.max});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text == '') {
      return newValue;
    } else if (int.parse(newValue.text) < min) {
      return const TextEditingValue().copyWith(text: min.toStringAsFixed(2));
    } else {
      return int.parse(newValue.text) > max ? oldValue : newValue;
    }
  }
}
