import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_sura.dart';
import 'package:tadars/models/hive/quran_verse.dart';

class SearchController extends GetxController {
  SearchController();
  Rx<List<QuranSura>?> suars = Rx<List<QuranSura>?>(null);
  Rx<List<QuranVerse>?> verses = Rx<List<QuranVerse>?>(null);
  Rx<String> searchKey = "".obs;
  void search(String key) {
    suars.value = BoxesHelper.searchInSuar(key);
    verses.value = BoxesHelper.searchInVerses(key);
    suars.refresh();
    verses.refresh();
  }

  ApiHelper apiTest = ApiHelper(Dio());
}
