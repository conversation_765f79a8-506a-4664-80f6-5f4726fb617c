import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';

import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

import '../helpers/boxes_helper.dart';
import '../models/hive/werd.dart';
import '../utils/constants/boxes.dart';

class WerdController extends GetxController {
  static WerdController get to => Get.find();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Werd newWerd;

  WerdController();

  @override
  void onInit() async {
    await configureLocalTimeZone();
    await initNotification();

    super.onInit();
  }

  Future<void> manageWerd(Werd werd) async {
    // print(werd.id);
    werd.id ??= BoxesHelper.getAutoIncrementId(Boxes.werds);
    // if (kDebugMode) {
    //   print(werd.id);
    // }
    if (werd.withAlarm) {
      WerdController.to.showNotification(
          werd.id ?? 0,
          werd.name,
          "",
          tz.TZDateTime.from(
              werd.alarmTime ?? DateTime.now().add(const Duration(minutes: 5)),
              tz.local));
    } else {
      disposeNotification(werd.id ?? 0);
    }
    await BoxesHelper.putWerd(werd);
  }

  deleteWrd(int? werdId) {
    BoxesHelper.deleteWerd(werdId);
  }

  Future<void> configureLocalTimeZone() async {
    tz.initializeTimeZones();
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  Future<void> initNotification() async {
    // Android initialization
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // ios initialization
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS);
    // the initialization settings are initialized after they are setted
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> showNotification(
      int id, String title, String body, tz.TZDateTime scheduledDate) async {
    await flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      scheduledDate, //schedule the notification to show after 2 seconds.
      NotificationDetails(
        // Android details
        android: AndroidNotificationDetails(
          'werd_$id',
          'werd channel',
          channelDescription: "channel to show werds time notifications",
          importance: Importance.max,
          priority: Priority.max,
        ),
        // iOS details
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      matchDateTimeComponents: DateTimeComponents.time,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,

      // androidAllowWhileIdle: true,
      // uiLocalNotificationDateInterpretation:
      //     UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  Future<void> disposeNotification(int id) async {
    await flutterLocalNotificationsPlugin.cancel(id);
  }
}
