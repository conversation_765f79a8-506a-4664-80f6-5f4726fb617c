import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:numberpicker/numberpicker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:quran_core/quran_core.dart';
import 'package:tadars/features/user_data_sync/data/enums/bookmark_type.dart';
import 'package:tadars/features/user_data_sync/data/services/user_sync_service.dart';
import 'package:tadars/features/user_data_sync/models/bookmark.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/helpers/sql_helper.dart';
import 'package:tadars/models/hive/quran_verse.dart';
import 'package:tadars/utils/common_styles.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/common_constants.dart';

import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/utils/enums.dart';
import 'package:tadars/utils/extensions.dart' as ex;
import 'package:tadars/utils/quran_ui_icons.dart';
import 'package:tadars/view/components/dialogs/verse_options_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../view/components/dialogs/tafseer_dialog.dart';
import 'package:advanced_in_app_review/advanced_in_app_review.dart';

class HomeController extends GetxController {
  static HomeController get to => Get.find<HomeController>();
  double pageHeight = 1792;
  double pageWidth = 1120;
  var viewMode = ViewMode.quran.obs;
  var selectedVerseNumber = 0.obs;
  var selectedSuraNumber = 0.obs;
  var currentSuraNumber = 0.obs;
  var currentPageNumber = 0.obs;
  var pageController = PageController(initialPage: 0).obs;
  var bookPageController = PageController(initialPage: 0).obs;
  Rx<Color?> pageColor = Rx<Color?>(const Color(0xffffffff));
  Rx<Color?> pageTextColor = Rx<Color?>(const Color(0xFF000000));
  Rx<bool> isCustomColor = Rx<bool>(false);
  Rx<Color?> customTextColor = Rx<Color?>(null);
  Rx<Color?> customBackgroundColor = Rx<Color?>(null);
  var pageFontSize = 16.0.obs;
  var showOverlay = false.obs;
  var firstSuraNumberInPage = 0.obs;
  var firstVerseNumberInPage = 0.obs;
  var lastSuraNumberInPage = 0.obs;
  var lastVerseNumberInPage = 0.obs;
  var currentPage = 0.obs;
  final dataKey = GlobalKey().obs;
  var canChangePage = true.obs;
  Rx<String?> documentDirectory = Rx<String?>(null);
  Rx<Map> waqafatCounts = Rx<Map>({});

  var showAutoScrollPagesTimer = AutoScrollPagesStatus.stop.obs;

  Rx<List<Surah>> surahs = Rx<List<Surah>>([]);

//quran page controller
  late QuranPageController quranPageController;

  //previous visited page
  var prevVisitedPage = Rx<int?>(null);

  Future<void> getWaqafatCounts() async {
    waqafatCounts.value = await SqlHelper.getAllTdbrsCount();
  }

  //tdbr sources for Add new tdb
  Future<List<Map<String, dynamic>>>? futuretdbrSorces;

  void toggleShowOverlay() {
    showOverlay.value = !showOverlay.value;
  }

  void goToVerse(int suraNumber, int verseNumber) async {
    selectedSuraNumber.value = suraNumber;
    selectedVerseNumber.value = verseNumber;
    var verse =
        await QuranDatabaseProvider.getVerseByNumber(suraNumber, verseNumber);

    quranPageController.selectVerse(verse);
  }

  // go to next verse
  void goToNextVerse() {
    // if (kDebugMode) {
    //   print("goToNextVerse");
    // }
    if (selectedVerseNumber.value < lastVerseNumberInPage.value) {
      selectedVerseNumber.value++;
    } else {
      if (selectedSuraNumber.value < lastSuraNumberInPage.value) {
        selectedSuraNumber.value++;
        selectedVerseNumber.value = 1;
      }
    }
    goToVerse(selectedSuraNumber.value, selectedVerseNumber.value);
  }

  // go to previous verse
  void goToPreviousVerse() {
    // if (kDebugMode) {
    //   print("goToPreviousVerse");
    // }
    if (selectedVerseNumber.value > firstVerseNumberInPage.value) {
      selectedVerseNumber.value--;
    } else {
      if (selectedSuraNumber.value > firstSuraNumberInPage.value) {
        selectedSuraNumber.value--;
        selectedVerseNumber.value =
            BoxesHelper.getPageById(currentPage.value--)?.lastVerseNumber ?? 0;
      }
    }
    goToVerse(selectedSuraNumber.value, selectedVerseNumber.value);
  }

  // show tafseer dialog
  void showTafseerDialog(int suraNumber, int verseNumber) {
    // if (kDebugMode) {
    //   print("showTafseerDialog");
    // }
    Get.dialog(
      TafseerDialog(
        suraNumber: suraNumber,
        verseNumber: verseNumber,
      ),
      barrierDismissible: false,
      transitionCurve: Curves.easeIn,
    );
    // Get.defaultDialog(
    //     radius: 9,
    //     title: "التفسير".tr,
    //     content: Container(
    //       constraints: BoxConstraints(maxHeight: Get.height * 0.7),
    //       child: SingleChildScrollView(
    //         child: Text(
    //           Hive.box<QuranVerse>(Boxes.quranVerses)
    //                   .get(
    //                     suraNumber.toString().padLeft(3, "0") +
    //                         "_" +
    //                         verseNumber.toString().padLeft(3, "0"),
    //                   )
    //                   ?.translation ??
    //               "",
    //           textAlign: TextAlign.left,
    //         ),
    //       ),
    //     ));
  }

  void goToPage(int pageNumber) {
    // pageController.value.animateToPage(pageNumber,
    //     duration: Duration(milliseconds: 1000), curve: Curves.linear);
    quranPageController.jumpToPage(pageNumber);
  }

  void saveLastPageAndSura(int pageNumber, int suraNumber) {
    Hive.box(Boxes.settings).put(SettingsConstants.lastPageKey, pageNumber);
    currentSuraNumber.value = suraNumber;
  }

  void saveLastPage(int pageNumber) {
    Hive.box(Boxes.settings).put(SettingsConstants.lastPageKey, pageNumber);
  }

  void changeThemeMode(bool darkMode) {
    Get.changeThemeMode(darkMode ? ThemeMode.dark : ThemeMode.light);
    Hive.box(Boxes.settings)
        .put(SettingsConstants.appDarkModeEnabledKey, darkMode);
    // Hive.box(Boxes.settings)
    //     .put(SettingsConstants.isCustomColorKey, darkMode ? false : true);
    // isCustomColor.value = darkMode ? false : true;
  }

  void changeWakelockEnableStatus(bool status) {
    WakelockPlus.toggle(enable: status);
    Hive.box(Boxes.settings).put(SettingsConstants.wakelockEnableKey, status);
  }

  void changeClockVisibility(bool status) {
    if (status) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
          overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
      SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    }
    Hive.box(Boxes.settings).put(SettingsConstants.clockVisibilityKey, status);
  }

  void changePageColor(Color color,
      {Color? textColor, bool customColor = false}) {
    Hive.box(Boxes.settings).put(SettingsConstants.pageColorKey, color.value);
    pageColor.value = color;
    Hive.box(Boxes.settings)
        .put(SettingsConstants.isCustomColorKey, customColor);
    isCustomColor.value = customColor;
  }

  void setCustomColor(Color backgroundColor, Color textColor) {
    Hive.box(Boxes.settings)
        .put(SettingsConstants.backColorKey, backgroundColor.value);
    customBackgroundColor.value = backgroundColor;
    Hive.box(Boxes.settings)
        .put(SettingsConstants.pageTextColorKey, textColor.value);
    customTextColor.value = textColor;
    Hive.box(Boxes.settings).put(SettingsConstants.isCustomColorKey, true);
    isCustomColor.value = true;
  }

  void changeFontSize(double size) {
    Hive.box(Boxes.settings).put(SettingsConstants.fontSizeKey, size);
    pageFontSize.value = size;
  }

  void changeQuranFontScale(double size) {
    Hive.box(Boxes.settings).put(SettingsConstants.quranFontScaleKey, size);
    quranPageController.scale = size;
  }

  void changeAppLocale(String code) {
    Hive.box(Boxes.settings).put(SettingsConstants.appLocaleCodeKey, code);
    Get.updateLocale(
      Locale(code),
    );
  }

  int getLastPage() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.lastPageKey, defaultValue: 1);
  }
  double getQuranFontScale() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.quranFontScaleKey, defaultValue: 1.0);
  }

  bool isAppDarkModeEnabled() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.appDarkModeEnabledKey, defaultValue: false);
  }

  bool isWAkelockEnabled() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.wakelockEnableKey, defaultValue: true);
  }

  bool isClockVisible() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.clockVisibilityKey, defaultValue: true);
  }

  double getFontSize() {
    return Hive.box(Boxes.settings)
        .get(SettingsConstants.fontSizeKey, defaultValue: 26.0);
  }

  int getPageColor() {
    return Hive.box(Boxes.settings).get(
      SettingsConstants.pageColorKey,
      defaultValue: const Color(0xffffffff).value,
    );
  }

  int getPageTextColor() {
    return Hive.box(Boxes.settings).get(
      SettingsConstants.pageTextColorKey,
      defaultValue: const Color(0xff000000).value,
    );
  }

  bool getIsCustomColor() {
    bool isCustomColor = Hive.box(Boxes.settings).get(
      SettingsConstants.isCustomColorKey,
      defaultValue: false,
    );
    print('is Custome color: $isCustomColor');
    return isCustomColor;
  }

  Color getCustomBackgroundColor() {
    return Color(
      Hive.box(Boxes.settings).get(
        SettingsConstants.backColorKey,
        defaultValue: const Color(0xffffffff).value,
      ),
    );
  }

  Color getCustomTextColor() {
    return Color(
      Hive.box(Boxes.settings).get(
        SettingsConstants.pageTextColorKey,
        defaultValue: const Color(0xff000000).value,
      ),
    );
  }

  Bookmark? getBookmark({
    required int suraNumber,
    required int verseNumber,
  }) {
    return UserSyncService.instance.getBookmark(suraNumber, verseNumber);

  }

  bool isBookmarkAvailable(
      {required int suraNumber, required int verseNumber}) {
    return getBookmark(
          suraNumber: suraNumber,
          verseNumber: verseNumber,
        ) !=
        null;
  }

  void saveBookmark(
      {required int suraNumber,
      required int verseNumber,
      required int pageNumber,
      required int colorIndex}) {
    UserSyncService.instance.saveBookmark(
      Bookmark(
        id: null,
        pageNumber: pageNumber,
        suraNumber: suraNumber,
        colorIndex: colorIndex,
        verseNumber: verseNumber,
        createdAt: DateTime.now(),
        type: BookmarkType.verse,
      ),
    );
  }

  void deleteBookmark({
    required int suraNumber,
    required int verseNumber,
  }) {
    Hive.box<Bookmark>(Boxes.bookmarks).delete(
      suraNumber.toString().padLeft(3, "0") +
          verseNumber.toString().padLeft(3, "0"),
    );
  }

  void openVerseOptionsDailog({
    required int suraNumber,
    required int verseNumber,
    required int pageNumber,
  }) {
    Get.dialog(VerseOptionsDialog(
        suraNumber: suraNumber,
        verseNumber: verseNumber,
        pageNumber: pageNumber));
  }

  Future<HomeController> init() async {
    var lastPage = getLastPage();
    var fontScale = getQuranFontScale();
    quranPageController = await QuranPageController.create(
        initialPage: lastPage, initFontScale: fontScale);
    return this;
  }

  @override
  Future<void> onInit() async {
    try {
      QuranAudioService.currentVerse.listen(
        (value) {
          quranPageController.selectVerse(value);
        },
      );

      Hive.box(Boxes.settings).listenable(
          keys: [SettingsConstants.appDarkModeEnabledKey]).addListener(() {
        bool isDarkMode = Hive.box(Boxes.settings)
            .get(SettingsConstants.appDarkModeEnabledKey, defaultValue: false);
        // if (kDebugMode) {
        //   print("Listen to dark mode $isDarkMode");
        // }
        pageColor.value = isDarkMode ? null : Color(getPageColor());
      });

      pageColor.value = Get.isDarkMode ? null : Color(getPageColor());

      customTextColor.value = getCustomTextColor();
      customBackgroundColor.value = getCustomBackgroundColor();
      isCustomColor.value = getIsCustomColor();
      pageFontSize.value = getFontSize();
      currentPageNumber.value = getLastPage();
      documentDirectory.value = (await getApplicationDocumentsDirectory()).path;
      getWaqafatCounts();
      // currentPageNumber.listen((page) async {
      //   pageTdbrsCounts.value = [];
      //   pageTdbrsCounts.value = await SqlHelper.getPageAllTdbrsCount(page);
      // });
      changeClockVisibility(isClockVisible());
    } catch (e) {}

    surahs.value = await QuranDatabaseProvider.getAllSurahs();
    super.onInit();
  }

  @override
  void onReady() async {
    int lastPage = getLastPage();
    print("Last page: $lastPage");
    quranPageController.page = lastPage;
    quranPageController.jumpToPage(lastPage);
    // goToPage(lastPage - 1);
    changeThemeMode(isAppDarkModeEnabled());
    await checkForNewVersion();

    AdvancedInAppReview()
        .setMinDaysBeforeRemind(7)
        .setMinDaysAfterInstall(2)
        .setMinLaunchTimes(2)
        .setMinSecondsBeforeShowDialog(4)
        .monitor();

    super.onReady();
  }

  Future<void> checkForNewVersion() async {
    if (kDebugMode) {
      print("Checking for new version");
    }
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    var version = packageInfo.version;

    if (kDebugMode) {
      print("Current version: $version");
    }

    ApiHelper(Dio())
        .checkForNewVersion(
            version, GetPlatform.isAndroid ? "android" : "iphone")
        .then((response) {
      print(response);
      if (response.hasNewVersion) {
        Get.defaultDialog(
            title: "${"تحديث".tr} ${"التطبيق".tr}",
            content: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                response.updateMsg,
              ),
            ),
            textConfirm: "تحديث".tr,
            textCancel: "الغاء الامر".tr,
            cancelTextColor: Get.theme.primaryColor,
            confirmTextColor: Colors.white,
            buttonColor: Get.theme.primaryColor,
            // onCancel: () {
            //   throw Exception('test error');
            // },
            onConfirm: () {
              launchUrl(Uri.parse(Platform.isAndroid
                  ? "https://play.google.com/store/apps/details?id=com.tadarose_quran"
                  : "https://apple.co/2vNt58v"));
              Get.back();
            });
      }
    });
  }
}

class VersesTextWidget extends StatelessWidget {
  const VersesTextWidget({
    super.key,
    required this.verses,
    this.fontSize = 13,
  });

  final List<QuranVerse> verses;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    String versesText = "\u202E";
    for (var item in verses) {
      versesText +=
          "${item.verseWithDiac.trim()} ${item.verseNumber.toArabicNumber()} ";
    }
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Text(
        versesText,
        textAlign: TextAlign.justify,
        // textDirection: TextDirection.rtl,
        style: TextStyle(
          fontSize: fontSize,
          fontFamily: CommonConstants.hafsFontFamily,
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class CustomNumberPaicker extends StatefulWidget {
  const CustomNumberPaicker({
    Key? key,
    required this.minValue,
    required this.maxValue,
    required this.value,
    required this.onChanged,
  }) : super(key: key);
  final int minValue;
  final int maxValue;
  final int value;
  final Function(int value) onChanged;

  @override
  _CustomNumberPaickerState createState() => _CustomNumberPaickerState();
}

class _CustomNumberPaickerState extends State<CustomNumberPaicker> {
  int _value = 1;
  @override
  void initState() {
    _value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (_value < widget.minValue) {
      _value = widget.minValue;
    }
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: NumberPicker(
          itemHeight: 31,
        // itemWidth: 60,
          textStyle: const TextStyle(
            fontSize: 10,
          ),
          decoration: BoxDecoration(
            borderRadius: CommonStyles.borderRadius,
            border: Border.all(
              width: 1,
              color: Colors.grey.shade100,
            ),
          ),
          selectedTextStyle: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.theme.colorScheme.secondary,
          ),
          minValue: widget.minValue,
          maxValue: widget.maxValue,
          value: _value,
          onChanged: (value) {
            setState(() {
              _value = value;
              widget.onChanged(value);
            });
          }),
    );
  }
}

class BookmarkToggle extends StatelessWidget {
  const BookmarkToggle({
    Key? key,
    required this.color,
    this.isActive = false,
    required this.onToggle,
  }) : super(key: key);
  final Color color;
  final bool isActive;
  final void Function(bool value) onToggle;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        var value = !isActive;
        onToggle(value);
      },
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Icon(
          isActive
              ? QuranUIIcons.bookmarkFilled
              : QuranUIIcons.bookmarkOutllined,
          color: color,
        ),
      ),
    );
  }
}

class WidgetSpanWrapper extends StatefulWidget {
  const WidgetSpanWrapper({required Key key, required this.child})
      : super(key: key);

  final Widget child;

  @override
  _WidgetSpanWrapperState createState() => _WidgetSpanWrapperState();
}

class _WidgetSpanWrapperState extends State<WidgetSpanWrapper> {
  Offset offset = Offset.zero;

  void updateXOffset(double xOffset) {
    setState(() {
      offset = Offset(xOffset, 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Transform.translate(
      offset: offset,
      child: widget.child,
    );
  }
}
