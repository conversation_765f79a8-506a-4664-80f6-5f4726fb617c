import 'package:get/get.dart';
import 'package:tadars/helpers/boxes_helper.dart';

import '../models/hive/tafseer.dart';

class TafseerSearchController extends GetxController {
  TafseerSearchController();
  Rx<String> searchKey = "".obs;
  Rx<bool> loading = false.obs;
  Rx<List<Tafseer>?> tafseers = Rx<List<Tafseer>?>(null);

  var downloadedTafseers = BoxesHelper.getAllDownloadedTafseers();
  Future<void> search(String key) async {
    loading.value = true;
    tafseers.value = [];
    loading.value = false;
  }

  Future<List<Tafseer>> searchInTafseer(String name) async {
    return await BoxesHelper.searchInTafseer(searchKey.value, name);
  }

}
