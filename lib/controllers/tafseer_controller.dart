import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tadars/controllers/home_controller.dart';
import 'package:tadars/enums/loading_status.dart';
import 'package:tadars/helpers/api_helper.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/hive/quran_page.dart';
import 'package:tadars/models/hive/quran_tafseer.dart';
import 'package:tadars/models/view_models/download_vm.dart';
import 'package:tadars/services/network_service.dart';
import 'package:tadars/utils/common_functions.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/configs.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:archive/archive_io.dart';

import '../models/hive/tafseer.dart';
import '../utils/constants/custom_colors.dart';
import '../utils/enums.dart';

class TafseerController extends GetxController {
  static var instance = Get.find<TafseerController>();
  var status = LoadingStatus.init.obs;
  var needToCallGetTafseer = true.obs;
  var downloadingItems = <String, DownloadVM>{}.obs;
  Rx<QuranPage?> currentPage = Rx<QuranPage?>(null);
  final ApiHelper _apiHelper = ApiHelper(Dio());
  Rx<QuranTafseer?> tafseer = Rx<QuranTafseer?>(
    BoxesHelper.getTafseerById(
      BoxesHelper.getSettingTafseerId(),
    ),
  );
  RxList<Tafseer?> tafseers = RxList<Tafseer?>();

  @override
  void onInit() {
    currentPage.value =
        BoxesHelper.getPageById(HomeController.to.currentPage.value);
    if (HomeController.to.viewMode.value == ViewMode.meanings) {
      getTafseers(HomeController.to.currentPage.value);
    }
    HomeController.to.currentPage.listen((pageNumber) {
      currentPage.value = BoxesHelper.getPageById(pageNumber);
      if (HomeController.to.viewMode.value == ViewMode.meanings) {
        getTafseers(pageNumber);
      } else {
        needToCallGetTafseer(true);
      }
    });
    super.onInit();
  }

  Future<void> getTafseers(int pageNumber) async {
    needToCallGetTafseer(false);
    if (checkHiveTafseerExist()) {
      await getTafseerFromHive(pageNumber);
    } else {
      await getTafseerFromApi(pageNumber);
    }
  }

  bool checkHiveTafseerExist() {
    if (tafseer.value != null) {
      if (["sa3dy", "sraj"].contains(tafseer.value!.name)) {
        return true;
      }
      if (BoxesHelper.getTafseerFromDownloaded(tafseer.value!.name) != null) {
        return true;
      }
    }
    return false;
  }

  // get page tafseer from api
  Future<void> getTafseerFromApi(int page) async {
    await Future.delayed(const Duration(milliseconds: 50), () {
      status(LoadingStatus.loading);
    });
    NetworkServices.instance.checkConnectivity(() async {
      try {
        var tafseers =
            await _apiHelper.getPageTafseer(page, tafseer.value?.name ?? "");
        // print(_tafseers);

        this.tafseers.value = tafseers;
        // tafseers.refresh();
        status.value = LoadingStatus.success;
      } catch (e) {
        status.value = LoadingStatus.error;
        CommonFunctions.handleError(e);
        // CommonFunctions.showErrorMessage('عذراً.. انت غير متصل بالانترنت');
        if (kDebugMode) {
          print(e);
        }
      }
    }, () {
      status.value = LoadingStatus.error;

      // CommonFunctions.showErrorMessage('عذراً.. انت غير متصل بالانترنت');
    });
  }

  // get page tafseer form hive
  Future<void> getTafseerFromHive(int page) async {
    if (tafseer.value != null) {
      try {
        var tafseers = await BoxesHelper.getTafseersByPageNumber(
            tafseer.value!.name, page);
        // print(_tafseers);

        this.tafseers.value = tafseers;
        // tafseers.refresh();
      } catch (e) {
        status.value = LoadingStatus.error;
        if (kDebugMode) {
          print(e);
        }
      }
    }
  }

  void changeTafseer(int id) {
    int box = Hive.box(Boxes.settings)
        .get(SettingsConstants.tafseerId, defaultValue: 1);
    if (box != id) {
      Hive.box(Boxes.settings).put(SettingsConstants.tafseerId, id);
      tafseer.value = BoxesHelper.getTafseerById(id);
      getTafseers(HomeController.to.currentPage.value);
    }
  }

  Tafseer? getVerseTafseer(int verseNumber, int suraNumber) {
    // print("param $verseNumber $suraNumber");
    // tafseers.forEach((element) {
    //   // print("${element?.verseNumber} ${element?.suraNumber}");
    // });
    print("needToCallGetTafseer ${needToCallGetTafseer.value}");
    if (needToCallGetTafseer.value == true) {
      // print("inside getVerseTafseer");
      getTafseers(HomeController.to.currentPage.value);
    }
    return tafseers.firstWhere(
        (element) =>
            element?.verseNumber == verseNumber &&
            element?.suraNumber == suraNumber,
        orElse: () => null);
  }

  // show tafseer dialog
  void showTafseerDialog() {
    var dataKey = GlobalKey();
    var tafseers = BoxesHelper.getAllTafseers();
    var tafseerId = BoxesHelper.getSettingTafseerId();
    tafseers.sort((a, b) => a.downloaded ? -1 : 1);
    Get.dialog(FractionallySizedBox(
      widthFactor: 0.9,
      heightFactor: 0.8,
      child: Material(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: [
            Container(
              height: 50,
              padding: const EdgeInsets.all(8),
              width: double.maxFinite,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                color: CustomColors.primaryColor.withOpacity(0.3),
              ),
              child: NavigationToolbar(
                middle: Text(
                  "اختر التفسير",
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: Get.theme.textTheme.titleLarge?.fontSize,
                    color: CustomColors.primaryColor,
                  ),
                ),
                trailing: IconButton(
                  icon: Icon(
                    Icons.close,
                    color: CustomColors.primaryColor,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                ),
              ),
            ),
            Expanded(
                child: Scrollbar(
              thumbVisibility: true,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    for (var t in tafseers)
                      Column(
                        children: [
                          Divider(
                            height: 1,
                            color: CustomColors.primaryColor,
                          ),
                          ListTile(
                            key: t.id == tafseerId ? dataKey : null,
                            onTap: () {
                              Get.back();
                              TafseerController.instance.changeTafseer(t.id);
                            },
                            title: Text(
                              t.title,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: Get.textTheme.bodyLarge?.fontSize,
                              ),
                            ),
                            subtitle: Text(
                              t.author,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: t.id == tafseerId
                                ? Icon(
                                    Icons.check,
                                    color: CustomColors.primaryColor,
                                  )
                                : null,
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    ));

    SchedulerBinding.instance.addPostFrameCallback((_) {
      var cntx = dataKey.currentContext;
      // print(cntx);

      if (cntx != null) {
        Scrollable.ensureVisible(cntx);
      }
    });
  }

  // download tafseer
  void downloadTafseer(String name) {
    NetworkServices.instance.checkConnectivity(() async {
      // url
      var url = Configs.getTafseerDownloadUrl(name);
      // save path
      var downloadDirectory = await getApplicationDocumentsDirectory();
      // file path
      var filePath = "${downloadDirectory.path}/$name.zip";
      downloadingItems[name] = DownloadVM(progress: 0, total: 100);
      downloadingItems.refresh();
      Dio().download(
        url,
        filePath,
        onReceiveProgress: (progress, total) {
          // if (kDebugMode) {
          //   print("$progress $total");
          // }
          downloadingItems[name] = DownloadVM(
            progress: progress.toDouble(),
            total: total.toDouble(),
          );
          downloadingItems.refresh();
        },
        deleteOnError: true,
      ).then((value) async {
        // if (kDebugMode) {
        //   print("download complete");
        // }
        downloadingItems.remove(name);
        downloadingItems.refresh();
        var documentDirectory = await getApplicationDocumentsDirectory();
        // unzip
        extractFileToDisk(filePath, documentDirectory.path);
        BoxesHelper.addTafseerToDownloaded(name);
      });
    });
  }

  @override
  void onReady() {
    status.listen((status) {
      // print("listen status $status");
      bool canPop = true;
      if (status == LoadingStatus.loading) {
        canPop = false;
        Get.defaultDialog(
          title: "تحميل التفسير",
          content: WillPopScope(
            onWillPop: () {
              return Future.value(canPop);
            },
            child: const Column(
              children: [
                SizedBox(
                  height: 100,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                Text(
                  "جاري جلب تفسير آيات الصفحة...",
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        );
      } else {
        canPop = true;
        Get.back();
      }
    });
    super.onReady();
  }
}
