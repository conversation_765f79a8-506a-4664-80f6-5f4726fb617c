import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:sqlbrite/sqlbrite.dart';
import 'package:tadars/services/download_tdrs_db_service.dart';
import 'package:tadars/services/language_import_service.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:tadars/view/widgets/language_loading_dialog.dart';
import 'package:path/path.dart';

class LanguageSelectionController extends GetxController {

@override
  void onInit() {
    isLoading.listen((value) {
      if (value) {
        LanguageLoadingDialog.show(this);
      } else {
        LanguageLoadingDialog.close();
      }
    });
    super.onInit();
  }
  // Observable variables
  final RxString selectedLanguage = 'ar'.obs;
  final RxBool isLoading = false.obs;
  final RxBool isDownloading = false.obs;
  final RxBool isExtracting = false.obs;
  final RxBool isProcessing = false.obs;
  final RxDouble downloadProgress = 0.0.obs;
  final RxInt totalFiles = 0.obs;
  final RxInt processedFiles = 0.obs;
  final RxString currentStep = ''.obs;
  final RxString currentFile = ''.obs;
  final RxString errorMessage = ''.obs;
  final RxInt totalFileSize = 0.obs; // Total file size in bytes
  final RxInt downloadedBytes = 0.obs; // Downloaded bytes

  void setSelectedLanguage(String language) {
    selectedLanguage.value = language;
  }

  Future<bool> _checkTdrsdbExists() async {
    var databasesPath = await getDatabasesPath();
    var path = join(databasesPath, "tdrs_db.sqlite");
    var exists = await databaseExists(path);

    return exists;
  }

  Future<void> _downloadAndProcessLanguageFiles() async {
  
    final LanguageImportService languageImportService = LanguageImportService();

    // Download and process language files with progress updates
    await languageImportService.downloadAndProcessLanguageFiles(
      selectedLanguage.value,
      onDownloadStart: () {
        isDownloading.value = true;
        currentStep.value = 'downloading';
      },
      onDownloadProgress: (received, total) {
        if (total != -1) {
          downloadProgress.value = received / total;
          totalFileSize.value = total;
          downloadedBytes.value = received;
        }
      },
      onDownloadComplete: () {
        isDownloading.value = false;
        downloadProgress.value = 1.0;
      },
      onExtractionStart: () {
        isExtracting.value = true;
        currentStep.value = 'extracting';
      },
      onExtractionComplete: () {
        isExtracting.value = false;
      },
      onProcessingStart: (fileCount) {
        isProcessing.value = true;
        currentStep.value = 'processing';
        totalFiles.value = fileCount;
      },
      onFileProcessed: (fileName, current, total) {
        processedFiles.value = current;
        currentFile.value = fileName;
      },
      onProcessingComplete: () {
        isProcessing.value = false;
        currentStep.value = 'completed';
      },
    );
  }

  Future<void> _getDatabaseForLocale() async {
    DownloadTdrsDbService downloadTdrsDbService = DownloadTdrsDbService();

    // Download and process language files with progress updates
    await downloadTdrsDbService.getDatabaseForLocale(
      selectedLanguage.value,
      onDownloadStart: () {
        isDownloading.value = true;
        currentStep.value = 'downloading';
      },
      onDownloadProgress: (received, total) {
        if (total != -1) {
          downloadProgress.value = received / total;
          totalFileSize.value = total;
          downloadedBytes.value = received;
        }
      },
      onDownloadComplete: () {
        isDownloading.value = false;
        downloadProgress.value = 1.0;
      },
      onExtractionStart: () {
        isExtracting.value = true;
        currentStep.value = 'extracting';
      },
      onExtractionComplete: () {
        isExtracting.value = false;
      },
      // onProcessingStart: (fileCount) {
      //   isProcessing.value = true;
      //   currentStep.value = 'processing';
      //   totalFiles.value = fileCount;
      // },
      // onFileProcessed: (fileName, current, total) {
      //   processedFiles.value = current;
      //   currentFile.value = fileName;
      // },
      // onProcessingComplete: () {
      //   isProcessing.value = false;
      //   currentStep.value = 'completed';
      // },
    );
  }

  Future<void> processLanguageSelection() async {
    try {
      isLoading.value = true;

      errorMessage.value = '';

      if (await _checkTdrsdbExists()) {
        await _downloadAndProcessLanguageFiles();
      } else {
        await _getDatabaseForLocale();
      }
      // // Save the selected language
      // await Hive.box(Boxes.settings).put(
      //   SettingsConstants.appLocaleCodeKey,
      //   selectedLanguage.value,
      // );
      // // Mark that language has been selected
      // await Hive.box(Boxes.settings).put(
      //   SettingsConstants.firstTimeLanguageSelectedKey,
      //   true,
      // );

    
      // Update locale
      Get.updateLocale(Locale(selectedLanguage.value));

      // Navigate to home page
      Get.offAllNamed(Routes.homePage);
    } catch (e) {
      errorMessage.value = 'Failed to download language files. Please try again.';
      isLoading.value = false;
      isDownloading.value = false;
      isExtracting.value = false;
      isProcessing.value = false;

      Get.snackbar(
        'Error',
        'Failed to download language files. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  String get downloadProgressText {
    if (downloadProgress.value <= 0) return '0%';
    return '${(downloadProgress.value * 100).toStringAsFixed(0)}%';
  }

  String get processingProgressText {
    if (totalFiles.value <= 0) return '0%';
    return '${(processedFiles.value / totalFiles.value * 100).toStringAsFixed(0)}%';
  }

  String get downloadSizeText {
    if (downloadProgress.value <= 0 || totalFileSize.value <= 0) return '';

    // Convert bytes to MB for display
    final totalSizeMB = totalFileSize.value / (1024 * 1024);
    final downloadedSizeMB = downloadedBytes.value / (1024 * 1024);

    return '${downloadedSizeMB.toStringAsFixed(1)}/${totalSizeMB.toStringAsFixed(1)} MB';
  }

 

  String get currentStepText {
    switch (currentStep.value) {
      case 'downloading':
        return 'Downloading language files...';
      case 'extracting':
        return 'Extracting files...';
      case 'processing':
        return 'Processing files...';
      case 'completed':
        return 'Completed!';
      default:
        return 'Preparing...';
    }
  }

  String get currentStepTextArabic {
    switch (currentStep.value) {
      case 'downloading':
        return 'جاري تحميل ملفات اللغة...';
      case 'extracting':
        return 'جاري استخراج الملفات...';
      case 'processing':
        return 'جاري معالجة الملفات...';
      case 'completed':
        return 'اكتمل!';
      default:
        return 'جاري التحضير...';
    }
  }
}
