import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:jiffy/jiffy.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tadars/helpers/boxes_helper.dart';
import 'package:tadars/models/view_models/sections_with_new_data_vm.dart';
import 'package:tadars/utils/constants/boxes.dart';
import 'package:tadars/utils/constants/routes.dart';
import 'package:tadars/utils/constants/settings.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../initializing.dart';

class AppController extends GetxController {
  SectionsWithNewDataVm sectionsWithNewDataVm = BoxesHelper.getLastIds();
  SectionsWithNewDataVm sectionsWithNewDataResponse = SectionsWithNewDataVm();

  // Map<String, Box> boxes = {
  //   Boxes.tdbrTadaborCategories:
  //       Hive.box<TdbrTadaborCategory>(Boxes.tdbrTadaborCategories),
  //   Boxes.tdbrComparables: Hive.box<TdbrComparable>(Boxes.tdbrComparables),
  //   Boxes.tdbrConsiders: Hive.box<TdbrConsider>(Boxes.tdbrConsiders),
  //   Boxes.tdbrEloquences: Hive.box<TdbrEloquence>(Boxes.tdbrEloquences),
  //   Boxes.tdbrLinks: Hive.box<TdbrLink>(Boxes.tdbrLinks),
  //   Boxes.tdbrMedia: Hive.box<TdbrMedia>(Boxes.tdbrMedia),
  //   Boxes.tdbrPrays: Hive.box<TdbrPray>(Boxes.tdbrPrays),
  //   Boxes.tdbrQuestions: Hive.box<TdbrQuestion>(Boxes.tdbrQuestions),
  //   Boxes.tdbrRules: Hive.box<TdbrRule>(Boxes.tdbrRules),
  //   Boxes.tdbrSources: Hive.box<TdbrSource>(Boxes.tdbrSources),
  //   Boxes.tdbrSuggests: Hive.box<TdbrSuggest>(Boxes.tdbrSuggests),
  //   Boxes.tdbrTadabors: Hive.box<TdbrTadabor>(Boxes.tdbrTadabors),
  //   'tdbr_ayat': Hive.box<TdbrVerse>(Boxes.tdbrVerses),
  // };

  @override
  void onInit() async {
    init();

    super.onInit();
  }

  Future<void> init() async {

    var watch = Stopwatch()..start();
    var documentDirectory = await getApplicationDocumentsDirectory();

    await initBoxes(documentDirectory.path);
    registerAdapters();
    await openBoxes();
    // await initTdrsDB();
    // await initReciterDB();

    await initServices();
    setupWakeUp();

    if (kDebugMode) {
      print(Get.deviceLocale);
    }

    // Check if first-time language selection has been completed
    bool isFirstTimeLanguageSelected =
        BoxesHelper.isFirstTimeLanguageSelected();

    // await Future.delayed(const Duration(seconds: 3));
    Jiffy.setLocale(Get.locale!.languageCode);
    // print(BoxesHelper.getLastLikeId().toString() + " Last LIKE ID");
    // print(BoxesHelper.getLastUserId().toString() + " Last User ID");
    // sync();

    // SyncService.instance.sync();
    watch.stop();

    print("init service took ${watch.elapsedMilliseconds}");

    // Navigate to language selection page if first-time setup hasn't been completed
    // if (!isFirstTimeLanguageSelected) {
    Get.offAllNamed(Routes.languageSelectionPage);
    // } else {
    //   Get.offAllNamed(Routes.homePage);
    // }
  }

  setupWakeUp() {
    bool wakelockEnabled = Hive.box(Boxes.settings)
        .get(SettingsConstants.wakelockEnableKey, defaultValue: true);
    WakelockPlus.toggle(enable: wakelockEnabled);
  }
}
