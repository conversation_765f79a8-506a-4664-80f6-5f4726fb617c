{"buildFiles": ["C:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\tadars\\android\\app\\.cxx\\RelWithDebInfo\\1q43i6r6\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\tadars\\android\\app\\.cxx\\RelWithDebInfo\\1q43i6r6\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}