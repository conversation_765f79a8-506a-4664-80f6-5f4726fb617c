                        -HC:\android\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\android\sdk\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\android\sdk\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\android\sdk\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\flutter\tadars\build\app\intermediates\cxx\Debug\4l5b4f6c\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\flutter\tadars\build\app\intermediates\cxx\Debug\4l5b4f6c\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\Desktop\flutter\tadars\android\app\.cxx\Debug\4l5b4f6c\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2