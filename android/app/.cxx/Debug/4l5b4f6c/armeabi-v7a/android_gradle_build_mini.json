{"buildFiles": ["C:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\tadars\\android\\app\\.cxx\\Debug\\4l5b4f6c\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\tadars\\android\\app\\.cxx\\Debug\\4l5b4f6c\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}